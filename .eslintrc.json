{"root": true, "env": {"node": true, "jest": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"project": "tsconfig.json", "sourceType": "module"}, "ignorePatterns": ["jest.config.ts", "**/**.spec.ts", "**/**.test.ts"], "plugins": ["@typescript-eslint", "unused-imports"], "rules": {"@typescript-eslint/interface-name-prefix": "off", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-namespace": "off", "@typescript-eslint/no-var-requires": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-non-null-assertion": "off", "unused-imports/no-unused-imports": "warn", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "prettier/prettier": "warn"}}