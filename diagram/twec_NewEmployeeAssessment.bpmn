<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0xtc4fd" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="TWEC_NewEmployeeAssessment" name="新進人員考核" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_10um5rl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_08nzfl7</bpmn:incoming>
      <bpmn:incoming>Flow_0xrhr4i</bpmn:incoming>
      <bpmn:incoming>Flow_10um5rl</bpmn:incoming>
      <bpmn:incoming>Flow_0qztyds</bpmn:incoming>
      <bpmn:outgoing>Flow_1j6lrkb</bpmn:outgoing>
      <bpmn:outgoing>Flow_05g7mdv</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_05g7mdv</bpmn:incoming>
      <bpmn:incoming>Flow_1yhkt40</bpmn:incoming>
      <bpmn:outgoing>Flow_1idas1g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="EndEvent_1">
      <bpmn:incoming>Flow_1idas1g</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1idas1g" sourceRef="Activity_End" targetRef="EndEvent_1" />
    <bpmn:sequenceFlow id="Flow_10um5rl" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_1j6lrkb" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step2" name="部門主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1j6lrkb</bpmn:incoming>
      <bpmn:outgoing>Flow_1p6n09f</bpmn:outgoing>
      <bpmn:outgoing>Flow_08nzfl7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1p6n09f" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_05g7mdv" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08nzfl7" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="總經理">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">prefix_form_specify_user</camunda:inputParameter>
          <camunda:inputParameter name="allow_add_countersign">true</camunda:inputParameter>
          <camunda:inputParameter name="additional_docs_type">initiator</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1jenkzw</bpmn:incoming>
      <bpmn:outgoing>Flow_0xrhr4i</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yhkt40</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0xrhr4i" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1yhkt40" name="核准" sourceRef="Activity_Step4" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="HR 主管">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">prefix_form_specify_user</camunda:inputParameter>
          <camunda:inputParameter name="allow_add_countersign">true</camunda:inputParameter>
          <camunda:inputParameter name="additional_docs_type">initiator</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1p6n09f</bpmn:incoming>
      <bpmn:outgoing>Flow_1jenkzw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qztyds</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1jenkzw" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0qztyds" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TWEC_NewEmployeeAssessment">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="182" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="290" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ayks9o_di" bpmnElement="Activity_Step2">
        <dc:Bounds x="440" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0hu135l" bpmnElement="Activity_Step3">
        <dc:Bounds x="600" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ts2638" bpmnElement="Activity_Step4">
        <dc:Bounds x="750" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="910" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_128mpal_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="1072" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_10um5rl_di" bpmnElement="Flow_10um5rl">
        <di:waypoint x="218" y="120" />
        <di:waypoint x="290" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1j6lrkb_di" bpmnElement="Flow_1j6lrkb">
        <di:waypoint x="390" y="120" />
        <di:waypoint x="440" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="407" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1p6n09f_di" bpmnElement="Flow_1p6n09f">
        <di:waypoint x="540" y="120" />
        <di:waypoint x="600" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="559" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05g7mdv_di" bpmnElement="Flow_05g7mdv">
        <di:waypoint x="320" y="160" />
        <di:waypoint x="320" y="240" />
        <di:waypoint x="960" y="240" />
        <di:waypoint x="960" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="289" y="203" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08nzfl7_di" bpmnElement="Flow_08nzfl7">
        <di:waypoint x="490" y="160" />
        <di:waypoint x="490" y="220" />
        <di:waypoint x="350" y="220" />
        <di:waypoint x="350" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="459" y="183" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xrhr4i_di" bpmnElement="Flow_0xrhr4i">
        <di:waypoint x="800" y="160" />
        <di:waypoint x="800" y="220" />
        <di:waypoint x="350" y="220" />
        <di:waypoint x="350" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="183" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qztyds_di" bpmnElement="Flow_0qztyds">
        <di:waypoint x="650" y="160" />
        <di:waypoint x="650" y="220" />
        <di:waypoint x="350" y="220" />
        <di:waypoint x="350" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="619" y="186" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jenkzw_di" bpmnElement="Flow_1jenkzw">
        <di:waypoint x="700" y="120" />
        <di:waypoint x="750" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="714" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yhkt40_di" bpmnElement="Flow_1yhkt40">
        <di:waypoint x="850" y="120" />
        <di:waypoint x="910" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="869" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1idas1g_di" bpmnElement="Flow_1idas1g">
        <di:waypoint x="1010" y="120" />
        <di:waypoint x="1072" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
