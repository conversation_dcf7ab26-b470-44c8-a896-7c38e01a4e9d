<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0og4ys1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="CN_QuotationOfficialSeal" name="报价单合约用印" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0at6wdn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="业务">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0f0p54t</bpmn:incoming>
      <bpmn:outgoing>Flow_1sklbmc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1q5hbke</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_Finished" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1hdls6f</bpmn:incoming>
      <bpmn:incoming>Flow_1dkalj7</bpmn:incoming>
      <bpmn:incoming>Flow_0nm7ihb</bpmn:incoming>
      <bpmn:incoming>Flow_1q5hbke</bpmn:incoming>
      <bpmn:incoming>Flow_1rdrybp</bpmn:incoming>
      <bpmn:incoming>Flow_17cqjo7</bpmn:incoming>
      <bpmn:incoming>Flow_1wj65rc</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdhhmm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Activity_Step2" name="办事处主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1sklbmc</bpmn:incoming>
      <bpmn:outgoing>Flow_0oc8paj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0nm7ihb</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1sklbmc" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateThrowEvent id="Event_02b5csr">
      <bpmn:incoming>Flow_1gdhhmm</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1gdhhmm" sourceRef="Activity_Finished" targetRef="Event_02b5csr" />
    <bpmn:serviceTask id="Activity_Start" name="开始" camunda:type="external" camunda:topic="common:task:start">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0at6wdn</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0p54t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0at6wdn" sourceRef="StartEvent_1" targetRef="Activity_Start" />
    <bpmn:sequenceFlow id="Flow_0f0p54t" sourceRef="Activity_Start" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_0oc8paj" name="核准" sourceRef="Activity_Step2" targetRef="Gateway_1gd7udf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1q5hbke" name="驳回" sourceRef="Activity_Step1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0nm7ihb" name="驳回" sourceRef="Activity_Step2" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1dkalj7" name="驳回" sourceRef="Activity_Step4" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="事业部主管">
      <bpmn:incoming>Flow_0kklb56</bpmn:incoming>
      <bpmn:outgoing>Flow_1dkalj7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m21ptg</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1m21ptg" name="核准" sourceRef="Activity_Step4" targetRef="Gateway_083mr5c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step5" name="总经理">
      <bpmn:incoming>Flow_1h59ttq</bpmn:incoming>
      <bpmn:outgoing>Flow_1hdls6f</bpmn:outgoing>
      <bpmn:outgoing>Flow_0t3l1tl</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1hdls6f" name="驳回" sourceRef="Activity_Step5" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0t3l1tl" name="核准" sourceRef="Activity_Step5" targetRef="Activity_Step6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0kx1y0c">
      <bpmn:incoming>Flow_06lqdby</bpmn:incoming>
      <bpmn:incoming>Flow_0hnc2cl</bpmn:incoming>
      <bpmn:outgoing>Flow_0kklb56</bpmn:outgoing>
      <bpmn:outgoing>Flow_018os3s</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_083mr5c">
      <bpmn:incoming>Flow_1m21ptg</bpmn:incoming>
      <bpmn:incoming>Flow_018os3s</bpmn:incoming>
      <bpmn:outgoing>Flow_1h59ttq</bpmn:outgoing>
      <bpmn:outgoing>Flow_07eqzry</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1h59ttq" sourceRef="Gateway_083mr5c" targetRef="Activity_Step5" />
    <bpmn:sequenceFlow id="Flow_0kklb56" sourceRef="Gateway_0kx1y0c" targetRef="Activity_Step4" />
    <bpmn:sequenceFlow id="Flow_018os3s" sourceRef="Gateway_0kx1y0c" targetRef="Gateway_083mr5c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Activity_Step3_gateway == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07eqzry" sourceRef="Gateway_083mr5c" targetRef="Activity_Step6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Activity_Step4_gateway == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step6" name="RA">
      <bpmn:incoming>Flow_07eqzry</bpmn:incoming>
      <bpmn:incoming>Flow_0t3l1tl</bpmn:incoming>
      <bpmn:outgoing>Flow_1rdrybp</bpmn:outgoing>
      <bpmn:outgoing>Flow_17cqjo7</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1rdrybp" name="核准" sourceRef="Activity_Step6" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_17cqjo7" name="驳回" sourceRef="Activity_Step6" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1gd7udf">
      <bpmn:incoming>Flow_0oc8paj</bpmn:incoming>
      <bpmn:outgoing>Flow_06lqdby</bpmn:outgoing>
      <bpmn:outgoing>Flow_11bmj6p</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_06lqdby" sourceRef="Gateway_1gd7udf" targetRef="Gateway_0kx1y0c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Activity_Step2_gateway == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="大区主管">
      <bpmn:incoming>Flow_11bmj6p</bpmn:incoming>
      <bpmn:outgoing>Flow_0hnc2cl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1wj65rc</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_11bmj6p" sourceRef="Gateway_1gd7udf" targetRef="Activity_Step3" />
    <bpmn:sequenceFlow id="Flow_0hnc2cl" name="核准" sourceRef="Activity_Step3" targetRef="Gateway_0kx1y0c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wj65rc" name="驳回" sourceRef="Activity_Step3" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:error id="Error_1ehy8wa" name="Error_2ot71qf" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CN_QuotationOfficialSeal">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="153" y="149" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="390" y="127" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_Finished">
        <dc:Bounds x="1550" y="127" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ma9eup_di" bpmnElement="Activity_Step2">
        <dc:Bounds x="551" y="127" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02b5csr_di" bpmnElement="Event_02b5csr">
        <dc:Bounds x="1692" y="149" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0o1w0z6" bpmnElement="Activity_Start">
        <dc:Bounds x="241" y="127" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16n3l3a_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="1020" y="127" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0tnpyxk" bpmnElement="Activity_Step5">
        <dc:Bounds x="1250" y="127" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0kx1y0c_di" bpmnElement="Gateway_0kx1y0c" isMarkerVisible="true">
        <dc:Bounds x="935" y="142" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_083mr5c_di" bpmnElement="Gateway_083mr5c" isMarkerVisible="true">
        <dc:Bounds x="1165" y="142" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1p41dpc" bpmnElement="Activity_Step6">
        <dc:Bounds x="1400" y="127" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1gd7udf_di" bpmnElement="Gateway_1gd7udf" isMarkerVisible="true">
        <dc:Bounds x="704" y="142" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pwf7gp" bpmnElement="Activity_Step3">
        <dc:Bounds x="790" y="127" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1sklbmc_di" bpmnElement="Flow_1sklbmc">
        <di:waypoint x="490" y="167" />
        <di:waypoint x="551" y="167" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="510" y="149" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gdhhmm_di" bpmnElement="Flow_1gdhhmm">
        <di:waypoint x="1650" y="167" />
        <di:waypoint x="1692" y="167" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0at6wdn_di" bpmnElement="Flow_0at6wdn">
        <di:waypoint x="189" y="167" />
        <di:waypoint x="241" y="167" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f0p54t_di" bpmnElement="Flow_0f0p54t">
        <di:waypoint x="341" y="167" />
        <di:waypoint x="390" y="167" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oc8paj_di" bpmnElement="Flow_0oc8paj">
        <di:waypoint x="651" y="167" />
        <di:waypoint x="704" y="167" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="667" y="149" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q5hbke_di" bpmnElement="Flow_1q5hbke">
        <di:waypoint x="440" y="207" />
        <di:waypoint x="440" y="280" />
        <di:waypoint x="1600" y="280" />
        <di:waypoint x="1600" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="449" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nm7ihb_di" bpmnElement="Flow_0nm7ihb">
        <di:waypoint x="601" y="207" />
        <di:waypoint x="601" y="280" />
        <di:waypoint x="1600" y="280" />
        <di:waypoint x="1600" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="620" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dkalj7_di" bpmnElement="Flow_1dkalj7">
        <di:waypoint x="1070" y="207" />
        <di:waypoint x="1070" y="280" />
        <di:waypoint x="1600" y="280" />
        <di:waypoint x="1600" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1089" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m21ptg_di" bpmnElement="Flow_1m21ptg">
        <di:waypoint x="1120" y="167" />
        <di:waypoint x="1165" y="167" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1130" y="149" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hdls6f_di" bpmnElement="Flow_1hdls6f">
        <di:waypoint x="1300" y="207" />
        <di:waypoint x="1300" y="280" />
        <di:waypoint x="1600" y="280" />
        <di:waypoint x="1600" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1309" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t3l1tl_di" bpmnElement="Flow_0t3l1tl">
        <di:waypoint x="1350" y="167" />
        <di:waypoint x="1400" y="167" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1362" y="149" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h59ttq_di" bpmnElement="Flow_1h59ttq">
        <di:waypoint x="1215" y="167" />
        <di:waypoint x="1250" y="167" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kklb56_di" bpmnElement="Flow_0kklb56">
        <di:waypoint x="985" y="167" />
        <di:waypoint x="1020" y="167" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_018os3s_di" bpmnElement="Flow_018os3s">
        <di:waypoint x="960" y="142" />
        <di:waypoint x="960" y="80" />
        <di:waypoint x="1170" y="80" />
        <di:waypoint x="1170" y="162" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07eqzry_di" bpmnElement="Flow_07eqzry">
        <di:waypoint x="1190" y="142" />
        <di:waypoint x="1190" y="80" />
        <di:waypoint x="1450" y="80" />
        <di:waypoint x="1450" y="127" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rdrybp_di" bpmnElement="Flow_1rdrybp">
        <di:waypoint x="1500" y="167" />
        <di:waypoint x="1550" y="167" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1512" y="149" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17cqjo7_di" bpmnElement="Flow_17cqjo7">
        <di:waypoint x="1450" y="207" />
        <di:waypoint x="1450" y="280" />
        <di:waypoint x="1600" y="280" />
        <di:waypoint x="1600" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1459" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06lqdby_di" bpmnElement="Flow_06lqdby">
        <di:waypoint x="729" y="142" />
        <di:waypoint x="729" y="80" />
        <di:waypoint x="950" y="80" />
        <di:waypoint x="950" y="152" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11bmj6p_di" bpmnElement="Flow_11bmj6p">
        <di:waypoint x="754" y="167" />
        <di:waypoint x="790" y="167" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hnc2cl_di" bpmnElement="Flow_0hnc2cl">
        <di:waypoint x="890" y="167" />
        <di:waypoint x="935" y="167" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="902" y="149" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wj65rc_di" bpmnElement="Flow_1wj65rc">
        <di:waypoint x="840" y="207" />
        <di:waypoint x="840" y="280" />
        <di:waypoint x="1600" y="280" />
        <di:waypoint x="1600" y="207" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="849" y="243" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
