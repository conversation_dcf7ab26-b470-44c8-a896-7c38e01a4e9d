<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0xtc4fd" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="TW_StoreMemberFeedback" name="門市會員反應" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_10um5rl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_10um5rl</bpmn:incoming>
      <bpmn:incoming>Flow_0ttz5td</bpmn:incoming>
      <bpmn:outgoing>Flow_184zwle</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rl8udi</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0hpnmq9</bpmn:incoming>
      <bpmn:incoming>Flow_184zwle</bpmn:incoming>
      <bpmn:outgoing>Flow_1idas1g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="EndEvent_1">
      <bpmn:incoming>Flow_1idas1g</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1idas1g" sourceRef="Activity_End" targetRef="EndEvent_1" />
    <bpmn:sequenceFlow id="Flow_0hpnmq9" name="核准" sourceRef="Activity_Step6" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10um5rl" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_184zwle" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step6" name="填單單位結案">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0rgxyym</bpmn:incoming>
      <bpmn:outgoing>Flow_0hpnmq9</bpmn:outgoing>
      <bpmn:outgoing>Flow_10y1dl3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_10y1dl3" name="駁回" sourceRef="Activity_Step6" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step5" name="部門主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_04dm7lc</bpmn:incoming>
      <bpmn:incoming>Flow_0dwudw1</bpmn:incoming>
      <bpmn:outgoing>Flow_0rgxyym</bpmn:outgoing>
      <bpmn:outgoing>Flow_016gc1k</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step2" name="填單人員">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_10y1dl3</bpmn:incoming>
      <bpmn:incoming>Flow_016gc1k</bpmn:incoming>
      <bpmn:incoming>Flow_1kymz6i</bpmn:incoming>
      <bpmn:incoming>Flow_07d4xcj</bpmn:incoming>
      <bpmn:incoming>Flow_0rl8udi</bpmn:incoming>
      <bpmn:outgoing>Flow_0ttz5td</bpmn:outgoing>
      <bpmn:outgoing>Flow_10vcane</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0rgxyym" name="核准" sourceRef="Activity_Step5" targetRef="Activity_Step6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_016gc1k" name="駁回" sourceRef="Activity_Step5" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ttz5td" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rl8udi" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="直屬主管">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="allow_skip_type">specify_user</camunda:inputParameter>
          <camunda:inputParameter name="allow_skip_user_code">L0528-W</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_10vcane</bpmn:incoming>
      <bpmn:outgoing>Flow_1kymz6i</bpmn:outgoing>
      <bpmn:outgoing>Flow_0lt46k6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1kymz6i" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10vcane" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_14v3g9l">
      <bpmn:incoming>Flow_0lt46k6</bpmn:incoming>
      <bpmn:outgoing>Flow_10ejgbw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0dwudw1</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0lt46k6" name="核准" sourceRef="Activity_Step3" targetRef="Gateway_14v3g9l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="相關單位會簽">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_10ejgbw</bpmn:incoming>
      <bpmn:outgoing>Flow_04dm7lc</bpmn:outgoing>
      <bpmn:outgoing>Flow_07d4xcj</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="form_data_countersigners" camunda:elementVariable="user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${countersigners_reject_count &gt; 0 || nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_04dm7lc" name="核准" sourceRef="Activity_Step4" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07d4xcj" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${countersigners_reject_count &gt; 0}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10ejgbw" name="需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "Y"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0dwudw1" name="不需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "N"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TW_StoreMemberFeedback">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="250" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="860" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_128mpal_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="892" y="452" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1i7b1kf_di" bpmnElement="Activity_Step6">
        <dc:Bounds x="1190" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1yqnrwz" bpmnElement="Activity_Step5">
        <dc:Bounds x="1030" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_18j7hx5_di" bpmnElement="Activity_Step2">
        <dc:Bounds x="420" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step3">
        <dc:Bounds x="590" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14v3g9l_di" bpmnElement="Gateway_14v3g9l" isMarkerVisible="true">
        <dc:Bounds x="755" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16o97v6_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="860" y="180" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1idas1g_di" bpmnElement="Flow_1idas1g">
        <di:waypoint x="910" y="420" />
        <di:waypoint x="910" y="452" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hpnmq9_di" bpmnElement="Flow_0hpnmq9">
        <di:waypoint x="1270" y="260" />
        <di:waypoint x="1270" y="380" />
        <di:waypoint x="960" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1279" y="306" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10um5rl_di" bpmnElement="Flow_10um5rl">
        <di:waypoint x="188" y="220" />
        <di:waypoint x="250" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_184zwle_di" bpmnElement="Flow_184zwle">
        <di:waypoint x="270" y="260" />
        <di:waypoint x="270" y="380" />
        <di:waypoint x="860" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="279" y="286" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10y1dl3_di" bpmnElement="Flow_10y1dl3">
        <di:waypoint x="1220" y="260" />
        <di:waypoint x="1220" y="310" />
        <di:waypoint x="490" y="310" />
        <di:waypoint x="490" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1229" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rgxyym_di" bpmnElement="Flow_0rgxyym">
        <di:waypoint x="1130" y="220" />
        <di:waypoint x="1190" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1150" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_016gc1k_di" bpmnElement="Flow_016gc1k">
        <di:waypoint x="1080" y="260" />
        <di:waypoint x="1080" y="310" />
        <di:waypoint x="490" y="310" />
        <di:waypoint x="490" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1089" y="286" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ttz5td_di" bpmnElement="Flow_0ttz5td">
        <di:waypoint x="470" y="260" />
        <di:waypoint x="470" y="310" />
        <di:waypoint x="320" y="310" />
        <di:waypoint x="320" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="442" y="286" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rl8udi_di" bpmnElement="Flow_0rl8udi">
        <di:waypoint x="350" y="220" />
        <di:waypoint x="420" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="375" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kymz6i_di" bpmnElement="Flow_1kymz6i">
        <di:waypoint x="640" y="260" />
        <di:waypoint x="640" y="310" />
        <di:waypoint x="490" y="310" />
        <di:waypoint x="490" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="649" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10vcane_di" bpmnElement="Flow_10vcane">
        <di:waypoint x="520" y="220" />
        <di:waypoint x="590" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="546" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lt46k6_di" bpmnElement="Flow_0lt46k6">
        <di:waypoint x="690" y="220" />
        <di:waypoint x="755" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="712" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04dm7lc_di" bpmnElement="Flow_04dm7lc">
        <di:waypoint x="960" y="220" />
        <di:waypoint x="1030" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="984" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07d4xcj_di" bpmnElement="Flow_07d4xcj">
        <di:waypoint x="910" y="260" />
        <di:waypoint x="910" y="310" />
        <di:waypoint x="490" y="310" />
        <di:waypoint x="490" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="924" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10ejgbw_di" bpmnElement="Flow_10ejgbw">
        <di:waypoint x="805" y="220" />
        <di:waypoint x="860" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="813" y="202" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dwudw1_di" bpmnElement="Flow_0dwudw1">
        <di:waypoint x="780" y="195" />
        <di:waypoint x="780" y="130" />
        <di:waypoint x="1080" y="130" />
        <di:waypoint x="1080" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="909" y="112" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
