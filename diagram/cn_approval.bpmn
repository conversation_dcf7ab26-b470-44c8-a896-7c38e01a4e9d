<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0xtc4fd" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="CN_Approval" name="簽呈" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_10um5rl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起单人确认">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_10um5rl</bpmn:incoming>
      <bpmn:incoming>Flow_1kymz6i</bpmn:incoming>
      <bpmn:incoming>Flow_07d4xcj</bpmn:incoming>
      <bpmn:incoming>Flow_10y1dl3</bpmn:incoming>
      <bpmn:incoming>Flow_1jh19o9</bpmn:incoming>
      <bpmn:incoming>Flow_18r6nzq</bpmn:incoming>
      <bpmn:incoming>Flow_00nk7xh</bpmn:incoming>
      <bpmn:outgoing>Flow_1j6lrkb</bpmn:outgoing>
      <bpmn:outgoing>Flow_184zwle</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0hpnmq9</bpmn:incoming>
      <bpmn:incoming>Flow_184zwle</bpmn:incoming>
      <bpmn:outgoing>Flow_1idas1g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="EndEvent_1">
      <bpmn:incoming>Flow_1idas1g</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1idas1g" sourceRef="Activity_End" targetRef="EndEvent_1" />
    <bpmn:sequenceFlow id="Flow_0hpnmq9" name="核准" sourceRef="Activity_Step7" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10um5rl" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:exclusiveGateway id="Gateway_14v3g9l">
      <bpmn:incoming>Flow_1x7z0h3</bpmn:incoming>
      <bpmn:outgoing>Flow_0dwudw1</bpmn:outgoing>
      <bpmn:outgoing>Flow_10ejgbw</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1j6lrkb" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0dwudw1" name="不需会签" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "N"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10ejgbw" name="需会签" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "Y"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07d4xcj" name="驳回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_184zwle" name="驳回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="相关单位会签">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_10ejgbw</bpmn:incoming>
      <bpmn:outgoing>Flow_07d4xcj</bpmn:outgoing>
      <bpmn:outgoing>Flow_04dm7lc</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0 || approve == false}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step7" name="执行长">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_073i24d</bpmn:incoming>
      <bpmn:outgoing>Flow_0hpnmq9</bpmn:outgoing>
      <bpmn:outgoing>Flow_10y1dl3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step2" name="直属主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1j6lrkb</bpmn:incoming>
      <bpmn:outgoing>Flow_1a965ze</bpmn:outgoing>
      <bpmn:outgoing>Flow_1kymz6i</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1a965ze" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1kymz6i" name="驳回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_118pm6e" name="核准" sourceRef="Activity_Step5" targetRef="Activity_Step6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10y1dl3" name="驳回" sourceRef="Activity_Step7" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="部门主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1a965ze</bpmn:incoming>
      <bpmn:outgoing>Flow_1x7z0h3</bpmn:outgoing>
      <bpmn:outgoing>Flow_1jh19o9</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1x7z0h3" sourceRef="Activity_Step3" targetRef="Gateway_14v3g9l" />
    <bpmn:userTask id="Activity_Step5" name="部门主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_04dm7lc</bpmn:incoming>
      <bpmn:outgoing>Flow_118pm6e</bpmn:outgoing>
      <bpmn:outgoing>Flow_00nk7xh</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_04dm7lc" name="核准" sourceRef="Activity_Step4" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1jh19o9" name="驳回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step6" name="签呈负责人">
      <bpmn:incoming>Flow_0dwudw1</bpmn:incoming>
      <bpmn:incoming>Flow_118pm6e</bpmn:incoming>
      <bpmn:outgoing>Flow_073i24d</bpmn:outgoing>
      <bpmn:outgoing>Flow_18r6nzq</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_073i24d" name="核准" sourceRef="Activity_Step6" targetRef="Activity_Step7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_18r6nzq" name="驳回" sourceRef="Activity_Step6" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_00nk7xh" name="驳回" sourceRef="Activity_Step5" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CN_Approval">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="202" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="240" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="830" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_128mpal_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="862" y="452" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14v3g9l_di" bpmnElement="Gateway_14v3g9l" isMarkerVisible="true">
        <dc:Bounds x="705" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16o97v6_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="810" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="400" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ayks9o_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="560" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1yqnrwz" bpmnElement="Activity_Step5">
        <dc:Bounds x="970" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16h17t2" bpmnElement="Activity_Step6">
        <dc:Bounds x="1130" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1i7b1kf_di" bpmnElement="Activity_Step7">
        <dc:Bounds x="1290" y="180" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1idas1g_di" bpmnElement="Flow_1idas1g">
        <di:waypoint x="880" y="420" />
        <di:waypoint x="880" y="452" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hpnmq9_di" bpmnElement="Flow_0hpnmq9">
        <di:waypoint x="1370" y="260" />
        <di:waypoint x="1370" y="380" />
        <di:waypoint x="930" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1379" y="306" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10um5rl_di" bpmnElement="Flow_10um5rl">
        <di:waypoint x="188" y="220" />
        <di:waypoint x="240" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1j6lrkb_di" bpmnElement="Flow_1j6lrkb">
        <di:waypoint x="340" y="220" />
        <di:waypoint x="400" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="359" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dwudw1_di" bpmnElement="Flow_0dwudw1">
        <di:waypoint x="730" y="195" />
        <di:waypoint x="730" y="140" />
        <di:waypoint x="1180" y="140" />
        <di:waypoint x="1180" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="951" y="123" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10ejgbw_di" bpmnElement="Flow_10ejgbw">
        <di:waypoint x="755" y="220" />
        <di:waypoint x="810" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="764" y="202" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07d4xcj_di" bpmnElement="Flow_07d4xcj">
        <di:waypoint x="860" y="260" />
        <di:waypoint x="860" y="310" />
        <di:waypoint x="310" y="310" />
        <di:waypoint x="310" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="874" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_184zwle_di" bpmnElement="Flow_184zwle">
        <di:waypoint x="260" y="260" />
        <di:waypoint x="260" y="380" />
        <di:waypoint x="830" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="269" y="286" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a965ze_di" bpmnElement="Flow_1a965ze">
        <di:waypoint x="500" y="220" />
        <di:waypoint x="560" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="519" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kymz6i_di" bpmnElement="Flow_1kymz6i">
        <di:waypoint x="450" y="260" />
        <di:waypoint x="450" y="310" />
        <di:waypoint x="310" y="310" />
        <di:waypoint x="310" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="459" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_118pm6e_di" bpmnElement="Flow_118pm6e">
        <di:waypoint x="1070" y="220" />
        <di:waypoint x="1130" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1089" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10y1dl3_di" bpmnElement="Flow_10y1dl3">
        <di:waypoint x="1320" y="260" />
        <di:waypoint x="1320" y="310" />
        <di:waypoint x="310" y="310" />
        <di:waypoint x="310" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1329" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1x7z0h3_di" bpmnElement="Flow_1x7z0h3">
        <di:waypoint x="660" y="220" />
        <di:waypoint x="705" y="220" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04dm7lc_di" bpmnElement="Flow_04dm7lc">
        <di:waypoint x="910" y="220" />
        <di:waypoint x="970" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="929" y="202" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jh19o9_di" bpmnElement="Flow_1jh19o9">
        <di:waypoint x="610" y="260" />
        <di:waypoint x="610" y="310" />
        <di:waypoint x="310" y="310" />
        <di:waypoint x="310" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="619" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_073i24d_di" bpmnElement="Flow_073i24d">
        <di:waypoint x="1230" y="220" />
        <di:waypoint x="1290" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1250" y="198" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18r6nzq_di" bpmnElement="Flow_18r6nzq">
        <di:waypoint x="1180" y="260" />
        <di:waypoint x="1180" y="310" />
        <di:waypoint x="310" y="310" />
        <di:waypoint x="310" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1196" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00nk7xh_di" bpmnElement="Flow_00nk7xh">
        <di:waypoint x="1020" y="260" />
        <di:waypoint x="1020" y="310" />
        <di:waypoint x="310" y="310" />
        <di:waypoint x="310" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1039" y="288" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
