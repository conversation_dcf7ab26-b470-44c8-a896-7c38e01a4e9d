<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1lkv2rp" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:message id="Message_2ug8t3p" name="Message_2ug8t3p" />
  <bpmn:process id="Demo" name="Demo" isExecutable="true" camunda:versionTag="2023-12-18">
    <bpmn:startEvent id="Event_0utwy8s" name="起單">
      <bpmn:extensionElements />
      <bpmn:outgoing>Flow_0srntue</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_0srntue" sourceRef="Event_0utwy8s" targetRef="Activity_Step1" />
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0srntue</bpmn:incoming>
      <bpmn:incoming>Flow_1lnh57i</bpmn:incoming>
      <bpmn:outgoing>Flow_0jj2zi1</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step2" name="直屬主管簽核">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1bby8t8</bpmn:incoming>
      <bpmn:outgoing>Flow_1lnh57i</bpmn:outgoing>
      <bpmn:outgoing>Flow_1w6boec</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="Event_006vnen" name="結束">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_03uubs0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0dvmoiv</bpmn:incoming>
      <bpmn:outgoing>Flow_03uubs0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_03uubs0" sourceRef="Activity_End" targetRef="Event_006vnen" />
    <bpmn:exclusiveGateway id="Gateway_0a9jcpb" name="是否核准">
      <bpmn:incoming>Flow_0jj2zi1</bpmn:incoming>
      <bpmn:outgoing>Flow_1bby8t8</bpmn:outgoing>
      <bpmn:outgoing>Flow_0fk756n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0jj2zi1" sourceRef="Activity_Step1" targetRef="Gateway_0a9jcpb" />
    <bpmn:sequenceFlow id="Flow_1bby8t8" name="核准" sourceRef="Gateway_0a9jcpb" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0fk756n" name="不核准" sourceRef="Gateway_0a9jcpb" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0dvmoiv" sourceRef="Activity_Finished" targetRef="Activity_End" />
    <bpmn:userTask id="Activity_Finished" name="簽核結束">
      <bpmn:incoming>Flow_0fk756n</bpmn:incoming>
      <bpmn:incoming>Flow_0lqrzie</bpmn:incoming>
      <bpmn:outgoing>Flow_0dvmoiv</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1lnh57i" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_1sayc9k">
      <bpmn:incoming>Flow_1w6boec</bpmn:incoming>
      <bpmn:outgoing>Flow_0lqrzie</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1w6boec" sourceRef="Activity_Step2" targetRef="Gateway_1sayc9k">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0lqrzie" name="gateway1" sourceRef="Gateway_1sayc9k" targetRef="Activity_Finished" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Demo">
      <bpmndi:BPMNShape id="Event_0utwy8s_di" bpmnElement="Event_0utwy8s">
        <dc:Bounds x="152" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="225" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="290" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_052ijt8_di" bpmnElement="Activity_Step2">
        <dc:Bounds x="640" y="160" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0a9jcpb_di" bpmnElement="Gateway_0a9jcpb" isMarkerVisible="true">
        <dc:Bounds x="485" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="488" y="145" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0r1mc5e_di" bpmnElement="Activity_End">
        <dc:Bounds x="640" y="400" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_006vnen_di" bpmnElement="Event_006vnen">
        <dc:Bounds x="672" y="512" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="679" y="558" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1gkaryw_di" bpmnElement="Activity_Finished">
        <dc:Bounds x="640" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1sayc9k_di" bpmnElement="Gateway_1sayc9k" isMarkerVisible="true">
        <dc:Bounds x="845" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0srntue_di" bpmnElement="Flow_0srntue">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="290" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0jj2zi1_di" bpmnElement="Flow_0jj2zi1">
        <di:waypoint x="390" y="200" />
        <di:waypoint x="485" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bby8t8_di" bpmnElement="Flow_1bby8t8">
        <di:waypoint x="535" y="200" />
        <di:waypoint x="640" y="200" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="569" y="182" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fk756n_di" bpmnElement="Flow_0fk756n">
        <di:waypoint x="510" y="225" />
        <di:waypoint x="510" y="320" />
        <di:waypoint x="640" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="563" y="326" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03uubs0_di" bpmnElement="Flow_03uubs0">
        <di:waypoint x="690" y="480" />
        <di:waypoint x="690" y="512" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dvmoiv_di" bpmnElement="Flow_0dvmoiv">
        <di:waypoint x="690" y="360" />
        <di:waypoint x="690" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1lnh57i_di" bpmnElement="Flow_1lnh57i">
        <di:waypoint x="690" y="160" />
        <di:waypoint x="690" y="80" />
        <di:waypoint x="340" y="80" />
        <di:waypoint x="340" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="505" y="62" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1w6boec_di" bpmnElement="Flow_1w6boec">
        <di:waypoint x="740" y="200" />
        <di:waypoint x="845" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lqrzie_di" bpmnElement="Flow_0lqrzie">
        <di:waypoint x="870" y="225" />
        <di:waypoint x="870" y="320" />
        <di:waypoint x="740" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="816" y="270" width="48" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
