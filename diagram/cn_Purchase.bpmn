<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0og4ys1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="CN_Purchase" name="采购" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0at6wdn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_Finished" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1dkalj7</bpmn:incoming>
      <bpmn:incoming>Flow_0nm7ihb</bpmn:incoming>
      <bpmn:incoming>Flow_1q5hbke</bpmn:incoming>
      <bpmn:incoming>Flow_1wj65rc</bpmn:incoming>
      <bpmn:incoming>Flow_1e2228g</bpmn:incoming>
      <bpmn:incoming>Flow_1h59ttq</bpmn:incoming>
      <bpmn:incoming>Flow_12ncm8m</bpmn:incoming>
      <bpmn:incoming>Flow_17cqjo7</bpmn:incoming>
      <bpmn:incoming>Flow_1hdls6f</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdhhmm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Activity_Step2" name="办事处主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1sklbmc</bpmn:incoming>
      <bpmn:outgoing>Flow_0oc8paj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0nm7ihb</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1sklbmc" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateThrowEvent id="Event_02b5csr">
      <bpmn:incoming>Flow_1gdhhmm</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1gdhhmm" sourceRef="Activity_Finished" targetRef="Event_02b5csr" />
    <bpmn:serviceTask id="Activity_Start" name="开始" camunda:type="external" camunda:topic="common:task:start">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0at6wdn</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0p54t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0at6wdn" sourceRef="StartEvent_1" targetRef="Activity_Start" />
    <bpmn:sequenceFlow id="Flow_0f0p54t" sourceRef="Activity_Start" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_0oc8paj" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1q5hbke" name="驳回" sourceRef="Activity_Step1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0nm7ihb" name="驳回" sourceRef="Activity_Step2" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1dkalj7" name="驳回" sourceRef="Activity_Step4" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="事业部主管">
      <bpmn:incoming>Flow_0hnc2cl</bpmn:incoming>
      <bpmn:outgoing>Flow_1dkalj7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m21ptg</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1m21ptg" name="核准" sourceRef="Activity_Step4" targetRef="Gateway_083mr5c">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="大区主管">
      <bpmn:incoming>Flow_0oc8paj</bpmn:incoming>
      <bpmn:outgoing>Flow_0hnc2cl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1wj65rc</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0hnc2cl" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wj65rc" name="驳回" sourceRef="Activity_Step3" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step1" name="业务">
      <bpmn:incoming>Flow_0f0p54t</bpmn:incoming>
      <bpmn:outgoing>Flow_1sklbmc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1q5hbke</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step6" name="总经理">
      <bpmn:incoming>Flow_088pbfl</bpmn:incoming>
      <bpmn:outgoing>Flow_1hdls6f</bpmn:outgoing>
      <bpmn:outgoing>Flow_0t3l1tl</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_083mr5c">
      <bpmn:incoming>Flow_1m21ptg</bpmn:incoming>
      <bpmn:outgoing>Flow_1h59ttq</bpmn:outgoing>
      <bpmn:outgoing>Flow_07eqzry</bpmn:outgoing>
      <bpmn:outgoing>Flow_1oja1pf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Activity_Step5" name="财务主管">
      <bpmn:incoming>Flow_07eqzry</bpmn:incoming>
      <bpmn:outgoing>Flow_17cqjo7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1rdrybp</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_1totbdq">
      <bpmn:incoming>Flow_1oja1pf</bpmn:incoming>
      <bpmn:incoming>Flow_1rdrybp</bpmn:incoming>
      <bpmn:outgoing>Flow_088pbfl</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pgt1e6</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Activity_Step7" name="总監">
      <bpmn:incoming>Flow_0t3l1tl</bpmn:incoming>
      <bpmn:incoming>Flow_0pgt1e6</bpmn:incoming>
      <bpmn:outgoing>Flow_1e2228g</bpmn:outgoing>
      <bpmn:outgoing>Flow_12ncm8m</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step7_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1hdls6f" name="驳回" sourceRef="Activity_Step6" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1h59ttq" sourceRef="Gateway_083mr5c" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("form_data_isCEO") == false }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_17cqjo7" name="驳回" sourceRef="Activity_Step5" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1e2228g" name="核准" sourceRef="Activity_Step7" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_12ncm8m" name="驳回" sourceRef="Activity_Step7" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_088pbfl" sourceRef="Gateway_1totbdq" targetRef="Activity_Step6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("form_data_isCEO") == true }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0t3l1tl" name="核准" sourceRef="Activity_Step6" targetRef="Activity_Step7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07eqzry" sourceRef="Gateway_083mr5c" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("form_data_isCEO") == true }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1oja1pf" sourceRef="Gateway_083mr5c" targetRef="Gateway_1totbdq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Activity_Step4_gateway == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1rdrybp" name="核准" sourceRef="Activity_Step5" targetRef="Gateway_1totbdq">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0pgt1e6" sourceRef="Gateway_1totbdq" targetRef="Activity_Step7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${Activity_Step4_gateway == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:error id="Error_1ehy8wa" name="Error_2ot71qf" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CN_Purchase">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="179" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ma9eup_di" bpmnElement="Activity_Step2">
        <dc:Bounds x="551" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0o1w0z6" bpmnElement="Activity_Start">
        <dc:Bounds x="240" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16n3l3a_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="870" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pwf7gp" bpmnElement="Activity_Step3">
        <dc:Bounds x="710" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jwbby9" bpmnElement="Activity_Step1">
        <dc:Bounds x="390" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0tnpyxk" bpmnElement="Activity_Step6">
        <dc:Bounds x="1320" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1p41dpc" bpmnElement="Activity_Step5">
        <dc:Bounds x="1091" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1totbdq_di" bpmnElement="Gateway_1totbdq" isMarkerVisible="true">
        <dc:Bounds x="1246" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16buzbi" bpmnElement="Activity_Step7">
        <dc:Bounds x="1481" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_083mr5c_di" bpmnElement="Gateway_083mr5c" isMarkerVisible="true">
        <dc:Bounds x="1016" y="172" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02b5csr_di" bpmnElement="Event_02b5csr">
        <dc:Bounds x="1779" y="179" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_Finished">
        <dc:Bounds x="1640" y="157" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1sklbmc_di" bpmnElement="Flow_1sklbmc">
        <di:waypoint x="490" y="197" />
        <di:waypoint x="551" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="512" y="179" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0at6wdn_di" bpmnElement="Flow_0at6wdn">
        <di:waypoint x="188" y="197" />
        <di:waypoint x="240" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f0p54t_di" bpmnElement="Flow_0f0p54t">
        <di:waypoint x="340" y="197" />
        <di:waypoint x="390" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oc8paj_di" bpmnElement="Flow_0oc8paj">
        <di:waypoint x="651" y="197" />
        <di:waypoint x="710" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="669" y="179" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q5hbke_di" bpmnElement="Flow_1q5hbke">
        <di:waypoint x="440" y="237" />
        <di:waypoint x="440" y="310" />
        <di:waypoint x="1690" y="310" />
        <di:waypoint x="1690" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="451" y="273" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nm7ihb_di" bpmnElement="Flow_0nm7ihb">
        <di:waypoint x="601" y="237" />
        <di:waypoint x="601" y="310" />
        <di:waypoint x="1690" y="310" />
        <di:waypoint x="1690" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="615" y="273" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dkalj7_di" bpmnElement="Flow_1dkalj7">
        <di:waypoint x="920" y="237" />
        <di:waypoint x="920" y="310" />
        <di:waypoint x="1690" y="310" />
        <di:waypoint x="1690" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="939" y="273" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1m21ptg_di" bpmnElement="Flow_1m21ptg">
        <di:waypoint x="970" y="197" />
        <di:waypoint x="1016" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="981" y="179" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hnc2cl_di" bpmnElement="Flow_0hnc2cl">
        <di:waypoint x="810" y="197" />
        <di:waypoint x="870" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="829" y="179" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wj65rc_di" bpmnElement="Flow_1wj65rc">
        <di:waypoint x="760" y="237" />
        <di:waypoint x="760" y="310" />
        <di:waypoint x="1690" y="310" />
        <di:waypoint x="1690" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="273" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hdls6f_di" bpmnElement="Flow_1hdls6f">
        <di:waypoint x="1370" y="237" />
        <di:waypoint x="1370" y="310" />
        <di:waypoint x="1690" y="310" />
        <di:waypoint x="1690" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1379" y="273" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h59ttq_di" bpmnElement="Flow_1h59ttq">
        <di:waypoint x="1031" y="182" />
        <di:waypoint x="1031" y="80" />
        <di:waypoint x="1690" y="80" />
        <di:waypoint x="1690" y="157" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_17cqjo7_di" bpmnElement="Flow_17cqjo7">
        <di:waypoint x="1141" y="237" />
        <di:waypoint x="1141" y="310" />
        <di:waypoint x="1690" y="310" />
        <di:waypoint x="1690" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1150" y="273" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1e2228g_di" bpmnElement="Flow_1e2228g">
        <di:waypoint x="1581" y="197" />
        <di:waypoint x="1640" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1600" y="179" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_12ncm8m_di" bpmnElement="Flow_12ncm8m">
        <di:waypoint x="1531" y="237" />
        <di:waypoint x="1531" y="310" />
        <di:waypoint x="1690" y="310" />
        <di:waypoint x="1690" y="237" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1550" y="273" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_088pbfl_di" bpmnElement="Flow_088pbfl">
        <di:waypoint x="1296" y="197" />
        <di:waypoint x="1320" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t3l1tl_di" bpmnElement="Flow_0t3l1tl">
        <di:waypoint x="1420" y="197" />
        <di:waypoint x="1481" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1437" y="179" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07eqzry_di" bpmnElement="Flow_07eqzry">
        <di:waypoint x="1066" y="197" />
        <di:waypoint x="1091" y="197" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rdrybp_di" bpmnElement="Flow_1rdrybp">
        <di:waypoint x="1191" y="197" />
        <di:waypoint x="1246" y="197" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1208" y="179" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1oja1pf_di" bpmnElement="Flow_1oja1pf">
        <di:waypoint x="1041" y="172" />
        <di:waypoint x="1041" y="110" />
        <di:waypoint x="1261" y="110" />
        <di:waypoint x="1261" y="182" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pgt1e6_di" bpmnElement="Flow_0pgt1e6">
        <di:waypoint x="1271" y="172" />
        <di:waypoint x="1271" y="110" />
        <di:waypoint x="1531" y="110" />
        <di:waypoint x="1531" y="157" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gdhhmm_di" bpmnElement="Flow_1gdhhmm">
        <di:waypoint x="1740" y="197" />
        <di:waypoint x="1779" y="197" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
