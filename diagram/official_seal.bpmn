<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1qzc94u" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.3.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="Official_Seal" name="合約存查" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0632vvm</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">initiator</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0632vvm</bpmn:incoming>
      <bpmn:incoming>Flow_04cddxg</bpmn:incoming>
      <bpmn:incoming>Flow_04kpuwz</bpmn:incoming>
      <bpmn:outgoing>Flow_1c3bdzx</bpmn:outgoing>
      <bpmn:outgoing>Flow_1og0yw7</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0632vvm" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:userTask id="Activity_Step2" name="直屬主管">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">direct_supervisor</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1c3bdzx</bpmn:incoming>
      <bpmn:outgoing>Flow_1nmdc2z</bpmn:outgoing>
      <bpmn:outgoing>Flow_04cddxg</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step3" name="相關單位會簽">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">prefix_form_specify_user</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1nmdc2z</bpmn:incoming>
      <bpmn:outgoing>Flow_041og17</bpmn:outgoing>
      <bpmn:outgoing>Flow_04kpuwz</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="form_data_countersigners" camunda:elementVariable="user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${countersigners_reject_count &gt; 0 || nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_1og0yw7</bpmn:incoming>
      <bpmn:incoming>Flow_1pl1e3i</bpmn:incoming>
      <bpmn:outgoing>Flow_14buuqo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1c3bdzx" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1nmdc2z" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_041og17" name="核准" sourceRef="Activity_Step3" targetRef="Activity_0hlugcj">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1og0yw7" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_04cddxg" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_04kpuwz" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateThrowEvent id="Event_0wc3sty">
      <bpmn:incoming>Flow_14buuqo</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_14buuqo" sourceRef="Activity_End" targetRef="Event_0wc3sty" />
    <bpmn:userTask id="Activity_0hlugcj" name="RA">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">specify_user_list</camunda:inputParameter>
          <camunda:inputParameter name="only_approved">true</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_041og17</bpmn:incoming>
      <bpmn:outgoing>Flow_1pl1e3i</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="initial_data_specify_user_list" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1pl1e3i" sourceRef="Activity_0hlugcj" targetRef="Activity_End" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Official_Seal">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="300" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="510" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16o97v6_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="720" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="510" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0wc3sty_di" bpmnElement="Event_0wc3sty">
        <dc:Bounds x="542" y="382" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_060p6dy_di" bpmnElement="Activity_0hlugcj">
        <dc:Bounds x="920" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0632vvm_di" bpmnElement="Flow_0632vvm">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="300" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c3bdzx_di" bpmnElement="Flow_1c3bdzx">
        <di:waypoint x="400" y="117" />
        <di:waypoint x="510" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="444" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1nmdc2z_di" bpmnElement="Flow_1nmdc2z">
        <di:waypoint x="610" y="117" />
        <di:waypoint x="720" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="654" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_041og17_di" bpmnElement="Flow_041og17">
        <di:waypoint x="820" y="117" />
        <di:waypoint x="920" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="859" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1og0yw7_di" bpmnElement="Flow_1og0yw7">
        <di:waypoint x="330" y="157" />
        <di:waypoint x="330" y="280" />
        <di:waypoint x="510" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="299" y="182" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04cddxg_di" bpmnElement="Flow_04cddxg">
        <di:waypoint x="560" y="157" />
        <di:waypoint x="560" y="200" />
        <di:waypoint x="350" y="200" />
        <di:waypoint x="350" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="569" y="182" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04kpuwz_di" bpmnElement="Flow_04kpuwz">
        <di:waypoint x="770" y="157" />
        <di:waypoint x="770" y="200" />
        <di:waypoint x="350" y="200" />
        <di:waypoint x="350" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="779" y="182" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14buuqo_di" bpmnElement="Flow_14buuqo">
        <di:waypoint x="560" y="320" />
        <di:waypoint x="560" y="382" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1pl1e3i_di" bpmnElement="Flow_1pl1e3i">
        <di:waypoint x="970" y="157" />
        <di:waypoint x="970" y="280" />
        <di:waypoint x="610" y="280" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
