<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0og4ys1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="CN_Scrap" name="报废" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0at6wdn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_Finished" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_1q5hbke</bpmn:incoming>
      <bpmn:incoming>Flow_0nm7ihb</bpmn:incoming>
      <bpmn:incoming>Flow_1wj65rc</bpmn:incoming>
      <bpmn:incoming>Flow_0y3lic2</bpmn:incoming>
      <bpmn:incoming>Flow_1v9t41p</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdhhmm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_02b5csr">
      <bpmn:incoming>Flow_1gdhhmm</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_Start" name="开始" camunda:type="external" camunda:topic="common:task:start">
      <bpmn:incoming>Flow_0at6wdn</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0p54t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Activity_Step3" name="大区经理 / &#10;事业单位、财务 、市场部主管">
      <bpmn:incoming>Flow_0oc8paj</bpmn:incoming>
      <bpmn:outgoing>Flow_1wj65rc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hnc2cl</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step1" name="业务">
      <bpmn:incoming>Flow_0f0p54t</bpmn:incoming>
      <bpmn:outgoing>Flow_1q5hbke</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sklbmc</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step5" name="总经理">
      <bpmn:incoming>Flow_0hnc2cl</bpmn:incoming>
      <bpmn:outgoing>Flow_0y3lic2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1v9t41p</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step2" name="部门主管">
      <bpmn:incoming>Flow_1sklbmc</bpmn:incoming>
      <bpmn:outgoing>Flow_0nm7ihb</bpmn:outgoing>
      <bpmn:outgoing>Flow_0oc8paj</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step2_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0at6wdn" sourceRef="StartEvent_1" targetRef="Activity_Start" />
    <bpmn:sequenceFlow id="Flow_1q5hbke" name="驳回" sourceRef="Activity_Step1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0nm7ihb" name="驳回" sourceRef="Activity_Step2" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wj65rc" name="驳回" sourceRef="Activity_Step3" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0y3lic2" name="驳回" sourceRef="Activity_Step5" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1gdhhmm" sourceRef="Activity_Finished" targetRef="Event_02b5csr" />
    <bpmn:sequenceFlow id="Flow_0f0p54t" sourceRef="Activity_Start" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_0hnc2cl" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1sklbmc" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1v9t41p" name="核准" sourceRef="Activity_Step5" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0oc8paj" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:error id="Error_1ehy8wa" name="Error_2ot71qf" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CN_Scrap">
      <bpmndi:BPMNShape id="BPMNShape_01mqhts" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="122" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06g3b2w" bpmnElement="Activity_Start">
        <dc:Bounds x="240" y="100" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1664830" bpmnElement="Activity_Step1">
        <dc:Bounds x="390" y="100" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_14hcls8" bpmnElement="Activity_Step2">
        <dc:Bounds x="551" y="100" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0httkb7" bpmnElement="Activity_Step3">
        <dc:Bounds x="720" y="100" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1gkfdot" bpmnElement="Activity_Step5">
        <dc:Bounds x="890" y="100" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1j2ziwd" bpmnElement="Event_02b5csr">
        <dc:Bounds x="1199" y="122" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10txg3r" bpmnElement="Activity_Finished">
        <dc:Bounds x="1060" y="100" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0fttqrd" bpmnElement="Flow_0at6wdn">
        <di:waypoint x="188" y="140" />
        <di:waypoint x="240" y="140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0j3wxqf" bpmnElement="Flow_1q5hbke">
        <di:waypoint x="440" y="180" />
        <di:waypoint x="440" y="253" />
        <di:waypoint x="1110" y="253" />
        <di:waypoint x="1110" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="444" y="216" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0gbbah8" bpmnElement="Flow_0nm7ihb">
        <di:waypoint x="600" y="180" />
        <di:waypoint x="600" y="253" />
        <di:waypoint x="1110" y="253" />
        <di:waypoint x="1110" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="601" y="216" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0gryn61" bpmnElement="Flow_1wj65rc">
        <di:waypoint x="770" y="180" />
        <di:waypoint x="770" y="253" />
        <di:waypoint x="1110" y="253" />
        <di:waypoint x="1110" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="789" y="216" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y3lic2_di" bpmnElement="Flow_0y3lic2">
        <di:waypoint x="940" y="180" />
        <di:waypoint x="940" y="253" />
        <di:waypoint x="1110" y="253" />
        <di:waypoint x="1110" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="959" y="216" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_11nifyx" bpmnElement="Flow_0f0p54t">
        <di:waypoint x="340" y="140" />
        <di:waypoint x="390" y="140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1w4bqs0" bpmnElement="Flow_0hnc2cl">
        <di:waypoint x="820" y="140" />
        <di:waypoint x="890" y="140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="845" y="122" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1q0xrdi" bpmnElement="Flow_1sklbmc">
        <di:waypoint x="490" y="140" />
        <di:waypoint x="551" y="140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="512" y="122" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v9t41p_di" bpmnElement="Flow_1v9t41p">
        <di:waypoint x="990" y="140" />
        <di:waypoint x="1060" y="140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1018" y="122" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_14ddo1n" bpmnElement="Flow_0oc8paj">
        <di:waypoint x="651" y="140" />
        <di:waypoint x="720" y="140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="676" y="122" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0kpd8kp" bpmnElement="Flow_1gdhhmm">
        <di:waypoint x="1160" y="140" />
        <di:waypoint x="1199" y="140" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
