<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0og4ys1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="CN_Requisition" name="国内请购" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0at6wdn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_Finished" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_1q5hbke</bpmn:incoming>
      <bpmn:incoming>Flow_0nm7ihb</bpmn:incoming>
      <bpmn:incoming>Flow_1dkalj7</bpmn:incoming>
      <bpmn:incoming>Flow_1wj65rc</bpmn:incoming>
      <bpmn:incoming>Flow_0y3lic2</bpmn:incoming>
      <bpmn:incoming>Flow_08jasmc</bpmn:incoming>
      <bpmn:incoming>Flow_09ta2w5</bpmn:incoming>
      <bpmn:incoming>Flow_1ywta1r</bpmn:incoming>
      <bpmn:incoming>Flow_1ide06s</bpmn:incoming>
      <bpmn:incoming>Flow_0drydst</bpmn:incoming>
      <bpmn:incoming>Flow_117buma</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdhhmm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_02b5csr">
      <bpmn:incoming>Flow_1gdhhmm</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_Start" name="开始" camunda:type="external" camunda:topic="common:task:start">
      <bpmn:incoming>Flow_0at6wdn</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0p54t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Activity_Step4" name="营运长">
      <bpmn:incoming>Flow_1l7612x</bpmn:incoming>
      <bpmn:outgoing>Flow_1dkalj7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m21ptg</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step3" name="大区经理 / &#10;事业单位、财务 、市场部主管">
      <bpmn:incoming>Flow_033s3sq</bpmn:incoming>
      <bpmn:outgoing>Flow_1wj65rc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hnc2cl</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step1" name="业务">
      <bpmn:incoming>Flow_0f0p54t</bpmn:incoming>
      <bpmn:outgoing>Flow_1q5hbke</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sklbmc</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step5" name="总经理">
      <bpmn:incoming>Flow_1wct1tl</bpmn:incoming>
      <bpmn:outgoing>Flow_0y3lic2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1v9t41p</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_0el6rcx">
      <bpmn:incoming>Flow_0oc8paj</bpmn:incoming>
      <bpmn:outgoing>Flow_1ywta1r</bpmn:outgoing>
      <bpmn:outgoing>Flow_033s3sq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1xkckj4">
      <bpmn:incoming>Flow_0hnc2cl</bpmn:incoming>
      <bpmn:outgoing>Flow_1ide06s</bpmn:outgoing>
      <bpmn:outgoing>Flow_1l7612x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0hsj2j1">
      <bpmn:incoming>Flow_1m21ptg</bpmn:incoming>
      <bpmn:outgoing>Flow_0drydst</bpmn:outgoing>
      <bpmn:outgoing>Flow_1wct1tl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1878713">
      <bpmn:incoming>Flow_1v9t41p</bpmn:incoming>
      <bpmn:outgoing>Flow_117buma</bpmn:outgoing>
      <bpmn:outgoing>Flow_0koo2wf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Activity_Step2" name="部门主管">
      <bpmn:incoming>Flow_1sklbmc</bpmn:incoming>
      <bpmn:outgoing>Flow_0nm7ihb</bpmn:outgoing>
      <bpmn:outgoing>Flow_0oc8paj</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step2_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step6" name="执行长">
      <bpmn:incoming>Flow_0koo2wf</bpmn:incoming>
      <bpmn:outgoing>Flow_08jasmc</bpmn:outgoing>
      <bpmn:outgoing>Flow_09ta2w5</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0at6wdn" sourceRef="StartEvent_1" targetRef="Activity_Start" />
    <bpmn:sequenceFlow id="Flow_1q5hbke" name="驳回" sourceRef="Activity_Step1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0nm7ihb" name="驳回" sourceRef="Activity_Step2" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1dkalj7" name="驳回" sourceRef="Activity_Step4" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wj65rc" name="驳回" sourceRef="Activity_Step3" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0y3lic2" name="驳回" sourceRef="Activity_Step5" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08jasmc" name="核准" sourceRef="Activity_Step6" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_09ta2w5" name="驳回" sourceRef="Activity_Step6" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ywta1r" sourceRef="Gateway_0el6rcx" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${1 == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ide06s" sourceRef="Gateway_1xkckj4" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${1 == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0drydst" sourceRef="Gateway_0hsj2j1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${1 == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_117buma" sourceRef="Gateway_1878713" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${1 == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1gdhhmm" sourceRef="Activity_Finished" targetRef="Event_02b5csr" />
    <bpmn:sequenceFlow id="Flow_0f0p54t" sourceRef="Activity_Start" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_1l7612x" name="6,001 - 7,500" sourceRef="Gateway_1xkckj4" targetRef="Activity_Step4" />
    <bpmn:sequenceFlow id="Flow_1m21ptg" name="核准" sourceRef="Activity_Step4" targetRef="Gateway_0hsj2j1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_033s3sq" name="2,501 - 6,000" sourceRef="Gateway_0el6rcx" targetRef="Activity_Step3" />
    <bpmn:sequenceFlow id="Flow_0hnc2cl" name="核准" sourceRef="Activity_Step3" targetRef="Gateway_1xkckj4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1sklbmc" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wct1tl" name="7,501 - 12,500" sourceRef="Gateway_0hsj2j1" targetRef="Activity_Step5" />
    <bpmn:sequenceFlow id="Flow_1v9t41p" name="核准" sourceRef="Activity_Step5" targetRef="Gateway_1878713">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0oc8paj" name="核准" sourceRef="Activity_Step2" targetRef="Gateway_0el6rcx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0koo2wf" name="12,501 以上" sourceRef="Gateway_1878713" targetRef="Activity_Step6" />
  </bpmn:process>
  <bpmn:error id="Error_1ehy8wa" name="Error_2ot71qf" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CN_Requisition">
      <bpmndi:BPMNShape id="BPMNShape_1ip558u" bpmnElement="StartEvent_1">
        <dc:Bounds x="172" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1qoto5p" bpmnElement="Activity_Finished">
        <dc:Bounds x="1930" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ujw4hr" bpmnElement="Event_02b5csr">
        <dc:Bounds x="2069" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0jbcqi7" bpmnElement="Activity_Start">
        <dc:Bounds x="260" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kwbxlw" bpmnElement="Activity_Step4">
        <dc:Bounds x="1170" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0jkya1i" bpmnElement="Activity_Step3">
        <dc:Bounds x="870" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16vvv94" bpmnElement="Activity_Step1">
        <dc:Bounds x="410" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0drjgca" bpmnElement="Activity_Step5">
        <dc:Bounds x="1470" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_029zo20" bpmnElement="Gateway_0el6rcx" isMarkerVisible="true">
        <dc:Bounds x="735" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ec369c" bpmnElement="Gateway_1xkckj4" isMarkerVisible="true">
        <dc:Bounds x="1035" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1wcwdfa" bpmnElement="Gateway_0hsj2j1" isMarkerVisible="true">
        <dc:Bounds x="1325" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0k3bcvu" bpmnElement="Gateway_1878713" isMarkerVisible="true">
        <dc:Bounds x="1624" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0p96mcp" bpmnElement="Activity_Step2">
        <dc:Bounds x="571" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_043qizq" bpmnElement="Activity_Step6">
        <dc:Bounds x="1770" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_1r8sowt" bpmnElement="Flow_0at6wdn">
        <di:waypoint x="208" y="190" />
        <di:waypoint x="260" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1btmw5y" bpmnElement="Flow_1q5hbke">
        <di:waypoint x="460" y="230" />
        <di:waypoint x="460" y="303" />
        <di:waypoint x="1980" y="303" />
        <di:waypoint x="1980" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="482" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1xt94y8" bpmnElement="Flow_0nm7ihb">
        <di:waypoint x="620" y="230" />
        <di:waypoint x="620" y="303" />
        <di:waypoint x="1980" y="303" />
        <di:waypoint x="1980" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="642" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1d0ldwr" bpmnElement="Flow_1dkalj7">
        <di:waypoint x="1220" y="230" />
        <di:waypoint x="1220" y="303" />
        <di:waypoint x="1980" y="303" />
        <di:waypoint x="1980" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1239" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0co3la9" bpmnElement="Flow_1wj65rc">
        <di:waypoint x="920" y="230" />
        <di:waypoint x="920" y="303" />
        <di:waypoint x="1980" y="303" />
        <di:waypoint x="1980" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="939" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1kpa4ay" bpmnElement="Flow_0y3lic2">
        <di:waypoint x="1520" y="230" />
        <di:waypoint x="1520" y="303" />
        <di:waypoint x="1980" y="303" />
        <di:waypoint x="1980" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1539" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0dr4wf8" bpmnElement="Flow_08jasmc">
        <di:waypoint x="1870" y="190" />
        <di:waypoint x="1930" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1889" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_12y3wsh" bpmnElement="Flow_09ta2w5">
        <di:waypoint x="1820" y="230" />
        <di:waypoint x="1820" y="303" />
        <di:waypoint x="1980" y="303" />
        <di:waypoint x="1980" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1839" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_05hyrq4" bpmnElement="Flow_1ywta1r">
        <di:waypoint x="760" y="165" />
        <di:waypoint x="760" y="83" />
        <di:waypoint x="1980" y="83" />
        <di:waypoint x="1980" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="660" y="146" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1w2wrqq" bpmnElement="Flow_1ide06s">
        <di:waypoint x="1060" y="165" />
        <di:waypoint x="1060" y="83" />
        <di:waypoint x="1980" y="83" />
        <di:waypoint x="1980" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="956" y="146" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1xns9ov" bpmnElement="Flow_0drydst">
        <di:waypoint x="1350" y="165" />
        <di:waypoint x="1350" y="83" />
        <di:waypoint x="1980" y="83" />
        <di:waypoint x="1980" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1239" y="146" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0t0p73i" bpmnElement="Flow_117buma">
        <di:waypoint x="1649" y="165" />
        <di:waypoint x="1649" y="83" />
        <di:waypoint x="1980" y="83" />
        <di:waypoint x="1980" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1488" y="146" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1uv8zry" bpmnElement="Flow_1gdhhmm">
        <di:waypoint x="2030" y="190" />
        <di:waypoint x="2069" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1ykfk73" bpmnElement="Flow_0f0p54t">
        <di:waypoint x="360" y="190" />
        <di:waypoint x="410" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0vz6gbr" bpmnElement="Flow_1l7612x">
        <di:waypoint x="1085" y="190" />
        <di:waypoint x="1170" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1095" y="172" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0nupxpc" bpmnElement="Flow_1m21ptg">
        <di:waypoint x="1270" y="190" />
        <di:waypoint x="1325" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1285" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_194c2y6" bpmnElement="Flow_033s3sq">
        <di:waypoint x="785" y="190" />
        <di:waypoint x="870" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="795" y="172" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0qwvic2" bpmnElement="Flow_0hnc2cl">
        <di:waypoint x="970" y="190" />
        <di:waypoint x="1035" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="992" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_01s9ytz" bpmnElement="Flow_1sklbmc">
        <di:waypoint x="510" y="190" />
        <di:waypoint x="571" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="532" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1aiha32" bpmnElement="Flow_1wct1tl">
        <di:waypoint x="1375" y="190" />
        <di:waypoint x="1470" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1387" y="172" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0tt9qgs" bpmnElement="Flow_1v9t41p">
        <di:waypoint x="1570" y="190" />
        <di:waypoint x="1624" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1589" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_19ciruy" bpmnElement="Flow_0oc8paj">
        <di:waypoint x="671" y="190" />
        <di:waypoint x="735" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="693" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0lutudw" bpmnElement="Flow_0koo2wf">
        <di:waypoint x="1674" y="190" />
        <di:waypoint x="1770" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1693" y="172" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
