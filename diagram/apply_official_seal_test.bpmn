<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_11l56v6" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="Apply_Official_Seal" name="申請合約用印" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0ztdxj3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0ztdxj3</bpmn:incoming>
      <bpmn:incoming>Flow_1s6bqya</bpmn:incoming>
      <bpmn:incoming>Flow_0cmkxg0</bpmn:incoming>
      <bpmn:incoming>Flow_04xybqa</bpmn:incoming>
      <bpmn:incoming>Flow_0pi0och</bpmn:incoming>
      <bpmn:incoming>Flow_0ifrl68</bpmn:incoming>
      <bpmn:outgoing>Flow_1fyedkf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bdhwwf</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0ztdxj3" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:userTask id="Activity_Step2" name="直屬主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1fyedkf</bpmn:incoming>
      <bpmn:outgoing>Flow_0eemaff</bpmn:outgoing>
      <bpmn:outgoing>Flow_1s6bqya</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step3" name="相關單位會簽">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0x50132</bpmn:incoming>
      <bpmn:outgoing>Flow_0cmkxg0</bpmn:outgoing>
      <bpmn:outgoing>Flow_03lz3q1</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="form_data_countersigners" camunda:elementVariable="user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${countersigners_reject_count &gt; 0 || nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0bdhwwf</bpmn:incoming>
      <bpmn:incoming>Flow_1r8b04c</bpmn:incoming>
      <bpmn:outgoing>Flow_0djdrch</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_1b020yf">
      <bpmn:incoming>Flow_0djdrch</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0djdrch" sourceRef="Activity_End" targetRef="Event_1b020yf" />
    <bpmn:exclusiveGateway id="Gateway_14v3g9l">
      <bpmn:incoming>Flow_02mn17s</bpmn:incoming>
      <bpmn:outgoing>Flow_0x50132</bpmn:outgoing>
      <bpmn:outgoing>Flow_0somi1n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fyedkf" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0eemaff" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step2-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0x50132" name="需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "Y"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0somi1n" name="不需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "N"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1s6bqya" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0bdhwwf" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0cmkxg0" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="RA">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_03lz3q1</bpmn:incoming>
      <bpmn:incoming>Flow_0somi1n</bpmn:incoming>
      <bpmn:outgoing>Flow_04xybqa</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ptnn2g</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_03lz3q1" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_04xybqa" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step2-1" name="部門主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0eemaff</bpmn:incoming>
      <bpmn:outgoing>Flow_02mn17s</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pi0och</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_02mn17s" name="核准" sourceRef="Activity_Step2-1" targetRef="Gateway_14v3g9l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0pi0och" name="駁回" sourceRef="Activity_Step2-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step7" name="起單人上傳雙方用印後合約">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0y52lpr</bpmn:incoming>
      <bpmn:outgoing>Flow_1izwis9</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step8" name="RA">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1izwis9</bpmn:incoming>
      <bpmn:outgoing>Flow_1r8b04c</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step8_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1izwis9" name="核准" sourceRef="Activity_Step7" targetRef="Activity_Step8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1r8b04c" name="核准" sourceRef="Activity_Step8" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0y52lpr" sourceRef="Activity_Step5-4" targetRef="Activity_Step7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step5-1" name="大章">
      <bpmn:incoming>Flow_0ptnn2g</bpmn:incoming>
      <bpmn:outgoing>Flow_0ifrl68</bpmn:outgoing>
      <bpmn:outgoing>Flow_1s0s8hj</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5-1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0ifrl68" name="駁回" sourceRef="Activity_Step5-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ptnn2g" name="核准" sourceRef="Activity_Step4" targetRef="Activity_Step5-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step5-2" name="小章">
      <bpmn:incoming>Flow_1s0s8hj</bpmn:incoming>
      <bpmn:outgoing>Flow_0xmvjg9</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5-2_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step5-3" name="騎縫章">
      <bpmn:incoming>Flow_0xmvjg9</bpmn:incoming>
      <bpmn:outgoing>Flow_0agazjl</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5-3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step5-4" name="戳記">
      <bpmn:incoming>Flow_0agazjl</bpmn:incoming>
      <bpmn:outgoing>Flow_0y52lpr</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5-4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1s0s8hj" sourceRef="Activity_Step5-1" targetRef="Activity_Step5-2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0xmvjg9" sourceRef="Activity_Step5-2" targetRef="Activity_Step5-3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0agazjl" sourceRef="Activity_Step5-3" targetRef="Activity_Step5-4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Apply_Official_Seal">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="169" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="230" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="382" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16o97v6_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="787" y="147" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="760" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1b020yf_di" bpmnElement="Event_1b020yf">
        <dc:Bounds x="942" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14v3g9l_di" bpmnElement="Gateway_14v3g9l" isMarkerVisible="true">
        <dc:Bounds x="696" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_060p6dy_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="948" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ayks9o_di" bpmnElement="Activity_Step2-1">
        <dc:Bounds x="540" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yi3ol4" bpmnElement="Activity_Step7">
        <dc:Bounds x="410" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10i184b" bpmnElement="Activity_Step8">
        <dc:Bounds x="580" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0fyl0mq" bpmnElement="Activity_Step5-2">
        <dc:Bounds x="1270" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16h17t2" bpmnElement="Activity_Step5-1">
        <dc:Bounds x="1110" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0auvkg1" bpmnElement="Activity_Step5-4">
        <dc:Bounds x="1590" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13tlgk8" bpmnElement="Activity_Step5-3">
        <dc:Bounds x="1431" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ztdxj3_di" bpmnElement="Flow_0ztdxj3">
        <di:waypoint x="188" y="187" />
        <di:waypoint x="230" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0djdrch_di" bpmnElement="Flow_0djdrch">
        <di:waypoint x="860" y="380" />
        <di:waypoint x="942" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fyedkf_di" bpmnElement="Flow_1fyedkf">
        <di:waypoint x="330" y="187" />
        <di:waypoint x="382" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="345" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0eemaff_di" bpmnElement="Flow_0eemaff">
        <di:waypoint x="482" y="187" />
        <di:waypoint x="540" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="500" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x50132_di" bpmnElement="Flow_0x50132">
        <di:waypoint x="746" y="187" />
        <di:waypoint x="787" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="750" y="169" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0somi1n_di" bpmnElement="Flow_0somi1n">
        <di:waypoint x="721" y="162" />
        <di:waypoint x="721" y="100" />
        <di:waypoint x="998" y="100" />
        <di:waypoint x="998" y="147" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="838" y="82" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s6bqya_di" bpmnElement="Flow_1s6bqya">
        <di:waypoint x="432" y="227" />
        <di:waypoint x="432" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="441" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bdhwwf_di" bpmnElement="Flow_0bdhwwf">
        <di:waypoint x="260" y="227" />
        <di:waypoint x="260" y="460" />
        <di:waypoint x="810" y="460" />
        <di:waypoint x="810" y="420" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="229" y="249" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cmkxg0_di" bpmnElement="Flow_0cmkxg0">
        <di:waypoint x="837" y="227" />
        <di:waypoint x="837" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="846" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03lz3q1_di" bpmnElement="Flow_03lz3q1">
        <di:waypoint x="887" y="187" />
        <di:waypoint x="948" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="907" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04xybqa_di" bpmnElement="Flow_04xybqa">
        <di:waypoint x="998" y="227" />
        <di:waypoint x="998" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1007" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02mn17s_di" bpmnElement="Flow_02mn17s">
        <di:waypoint x="640" y="187" />
        <di:waypoint x="696" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="657" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pi0och_di" bpmnElement="Flow_0pi0och">
        <di:waypoint x="590" y="227" />
        <di:waypoint x="590" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="599" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1izwis9_di" bpmnElement="Flow_1izwis9">
        <di:waypoint x="510" y="380" />
        <di:waypoint x="580" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="534" y="362" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r8b04c_di" bpmnElement="Flow_1r8b04c">
        <di:waypoint x="680" y="380" />
        <di:waypoint x="760" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="709" y="362" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y52lpr_di" bpmnElement="Flow_0y52lpr">
        <di:waypoint x="1640" y="227" />
        <di:waypoint x="1640" y="310" />
        <di:waypoint x="460" y="310" />
        <di:waypoint x="460" y="340" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1039" y="292" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ptnn2g_di" bpmnElement="Flow_0ptnn2g">
        <di:waypoint x="1048" y="187" />
        <di:waypoint x="1110" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1069" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ifrl68_di" bpmnElement="Flow_0ifrl68">
        <di:waypoint x="1160" y="227" />
        <di:waypoint x="1160" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1169" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s0s8hj_di" bpmnElement="Flow_1s0s8hj">
        <di:waypoint x="1210" y="187" />
        <di:waypoint x="1270" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xmvjg9_di" bpmnElement="Flow_0xmvjg9">
        <di:waypoint x="1370" y="187" />
        <di:waypoint x="1431" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0agazjl_di" bpmnElement="Flow_0agazjl">
        <di:waypoint x="1531" y="187" />
        <di:waypoint x="1590" y="187" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
