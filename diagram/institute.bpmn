<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0q6bcw9" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="Institute" name="制定" isExecutable="true" camunda:versionTag="1">
    <bpmn:extensionElements />
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0lm2wgn</bpmn:incoming>
      <bpmn:incoming>Flow_11soe7m</bpmn:incoming>
      <bpmn:outgoing>Flow_169byni</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="EndEvent_1">
      <bpmn:incoming>Flow_169byni</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:userTask id="Activity_Step3" name="部門主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0c09t4b</bpmn:incoming>
      <bpmn:outgoing>Flow_1ojya72</bpmn:outgoing>
      <bpmn:outgoing>Flow_00v1ck8</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step4" name="相關主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_10szad8</bpmn:incoming>
      <bpmn:outgoing>Flow_0gger45</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ajg4va</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="form_data_countersigners" camunda:elementVariable="user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${countersigners_reject_count &gt; 0 || nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step5" name="質量管理">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0ix0zja</bpmn:incoming>
      <bpmn:incoming>Flow_0gger45</bpmn:incoming>
      <bpmn:outgoing>Flow_0o5817j</bpmn:outgoing>
      <bpmn:outgoing>Flow_11b62zo</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_169byni" sourceRef="Activity_End" targetRef="EndEvent_1" />
    <bpmn:startEvent id="StartEvent_1" name="起單">
      <bpmn:outgoing>Flow_071sd16</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_071sd16</bpmn:incoming>
      <bpmn:incoming>Flow_043hdtd</bpmn:incoming>
      <bpmn:incoming>Flow_1ojya72</bpmn:incoming>
      <bpmn:incoming>Flow_11b62zo</bpmn:incoming>
      <bpmn:incoming>Flow_1ajg4va</bpmn:incoming>
      <bpmn:incoming>Flow_1c96pbk</bpmn:incoming>
      <bpmn:incoming>Flow_0dqqj6c</bpmn:incoming>
      <bpmn:outgoing>Flow_0lm2wgn</bpmn:outgoing>
      <bpmn:outgoing>Flow_0iadsmk</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_071sd16" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_0lm2wgn" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0gger45" name="核准" sourceRef="Activity_Step4" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0o5817j" name="核准" sourceRef="Activity_Step5" targetRef="Activity_Step6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_11soe7m" name="核准" sourceRef="Activity_Step7-1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ojya72" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ajg4va" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_11b62zo" name="駁回" sourceRef="Activity_Step5" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step2" name="直屬主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0iadsmk</bpmn:incoming>
      <bpmn:outgoing>Flow_043hdtd</bpmn:outgoing>
      <bpmn:outgoing>Flow_0c09t4b</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_043hdtd" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0c09t4b" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0iadsmk" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_11lvr2q">
      <bpmn:incoming>Flow_00v1ck8</bpmn:incoming>
      <bpmn:outgoing>Flow_10szad8</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ix0zja</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_00v1ck8" name="核准" sourceRef="Activity_Step3" targetRef="Gateway_11lvr2q">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10szad8" name="需會簽" sourceRef="Gateway_11lvr2q" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "Y"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ix0zja" name="不需會簽" sourceRef="Gateway_11lvr2q" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "N"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step6" name="管理代表">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0o5817j</bpmn:incoming>
      <bpmn:outgoing>Flow_1uuuxgq</bpmn:outgoing>
      <bpmn:outgoing>Flow_1c96pbk</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1uuuxgq" name="核准" sourceRef="Activity_Step6" targetRef="Activity_Step7-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1c96pbk" name="駁回" sourceRef="Activity_Step6" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0dqqj6c" name="駁回" sourceRef="Activity_Step7-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step7-1" name="文管發行">
      <bpmn:incoming>Flow_1uuuxgq</bpmn:incoming>
      <bpmn:outgoing>Flow_0dqqj6c</bpmn:outgoing>
      <bpmn:outgoing>Flow_11soe7m</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step7-1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Institute">
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="1120" y="360" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0aobyxt_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="1152" y="492" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ayks9o_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="740" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0c4vj3q_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="1120" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ks42mb_di" bpmnElement="Activity_Step5">
        <dc:Bounds x="1350" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0k77fwg" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="192" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="235" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="290" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="520" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_11lvr2q_di" bpmnElement="Gateway_11lvr2q" isMarkerVisible="true">
        <dc:Bounds x="955" y="185" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_016d2t2_di" bpmnElement="Activity_Step6">
        <dc:Bounds x="1550" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16vvv94" bpmnElement="Activity_Step7-1">
        <dc:Bounds x="1750" y="170" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_169byni_di" bpmnElement="Flow_169byni">
        <di:waypoint x="1170" y="440" />
        <di:waypoint x="1170" y="492" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_071sd16_di" bpmnElement="Flow_071sd16">
        <di:waypoint x="188" y="210" />
        <di:waypoint x="290" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lm2wgn_di" bpmnElement="Flow_0lm2wgn">
        <di:waypoint x="310" y="250" />
        <di:waypoint x="310" y="400" />
        <di:waypoint x="1120" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="314" y="283" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gger45_di" bpmnElement="Flow_0gger45">
        <di:waypoint x="1220" y="210" />
        <di:waypoint x="1350" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1274" y="192" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0o5817j_di" bpmnElement="Flow_0o5817j">
        <di:waypoint x="1450" y="210" />
        <di:waypoint x="1550" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1489" y="192" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11soe7m_di" bpmnElement="Flow_11soe7m">
        <di:waypoint x="1820" y="250" />
        <di:waypoint x="1820" y="400" />
        <di:waypoint x="1220" y="400" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1824" y="283" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ojya72_di" bpmnElement="Flow_1ojya72">
        <di:waypoint x="790" y="250" />
        <di:waypoint x="790" y="320" />
        <di:waypoint x="360" y="320" />
        <di:waypoint x="360" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="799" y="283" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ajg4va_di" bpmnElement="Flow_1ajg4va">
        <di:waypoint x="1170" y="250" />
        <di:waypoint x="1170" y="320" />
        <di:waypoint x="360" y="320" />
        <di:waypoint x="360" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1179" y="283" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11b62zo_di" bpmnElement="Flow_11b62zo">
        <di:waypoint x="1400" y="250" />
        <di:waypoint x="1400" y="320" />
        <di:waypoint x="360" y="320" />
        <di:waypoint x="360" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1409" y="283" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_043hdtd_di" bpmnElement="Flow_043hdtd">
        <di:waypoint x="570" y="250" />
        <di:waypoint x="570" y="320" />
        <di:waypoint x="360" y="320" />
        <di:waypoint x="360" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="579" y="283" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0c09t4b_di" bpmnElement="Flow_0c09t4b">
        <di:waypoint x="620" y="210" />
        <di:waypoint x="740" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="669" y="192" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0iadsmk_di" bpmnElement="Flow_0iadsmk">
        <di:waypoint x="390" y="210" />
        <di:waypoint x="520" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="444" y="192" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00v1ck8_di" bpmnElement="Flow_00v1ck8">
        <di:waypoint x="840" y="210" />
        <di:waypoint x="955" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="886" y="192" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10szad8_di" bpmnElement="Flow_10szad8">
        <di:waypoint x="1005" y="210" />
        <di:waypoint x="1120" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1047" y="192" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ix0zja_di" bpmnElement="Flow_0ix0zja">
        <di:waypoint x="980" y="185" />
        <di:waypoint x="980" y="100" />
        <di:waypoint x="1400" y="100" />
        <di:waypoint x="1400" y="170" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1168" y="82" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uuuxgq_di" bpmnElement="Flow_1uuuxgq">
        <di:waypoint x="1650" y="210" />
        <di:waypoint x="1750" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1689" y="192" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1c96pbk_di" bpmnElement="Flow_1c96pbk">
        <di:waypoint x="1600" y="250" />
        <di:waypoint x="1600" y="320" />
        <di:waypoint x="360" y="320" />
        <di:waypoint x="360" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1619" y="283" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dqqj6c_di" bpmnElement="Flow_0dqqj6c">
        <di:waypoint x="1780" y="250" />
        <di:waypoint x="1780" y="320" />
        <di:waypoint x="360" y="320" />
        <di:waypoint x="360" y="250" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1789" y="283" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
