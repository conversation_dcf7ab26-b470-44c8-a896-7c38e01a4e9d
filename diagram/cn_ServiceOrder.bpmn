<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0og4ys1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="CN_ServiceOrder" name="服务单" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0at6wdn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_Finished" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0nm7ihb</bpmn:incoming>
      <bpmn:incoming>Flow_1q5hbke</bpmn:incoming>
      <bpmn:incoming>Flow_0wazcm6</bpmn:incoming>
      <bpmn:incoming>Flow_14whylr</bpmn:incoming>
      <bpmn:incoming>Flow_1fplafw</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdhhmm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1sklbmc" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateThrowEvent id="Event_02b5csr">
      <bpmn:incoming>Flow_1gdhhmm</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1gdhhmm" sourceRef="Activity_Finished" targetRef="Event_02b5csr" />
    <bpmn:serviceTask id="Activity_Start" name="开始" camunda:type="external" camunda:topic="common:task:start">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0at6wdn</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0p54t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0at6wdn" sourceRef="StartEvent_1" targetRef="Activity_Start" />
    <bpmn:sequenceFlow id="Flow_0f0p54t" sourceRef="Activity_Start" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_0oc8paj" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step2-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1q5hbke" name="驳回" sourceRef="Activity_Step1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0nm7ihb" name="驳回" sourceRef="Activity_Step2" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step2" name="服务单申请人">
      <bpmn:incoming>Flow_1sklbmc</bpmn:incoming>
      <bpmn:outgoing>Flow_0nm7ihb</bpmn:outgoing>
      <bpmn:outgoing>Flow_0oc8paj</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step2_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step3" name="指派部门主管">
      <bpmn:incoming>Flow_07l1xrl</bpmn:incoming>
      <bpmn:outgoing>Flow_0wazcm6</bpmn:outgoing>
      <bpmn:outgoing>Flow_14whylr</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0wazcm6" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_14whylr" name="驳回" sourceRef="Activity_Step3" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step1" name="服务建立人">
      <bpmn:incoming>Flow_0f0p54t</bpmn:incoming>
      <bpmn:outgoing>Flow_1sklbmc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1q5hbke</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step2-1" name="服务单申请人主管">
      <bpmn:incoming>Flow_0oc8paj</bpmn:incoming>
      <bpmn:outgoing>Flow_07l1xrl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1fplafw</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step2-1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_07l1xrl" name="核准" sourceRef="Activity_Step2-1" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1fplafw" name="驳回" sourceRef="Activity_Step2-1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmn:error id="Error_1ehy8wa" name="Error_2ot71qf" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CN_ServiceOrder">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="153" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0o1w0z6" bpmnElement="Activity_Start">
        <dc:Bounds x="241" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0pwf7gp" bpmnElement="Activity_Step2">
        <dc:Bounds x="551" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0s19g8z" bpmnElement="Activity_Step1">
        <dc:Bounds x="390" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0s10yxh" bpmnElement="Activity_Step3">
        <dc:Bounds x="880" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_Finished">
        <dc:Bounds x="1050" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02b5csr_di" bpmnElement="Event_02b5csr">
        <dc:Bounds x="1192" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qc3iq1" bpmnElement="Activity_Step2-1">
        <dc:Bounds x="720" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1sklbmc_di" bpmnElement="Flow_1sklbmc">
        <di:waypoint x="490" y="117" />
        <di:waypoint x="551" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="510" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0at6wdn_di" bpmnElement="Flow_0at6wdn">
        <di:waypoint x="189" y="117" />
        <di:waypoint x="241" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f0p54t_di" bpmnElement="Flow_0f0p54t">
        <di:waypoint x="341" y="117" />
        <di:waypoint x="390" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oc8paj_di" bpmnElement="Flow_0oc8paj">
        <di:waypoint x="651" y="117" />
        <di:waypoint x="720" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="677" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1q5hbke_di" bpmnElement="Flow_1q5hbke">
        <di:waypoint x="440" y="157" />
        <di:waypoint x="440" y="230" />
        <di:waypoint x="1100" y="230" />
        <di:waypoint x="1100" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="449" y="193" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nm7ihb_di" bpmnElement="Flow_0nm7ihb">
        <di:waypoint x="601" y="157" />
        <di:waypoint x="601" y="230" />
        <di:waypoint x="1100" y="230" />
        <di:waypoint x="1100" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="620" y="193" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wazcm6_di" bpmnElement="Flow_0wazcm6">
        <di:waypoint x="980" y="117" />
        <di:waypoint x="1050" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1004" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14whylr_di" bpmnElement="Flow_14whylr">
        <di:waypoint x="930" y="157" />
        <di:waypoint x="930" y="230" />
        <di:waypoint x="1100" y="230" />
        <di:waypoint x="1100" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="949" y="193" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gdhhmm_di" bpmnElement="Flow_1gdhhmm">
        <di:waypoint x="1150" y="117" />
        <di:waypoint x="1192" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07l1xrl_di" bpmnElement="Flow_07l1xrl">
        <di:waypoint x="820" y="117" />
        <di:waypoint x="880" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="839" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fplafw_di" bpmnElement="Flow_1fplafw">
        <di:waypoint x="770" y="157" />
        <di:waypoint x="770" y="230" />
        <di:waypoint x="1100" y="230" />
        <di:waypoint x="1100" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="789" y="193" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
