<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_01hqf7g" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.3.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="GMP_Customer_Review" name="首营客户审核" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_120vhj9</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_Start" name="开始" camunda:type="external" camunda:topic="common:task:start">
      <bpmn:incoming>Flow_120vhj9</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0p54t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Activity_Step1" name="起单人确认">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">initiator</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0f0p54t</bpmn:incoming>
      <bpmn:outgoing>Flow_07cosf9</bpmn:outgoing>
      <bpmn:outgoing>Flow_1yqp6gc</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_07cosf9</bpmn:incoming>
      <bpmn:incoming>Flow_0oiyrbm</bpmn:incoming>
      <bpmn:incoming>Flow_0vrn8ai</bpmn:incoming>
      <bpmn:incoming>Flow_0a4b2qd</bpmn:incoming>
      <bpmn:outgoing>Flow_1686e31</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0f0p54t" sourceRef="Activity_Start" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_07cosf9" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="质量管理主管">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">specify_activity_user</camunda:inputParameter>
          <camunda:inputParameter name="user_code">C0243</camunda:inputParameter>
          <camunda:inputParameter name="dept_code">PD</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ck6nnp</bpmn:incoming>
      <bpmn:outgoing>Flow_0oiyrbm</bpmn:outgoing>
      <bpmn:outgoing>Flow_0a4b2qd</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1yqp6gc" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_120vhj9" sourceRef="StartEvent_1" targetRef="Activity_Start" />
    <bpmn:intermediateThrowEvent id="Event_0fahcln">
      <bpmn:incoming>Flow_1686e31</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1686e31" sourceRef="Activity_End" targetRef="Event_0fahcln" />
    <bpmn:userTask id="Activity_Step2" name="直属主管">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">direct_supervisor</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1yqp6gc</bpmn:incoming>
      <bpmn:outgoing>Flow_1ck6nnp</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vrn8ai</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0oiyrbm" name="核准" sourceRef="Activity_Step3" targetRef="Activity_End" />
    <bpmn:sequenceFlow id="Flow_1ck6nnp" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0vrn8ai" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0a4b2qd" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="GMP_Customer_Review">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0o1w0z6" bpmnElement="Activity_Start">
        <dc:Bounds x="250" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="420" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="950" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1i7b1kf_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="770" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0fahcln_di" bpmnElement="Event_0fahcln">
        <dc:Bounds x="1112" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="590" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0f0p54t_di" bpmnElement="Flow_0f0p54t">
        <di:waypoint x="350" y="117" />
        <di:waypoint x="420" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07cosf9_di" bpmnElement="Flow_07cosf9">
        <di:waypoint x="470" y="157" />
        <di:waypoint x="470" y="240" />
        <di:waypoint x="1000" y="240" />
        <di:waypoint x="1000" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="479" y="183" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yqp6gc_di" bpmnElement="Flow_1yqp6gc">
        <di:waypoint x="520" y="117" />
        <di:waypoint x="590" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="544" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_120vhj9_di" bpmnElement="Flow_120vhj9">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="250" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1686e31_di" bpmnElement="Flow_1686e31">
        <di:waypoint x="1050" y="117" />
        <di:waypoint x="1112" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oiyrbm_di" bpmnElement="Flow_0oiyrbm">
        <di:waypoint x="870" y="117" />
        <di:waypoint x="950" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="899" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ck6nnp_di" bpmnElement="Flow_1ck6nnp">
        <di:waypoint x="690" y="117" />
        <di:waypoint x="770" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="719" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vrn8ai_di" bpmnElement="Flow_0vrn8ai">
        <di:waypoint x="640" y="157" />
        <di:waypoint x="640" y="240" />
        <di:waypoint x="1000" y="240" />
        <di:waypoint x="1000" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="659" y="183" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a4b2qd_di" bpmnElement="Flow_0a4b2qd">
        <di:waypoint x="820" y="157" />
        <di:waypoint x="820" y="240" />
        <di:waypoint x="1000" y="240" />
        <di:waypoint x="1000" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="839" y="183" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
