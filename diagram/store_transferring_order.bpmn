<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0qnghkn" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.3.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="Store_Transferring_Order" name="門市調撥單" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_1x52ezg</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">initiator</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1x52ezg</bpmn:incoming>
      <bpmn:incoming>Flow_0y4yr9q</bpmn:incoming>
      <bpmn:incoming>Flow_0mr4uda</bpmn:incoming>
      <bpmn:incoming>Flow_0hdwiqn</bpmn:incoming>
      <bpmn:outgoing>Flow_0mq0dph</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hg7q5e</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step2" name="直屬主管">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">direct_supervisor</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0mq0dph</bpmn:incoming>
      <bpmn:outgoing>Flow_0y4yr9q</bpmn:outgoing>
      <bpmn:outgoing>Flow_0tkn27y</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1x52ezg" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_0mq0dph" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0y4yr9q" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0hg7q5e</bpmn:incoming>
      <bpmn:incoming>Flow_0yar6zv</bpmn:incoming>
      <bpmn:outgoing>Flow_0642s2s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0hg7q5e" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0tkn27y" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateThrowEvent id="Event_14d4gey">
      <bpmn:incoming>Flow_0642s2s</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0642s2s" sourceRef="Activity_End" targetRef="Event_14d4gey" />
    <bpmn:sequenceFlow id="Flow_0yar6zv" sourceRef="Activity_Step4" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0mr4uda" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="展品經理">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">specify_category_user</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0tkn27y</bpmn:incoming>
      <bpmn:outgoing>Flow_16zei21</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hdwiqn</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step4" name="通路營運主管">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">specify_category_user</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_16zei21</bpmn:incoming>
      <bpmn:outgoing>Flow_0mr4uda</bpmn:outgoing>
      <bpmn:outgoing>Flow_0yar6zv</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_16zei21" sourceRef="Activity_Step3" targetRef="Activity_Step4" />
    <bpmn:sequenceFlow id="Flow_0hdwiqn" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Store_Transferring_Order">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="300" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="500" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1i7b1kf_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="710" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14d4gey_di" bpmnElement="Event_14d4gey">
        <dc:Bounds x="742" y="392" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gky7gq" bpmnElement="Activity_Step4">
        <dc:Bounds x="920" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="710" y="250" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1x52ezg_di" bpmnElement="Flow_1x52ezg">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="300" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y4yr9q_di" bpmnElement="Flow_0y4yr9q">
        <di:waypoint x="550" y="157" />
        <di:waypoint x="550" y="220" />
        <di:waypoint x="370" y="220" />
        <di:waypoint x="370" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="559" y="164" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mr4uda_di" bpmnElement="Flow_0mr4uda">
        <di:waypoint x="970" y="160" />
        <di:waypoint x="970" y="220" />
        <di:waypoint x="370" y="220" />
        <di:waypoint x="370" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="979" y="170" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hdwiqn_di" bpmnElement="Flow_0hdwiqn">
        <di:waypoint x="760" y="160" />
        <di:waypoint x="760" y="220" />
        <di:waypoint x="370" y="220" />
        <di:waypoint x="370" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="170" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mq0dph_di" bpmnElement="Flow_0mq0dph">
        <di:waypoint x="400" y="117" />
        <di:waypoint x="500" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="439" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0hg7q5e_di" bpmnElement="Flow_0hg7q5e">
        <di:waypoint x="340" y="157" />
        <di:waypoint x="340" y="290" />
        <di:waypoint x="710" y="290" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="309" y="173" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tkn27y_di" bpmnElement="Flow_0tkn27y">
        <di:waypoint x="600" y="120" />
        <di:waypoint x="710" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16zei21_di" bpmnElement="Flow_16zei21">
        <di:waypoint x="810" y="120" />
        <di:waypoint x="920" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yar6zv_di" bpmnElement="Flow_0yar6zv">
        <di:waypoint x="1020" y="120" />
        <di:waypoint x="1060" y="120" />
        <di:waypoint x="1060" y="290" />
        <di:waypoint x="810" y="290" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0642s2s_di" bpmnElement="Flow_0642s2s">
        <di:waypoint x="760" y="330" />
        <di:waypoint x="760" y="392" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
