<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0og4ys1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="CN_ForeignRequisition" name="国外请购" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0at6wdn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_Finished" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_1q5hbke</bpmn:incoming>
      <bpmn:incoming>Flow_1dkalj7</bpmn:incoming>
      <bpmn:incoming>Flow_1wj65rc</bpmn:incoming>
      <bpmn:incoming>Flow_0y3lic2</bpmn:incoming>
      <bpmn:incoming>Flow_08jasmc</bpmn:incoming>
      <bpmn:incoming>Flow_09ta2w5</bpmn:incoming>
      <bpmn:incoming>Flow_1ywta1r</bpmn:incoming>
      <bpmn:incoming>Flow_1ide06s</bpmn:incoming>
      <bpmn:incoming>Flow_0drydst</bpmn:incoming>
      <bpmn:incoming>Flow_117buma</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdhhmm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_02b5csr">
      <bpmn:incoming>Flow_1gdhhmm</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:serviceTask id="Activity_Start" name="开始" camunda:type="external" camunda:topic="common:task:start">
      <bpmn:incoming>Flow_0at6wdn</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0p54t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Activity_Step4" name="营运长">
      <bpmn:incoming>Flow_1l7612x</bpmn:incoming>
      <bpmn:outgoing>Flow_1dkalj7</bpmn:outgoing>
      <bpmn:outgoing>Flow_1m21ptg</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step3" name="大区经理 / &#10;事业单位、财务 、市场部主管">
      <bpmn:incoming>Flow_033s3sq</bpmn:incoming>
      <bpmn:outgoing>Flow_1wj65rc</bpmn:outgoing>
      <bpmn:outgoing>Flow_0hnc2cl</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step1" name="业务">
      <bpmn:incoming>Flow_0f0p54t</bpmn:incoming>
      <bpmn:outgoing>Flow_1q5hbke</bpmn:outgoing>
      <bpmn:outgoing>Flow_1sklbmc</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step5" name="总经理">
      <bpmn:incoming>Flow_1wct1tl</bpmn:incoming>
      <bpmn:outgoing>Flow_0y3lic2</bpmn:outgoing>
      <bpmn:outgoing>Flow_1v9t41p</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_0el6rcx">
      <bpmn:incoming>Flow_1sklbmc</bpmn:incoming>
      <bpmn:outgoing>Flow_1ywta1r</bpmn:outgoing>
      <bpmn:outgoing>Flow_033s3sq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1xkckj4">
      <bpmn:incoming>Flow_0hnc2cl</bpmn:incoming>
      <bpmn:outgoing>Flow_1ide06s</bpmn:outgoing>
      <bpmn:outgoing>Flow_1l7612x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0hsj2j1">
      <bpmn:incoming>Flow_1m21ptg</bpmn:incoming>
      <bpmn:outgoing>Flow_0drydst</bpmn:outgoing>
      <bpmn:outgoing>Flow_1wct1tl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1878713">
      <bpmn:incoming>Flow_1v9t41p</bpmn:incoming>
      <bpmn:outgoing>Flow_117buma</bpmn:outgoing>
      <bpmn:outgoing>Flow_0koo2wf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Activity_Step6" name="执行长">
      <bpmn:incoming>Flow_0koo2wf</bpmn:incoming>
      <bpmn:outgoing>Flow_08jasmc</bpmn:outgoing>
      <bpmn:outgoing>Flow_09ta2w5</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0at6wdn" sourceRef="StartEvent_1" targetRef="Activity_Start" />
    <bpmn:sequenceFlow id="Flow_1q5hbke" name="驳回" sourceRef="Activity_Step1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1dkalj7" name="驳回" sourceRef="Activity_Step4" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wj65rc" name="驳回" sourceRef="Activity_Step3" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0y3lic2" name="驳回" sourceRef="Activity_Step5" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08jasmc" name="核准" sourceRef="Activity_Step6" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_09ta2w5" name="驳回" sourceRef="Activity_Step6" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ywta1r" sourceRef="Gateway_0el6rcx" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${1== 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ide06s" sourceRef="Gateway_1xkckj4" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${1 == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0drydst" sourceRef="Gateway_0hsj2j1" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${1 == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_117buma" sourceRef="Gateway_1878713" targetRef="Activity_Finished">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${1 == 2}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1gdhhmm" sourceRef="Activity_Finished" targetRef="Event_02b5csr" />
    <bpmn:sequenceFlow id="Flow_0f0p54t" sourceRef="Activity_Start" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_1m21ptg" name="核准" sourceRef="Activity_Step4" targetRef="Gateway_0hsj2j1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_033s3sq" name="5,000 以下&#10;(USD)" sourceRef="Gateway_0el6rcx" targetRef="Activity_Step3" />
    <bpmn:sequenceFlow id="Flow_0hnc2cl" name="核准" sourceRef="Activity_Step3" targetRef="Gateway_1xkckj4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1sklbmc" name="核准" sourceRef="Activity_Step1" targetRef="Gateway_0el6rcx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1wct1tl" name="10,001 - 30,000&#10;(USD)" sourceRef="Gateway_0hsj2j1" targetRef="Activity_Step5" />
    <bpmn:sequenceFlow id="Flow_1v9t41p" name="核准" sourceRef="Activity_Step5" targetRef="Gateway_1878713">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0koo2wf" name="30,001 以上&#10;(USD)" sourceRef="Gateway_1878713" targetRef="Activity_Step6" />
    <bpmn:sequenceFlow id="Flow_1l7612x" name="5,001-10,000&#10;(USD)" sourceRef="Gateway_1xkckj4" targetRef="Activity_Step4" />
  </bpmn:process>
  <bpmn:error id="Error_1ehy8wa" name="Error_2ot71qf" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="CN_ForeignRequisition">
      <bpmndi:BPMNShape id="BPMNShape_0pcd0tl" bpmnElement="StartEvent_1">
        <dc:Bounds x="132" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0cstmgl" bpmnElement="Activity_Finished">
        <dc:Bounds x="1730" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rlwa38" bpmnElement="Event_02b5csr">
        <dc:Bounds x="1869" y="172" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_13512ie" bpmnElement="Activity_Start">
        <dc:Bounds x="220" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0s2c3pa" bpmnElement="Activity_Step4">
        <dc:Bounds x="970" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1hdx4zm" bpmnElement="Activity_Step3">
        <dc:Bounds x="660" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bbki6t" bpmnElement="Activity_Step1">
        <dc:Bounds x="370" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0zibd2f" bpmnElement="Activity_Step5">
        <dc:Bounds x="1270" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1jkqvdp" bpmnElement="Gateway_0el6rcx" isMarkerVisible="true">
        <dc:Bounds x="525" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1o2822k" bpmnElement="Gateway_1xkckj4" isMarkerVisible="true">
        <dc:Bounds x="825" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1323zbh" bpmnElement="Gateway_0hsj2j1" isMarkerVisible="true">
        <dc:Bounds x="1125" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1wecdc8" bpmnElement="Gateway_1878713" isMarkerVisible="true">
        <dc:Bounds x="1424" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ob638t" bpmnElement="Activity_Step6">
        <dc:Bounds x="1570" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_0ku28c4" bpmnElement="Flow_0at6wdn">
        <di:waypoint x="168" y="190" />
        <di:waypoint x="220" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ak2gqr" bpmnElement="Flow_1q5hbke">
        <di:waypoint x="420" y="230" />
        <di:waypoint x="420" y="303" />
        <di:waypoint x="1780" y="303" />
        <di:waypoint x="1780" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="439" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0y6ma8c" bpmnElement="Flow_1dkalj7">
        <di:waypoint x="1020" y="230" />
        <di:waypoint x="1020" y="303" />
        <di:waypoint x="1780" y="303" />
        <di:waypoint x="1780" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1039" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ipj2xo" bpmnElement="Flow_1wj65rc">
        <di:waypoint x="710" y="230" />
        <di:waypoint x="710" y="303" />
        <di:waypoint x="1780" y="303" />
        <di:waypoint x="1780" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="729" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1jw6tw1" bpmnElement="Flow_0y3lic2">
        <di:waypoint x="1320" y="230" />
        <di:waypoint x="1320" y="303" />
        <di:waypoint x="1780" y="303" />
        <di:waypoint x="1780" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1339" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_15sbj2b" bpmnElement="Flow_08jasmc">
        <di:waypoint x="1670" y="190" />
        <di:waypoint x="1730" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1689" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0x6sh0k" bpmnElement="Flow_09ta2w5">
        <di:waypoint x="1620" y="230" />
        <di:waypoint x="1620" y="303" />
        <di:waypoint x="1780" y="303" />
        <di:waypoint x="1780" y="230" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1639" y="266" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1bmn52x" bpmnElement="Flow_1ywta1r">
        <di:waypoint x="550" y="165" />
        <di:waypoint x="550" y="83" />
        <di:waypoint x="1780" y="83" />
        <di:waypoint x="1780" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="660" y="146" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_12nvspv" bpmnElement="Flow_1ide06s">
        <di:waypoint x="850" y="165" />
        <di:waypoint x="850" y="83" />
        <di:waypoint x="1780" y="83" />
        <di:waypoint x="1780" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="956" y="146" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0uhgp7j" bpmnElement="Flow_0drydst">
        <di:waypoint x="1150" y="165" />
        <di:waypoint x="1150" y="83" />
        <di:waypoint x="1780" y="83" />
        <di:waypoint x="1780" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1239" y="146" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_11ovstp" bpmnElement="Flow_117buma">
        <di:waypoint x="1449" y="165" />
        <di:waypoint x="1449" y="83" />
        <di:waypoint x="1780" y="83" />
        <di:waypoint x="1780" y="150" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1488" y="146" width="71" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0mox5o6" bpmnElement="Flow_1gdhhmm">
        <di:waypoint x="1830" y="190" />
        <di:waypoint x="1869" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0xrcv5x" bpmnElement="Flow_0f0p54t">
        <di:waypoint x="320" y="190" />
        <di:waypoint x="370" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_073aoqw" bpmnElement="Flow_1m21ptg">
        <di:waypoint x="1070" y="190" />
        <di:waypoint x="1125" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1085" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0dpd5hi" bpmnElement="Flow_033s3sq">
        <di:waypoint x="575" y="190" />
        <di:waypoint x="660" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="591" y="156" width="53" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1ptsbis" bpmnElement="Flow_0hnc2cl">
        <di:waypoint x="760" y="190" />
        <di:waypoint x="825" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="782" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_19mo1ir" bpmnElement="Flow_1sklbmc">
        <di:waypoint x="470" y="190" />
        <di:waypoint x="525" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="489" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1mm1thn" bpmnElement="Flow_1wct1tl">
        <di:waypoint x="1175" y="190" />
        <di:waypoint x="1270" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1181" y="156" width="78" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0ds9wzs" bpmnElement="Flow_1v9t41p">
        <di:waypoint x="1370" y="190" />
        <di:waypoint x="1424" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1389" y="172" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_07tereq" bpmnElement="Flow_0koo2wf">
        <di:waypoint x="1474" y="190" />
        <di:waypoint x="1570" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1493" y="156" width="59" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1gvpz7p" bpmnElement="Flow_1l7612x">
        <di:waypoint x="875" y="190" />
        <di:waypoint x="970" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="890" y="156" width="65" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
