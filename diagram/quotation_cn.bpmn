<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0og4ys1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.3.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="Quotation_CN" name="报价" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0at6wdn</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起单人确认">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">initiator</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0f0p54t</bpmn:incoming>
      <bpmn:outgoing>Flow_1sklbmc</bpmn:outgoing>
      <bpmn:outgoing>Flow_07cosf9</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="结束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_00jb8uu</bpmn:incoming>
      <bpmn:incoming>Flow_07cosf9</bpmn:incoming>
      <bpmn:incoming>Flow_16umluo</bpmn:incoming>
      <bpmn:outgoing>Flow_1gdhhmm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Activity_Step2" name="直属主管">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="assignee_type">direct_supervisor</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1sklbmc</bpmn:incoming>
      <bpmn:outgoing>Flow_00jb8uu</bpmn:outgoing>
      <bpmn:outgoing>Flow_16umluo</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1sklbmc" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_00jb8uu" name="核准" sourceRef="Activity_Step2" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_16umluo" name="驳回" sourceRef="Activity_Step2" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07cosf9" name="驳回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateThrowEvent id="Event_02b5csr">
      <bpmn:incoming>Flow_1gdhhmm</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1gdhhmm" sourceRef="Activity_End" targetRef="Event_02b5csr" />
    <bpmn:serviceTask id="Activity_Start" name="开始" camunda:type="external" camunda:topic="common:task:start">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0at6wdn</bpmn:incoming>
      <bpmn:outgoing>Flow_0f0p54t</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0at6wdn" sourceRef="StartEvent_1" targetRef="Activity_Start" />
    <bpmn:sequenceFlow id="Flow_0f0p54t" sourceRef="Activity_Start" targetRef="Activity_Step1" />
  </bpmn:process>
  <bpmn:error id="Error_1ehy8wa" name="Error_2ot71qf" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Quotation_CN">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="440" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="830" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ma9eup_di" bpmnElement="Activity_Step2">
        <dc:Bounds x="630" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_02b5csr_di" bpmnElement="Event_02b5csr">
        <dc:Bounds x="1012" y="99" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0o1w0z6" bpmnElement="Activity_Start">
        <dc:Bounds x="260" y="77" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1sklbmc_di" bpmnElement="Flow_1sklbmc">
        <di:waypoint x="540" y="117" />
        <di:waypoint x="630" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="574" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00jb8uu_di" bpmnElement="Flow_00jb8uu">
        <di:waypoint x="730" y="117" />
        <di:waypoint x="830" y="117" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="769" y="99" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16umluo_di" bpmnElement="Flow_16umluo">
        <di:waypoint x="680" y="157" />
        <di:waypoint x="680" y="210" />
        <di:waypoint x="880" y="210" />
        <di:waypoint x="880" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="689" y="183" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_07cosf9_di" bpmnElement="Flow_07cosf9">
        <di:waypoint x="470" y="157" />
        <di:waypoint x="470" y="210" />
        <di:waypoint x="880" y="210" />
        <di:waypoint x="880" y="157" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="479" y="183" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gdhhmm_di" bpmnElement="Flow_1gdhhmm">
        <di:waypoint x="930" y="117" />
        <di:waypoint x="1012" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0at6wdn_di" bpmnElement="Flow_0at6wdn">
        <di:waypoint x="188" y="117" />
        <di:waypoint x="260" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f0p54t_di" bpmnElement="Flow_0f0p54t">
        <di:waypoint x="360" y="117" />
        <di:waypoint x="440" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
