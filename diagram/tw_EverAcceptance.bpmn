<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0xtc4fd" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="TW_EverAcceptance" name="需求驗收" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_10um5rl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_10um5rl</bpmn:incoming>
      <bpmn:incoming>Flow_1o1t04z</bpmn:incoming>
      <bpmn:incoming>Flow_060xhhj</bpmn:incoming>
      <bpmn:incoming>Flow_08kzewb</bpmn:incoming>
      <bpmn:outgoing>Flow_184zwle</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rl8udi</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_184zwle</bpmn:incoming>
      <bpmn:incoming>Flow_08zu0y8</bpmn:incoming>
      <bpmn:outgoing>Flow_1idas1g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="EndEvent_1">
      <bpmn:incoming>Flow_1idas1g</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1idas1g" sourceRef="Activity_End" targetRef="EndEvent_1" />
    <bpmn:sequenceFlow id="Flow_10um5rl" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_184zwle" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rl8udi" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="需求方主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1eai9rf</bpmn:incoming>
      <bpmn:outgoing>Flow_1o1t04z</bpmn:outgoing>
      <bpmn:outgoing>Flow_08zu0y8</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step2" name="資訊部主管">
      <bpmn:incoming>Flow_0rl8udi</bpmn:incoming>
      <bpmn:outgoing>Flow_00augq9</bpmn:outgoing>
      <bpmn:outgoing>Flow_060xhhj</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step2_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_00augq9" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1o1t04z" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="需求方">
      <bpmn:incoming>Flow_00augq9</bpmn:incoming>
      <bpmn:outgoing>Flow_1eai9rf</bpmn:outgoing>
      <bpmn:outgoing>Flow_08kzewb</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1eai9rf" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_060xhhj" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08kzewb" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08zu0y8" name="核准" sourceRef="Activity_Step4" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TW_EverAcceptance">
      <bpmndi:BPMNShape id="BPMNShape_1bfxdvh" bpmnElement="Activity_Step3">
        <dc:Bounds x="593" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="250" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="593" y="220" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_128mpal_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="625" y="332" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ikhnk3" bpmnElement="Activity_Step2">
        <dc:Bounds x="420" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_19l1pne" bpmnElement="Activity_Step4">
        <dc:Bounds x="759" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_00augq9_di" bpmnElement="Flow_00augq9">
        <di:waypoint x="520" y="120" />
        <di:waypoint x="593" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="547" y="98" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eai9rf_di" bpmnElement="Flow_1eai9rf">
        <di:waypoint x="693" y="120" />
        <di:waypoint x="759" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="716" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08kzewb_di" bpmnElement="Flow_08kzewb">
        <di:waypoint x="643" y="160" />
        <di:waypoint x="643" y="200" />
        <di:waypoint x="300" y="200" />
        <di:waypoint x="300" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="609" y="173" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o1t04z_di" bpmnElement="Flow_1o1t04z">
        <di:waypoint x="809" y="160" />
        <di:waypoint x="809" y="200" />
        <di:waypoint x="300" y="200" />
        <di:waypoint x="300" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="777" y="173" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08zu0y8_di" bpmnElement="Flow_08zu0y8">
        <di:waypoint x="832" y="160" />
        <di:waypoint x="832" y="260" />
        <di:waypoint x="693" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="836" y="207" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10um5rl_di" bpmnElement="Flow_10um5rl">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="250" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_060xhhj_di" bpmnElement="Flow_060xhhj">
        <di:waypoint x="470" y="160" />
        <di:waypoint x="470" y="200" />
        <di:waypoint x="300" y="200" />
        <di:waypoint x="300" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="440" y="173" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_184zwle_di" bpmnElement="Flow_184zwle">
        <di:waypoint x="270" y="160" />
        <di:waypoint x="270" y="260" />
        <di:waypoint x="593" y="260" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="239" y="181" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rl8udi_di" bpmnElement="Flow_0rl8udi">
        <di:waypoint x="350" y="120" />
        <di:waypoint x="420" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="374" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1idas1g_di" bpmnElement="Flow_1idas1g">
        <di:waypoint x="643" y="300" />
        <di:waypoint x="643" y="332" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
