<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0xtc4fd" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="TW_EverQuest" name="需求申請" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_10um5rl</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人(PM)確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_10um5rl</bpmn:incoming>
      <bpmn:incoming>Flow_1r02zzo</bpmn:incoming>
      <bpmn:incoming>Flow_1o1t04z</bpmn:incoming>
      <bpmn:incoming>Flow_1guibrr</bpmn:incoming>
      <bpmn:incoming>Flow_060xhhj</bpmn:incoming>
      <bpmn:outgoing>Flow_184zwle</bpmn:outgoing>
      <bpmn:outgoing>Flow_0rl8udi</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_184zwle</bpmn:incoming>
      <bpmn:incoming>Flow_00augq9</bpmn:incoming>
      <bpmn:outgoing>Flow_1idas1g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="EndEvent_1">
      <bpmn:incoming>Flow_1idas1g</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_1idas1g" sourceRef="Activity_End" targetRef="EndEvent_1" />
    <bpmn:sequenceFlow id="Flow_10um5rl" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:sequenceFlow id="Flow_184zwle" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0rl8udi" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step2" name="需求方">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0rl8udi</bpmn:incoming>
      <bpmn:outgoing>Flow_0lt46k6</bpmn:outgoing>
      <bpmn:outgoing>Flow_1guibrr</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0lt46k6" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="相關單位會簽">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_099d2lb</bpmn:incoming>
      <bpmn:outgoing>Flow_04dm7lc</bpmn:outgoing>
      <bpmn:outgoing>Flow_1r02zzo</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0 || approve == false}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_04dm7lc" name="核准" sourceRef="Activity_Step4" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3" name="需求方主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0lt46k6</bpmn:incoming>
      <bpmn:outgoing>Flow_0icvtqn</bpmn:outgoing>
      <bpmn:outgoing>Flow_1o1t04z</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0icvtqn" name="核准" sourceRef="Activity_Step3" targetRef="Gateway_14v3g9l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step5" name="資訊部主管">
      <bpmn:incoming>Flow_10ian10</bpmn:incoming>
      <bpmn:incoming>Flow_04dm7lc</bpmn:incoming>
      <bpmn:outgoing>Flow_00augq9</bpmn:outgoing>
      <bpmn:outgoing>Flow_060xhhj</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_00augq9" name="核准" sourceRef="Activity_Step5" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1r02zzo" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1o1t04z" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1guibrr" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_060xhhj" name="駁回" sourceRef="Activity_Step5" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_14v3g9l">
      <bpmn:incoming>Flow_0icvtqn</bpmn:incoming>
      <bpmn:outgoing>Flow_099d2lb</bpmn:outgoing>
      <bpmn:outgoing>Flow_10ian10</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_099d2lb" name="需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "Y"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_10ian10" name="不需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "N"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TW_EverQuest">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="162" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="250" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="830" y="280" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_128mpal_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="862" y="392" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="410" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16o97v6_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="850" y="140" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_19l1pne" bpmnElement="Activity_Step3">
        <dc:Bounds x="570" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1ikhnk3" bpmnElement="Activity_Step5">
        <dc:Bounds x="1020" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1md3czo" bpmnElement="Gateway_14v3g9l" isMarkerVisible="true">
        <dc:Bounds x="735" y="155" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1idas1g_di" bpmnElement="Flow_1idas1g">
        <di:waypoint x="880" y="360" />
        <di:waypoint x="880" y="392" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10um5rl_di" bpmnElement="Flow_10um5rl">
        <di:waypoint x="188" y="180" />
        <di:waypoint x="250" y="180" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_184zwle_di" bpmnElement="Flow_184zwle">
        <di:waypoint x="270" y="220" />
        <di:waypoint x="270" y="320" />
        <di:waypoint x="830" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="239" y="241" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rl8udi_di" bpmnElement="Flow_0rl8udi">
        <di:waypoint x="350" y="180" />
        <di:waypoint x="410" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="369" y="162" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lt46k6_di" bpmnElement="Flow_0lt46k6">
        <di:waypoint x="510" y="180" />
        <di:waypoint x="570" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="532" y="162" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04dm7lc_di" bpmnElement="Flow_04dm7lc">
        <di:waypoint x="950" y="180" />
        <di:waypoint x="1020" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="975" y="162" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0icvtqn_di" bpmnElement="Flow_0icvtqn">
        <di:waypoint x="670" y="180" />
        <di:waypoint x="735" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="692" y="162" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_00augq9_di" bpmnElement="Flow_00augq9">
        <di:waypoint x="1090" y="220" />
        <di:waypoint x="1090" y="320" />
        <di:waypoint x="930" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1094" y="267" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r02zzo_di" bpmnElement="Flow_1r02zzo">
        <di:waypoint x="900" y="220" />
        <di:waypoint x="900" y="260" />
        <di:waypoint x="300" y="260" />
        <di:waypoint x="300" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="833" y="233" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o1t04z_di" bpmnElement="Flow_1o1t04z">
        <di:waypoint x="620" y="220" />
        <di:waypoint x="620" y="260" />
        <di:waypoint x="300" y="260" />
        <di:waypoint x="300" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="589" y="233" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1guibrr_di" bpmnElement="Flow_1guibrr">
        <di:waypoint x="460" y="220" />
        <di:waypoint x="460" y="260" />
        <di:waypoint x="300" y="260" />
        <di:waypoint x="300" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="419" y="233" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_060xhhj_di" bpmnElement="Flow_060xhhj">
        <di:waypoint x="1070" y="220" />
        <di:waypoint x="1070" y="260" />
        <di:waypoint x="300" y="260" />
        <di:waypoint x="300" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1035" y="233" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_099d2lb_di" bpmnElement="Flow_099d2lb">
        <di:waypoint x="785" y="180" />
        <di:waypoint x="850" y="180" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="801" y="162" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_10ian10_di" bpmnElement="Flow_10ian10">
        <di:waypoint x="760" y="155" />
        <di:waypoint x="760" y="100" />
        <di:waypoint x="1070" y="100" />
        <di:waypoint x="1070" y="140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="894" y="82" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
