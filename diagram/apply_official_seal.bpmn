<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_11l56v6" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="Apply_Official_Seal" name="申請合約用印" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0ztdxj3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0ztdxj3</bpmn:incoming>
      <bpmn:incoming>Flow_1s6bqya</bpmn:incoming>
      <bpmn:incoming>Flow_0cmkxg0</bpmn:incoming>
      <bpmn:incoming>Flow_04xybqa</bpmn:incoming>
      <bpmn:incoming>Flow_0pi0och</bpmn:incoming>
      <bpmn:incoming>Flow_0ifrl68</bpmn:incoming>
      <bpmn:outgoing>Flow_1fyedkf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bdhwwf</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0ztdxj3" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:userTask id="Activity_Step2" name="直屬主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1fyedkf</bpmn:incoming>
      <bpmn:outgoing>Flow_0eemaff</bpmn:outgoing>
      <bpmn:outgoing>Flow_1s6bqya</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step3" name="相關單位會簽">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0x50132</bpmn:incoming>
      <bpmn:outgoing>Flow_0cmkxg0</bpmn:outgoing>
      <bpmn:outgoing>Flow_03lz3q1</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="form_data_countersigners" camunda:elementVariable="user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${countersigners_reject_count &gt; 0 || nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0bdhwwf</bpmn:incoming>
      <bpmn:incoming>Flow_1r8b04c</bpmn:incoming>
      <bpmn:outgoing>Flow_0djdrch</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_1b020yf">
      <bpmn:incoming>Flow_0djdrch</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0djdrch" sourceRef="Activity_End" targetRef="Event_1b020yf" />
    <bpmn:exclusiveGateway id="Gateway_14v3g9l">
      <bpmn:incoming>Flow_02mn17s</bpmn:incoming>
      <bpmn:outgoing>Flow_0x50132</bpmn:outgoing>
      <bpmn:outgoing>Flow_0somi1n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fyedkf" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0eemaff" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step2-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0x50132" name="需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "Y"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0somi1n" name="不需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "N"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1s6bqya" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0bdhwwf" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0cmkxg0" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="法務">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_03lz3q1</bpmn:incoming>
      <bpmn:incoming>Flow_0somi1n</bpmn:incoming>
      <bpmn:outgoing>Flow_04xybqa</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ptnn2g</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_03lz3q1" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_04xybqa" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step2-1" name="部門主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0eemaff</bpmn:incoming>
      <bpmn:outgoing>Flow_02mn17s</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pi0och</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_02mn17s" name="核准" sourceRef="Activity_Step2-1" targetRef="Gateway_14v3g9l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0pi0och" name="駁回" sourceRef="Activity_Step2-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step7" name="起單人上傳雙方用印後合約">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1odfqj5</bpmn:incoming>
      <bpmn:incoming>Flow_0y52lpr</bpmn:incoming>
      <bpmn:outgoing>Flow_1izwis9</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_0a281hn">
      <bpmn:incoming>Flow_0ptnn2g</bpmn:incoming>
      <bpmn:outgoing>Flow_1trsu74</bpmn:outgoing>
      <bpmn:outgoing>Flow_0oxs3t7</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1d4a3yc">
      <bpmn:incoming>Flow_0oxs3t7</bpmn:incoming>
      <bpmn:incoming>Flow_18v8aaz</bpmn:incoming>
      <bpmn:outgoing>Flow_1havifm</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ty9lgl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Activity_Step6-2" name="小章">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1havifm</bpmn:incoming>
      <bpmn:outgoing>Flow_05of8gw</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1trsu74" name="有大章" sourceRef="Gateway_0a281hn" targetRef="Activity_Step6-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_officialSeal") == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0oxs3t7" name="無大章&#10;" sourceRef="Gateway_0a281hn" targetRef="Gateway_1d4a3yc" />
    <bpmn:sequenceFlow id="Flow_1havifm" name="有小章" sourceRef="Gateway_1d4a3yc" targetRef="Activity_Step6-2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_personalSeal") == true }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step8" name="法務">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1izwis9</bpmn:incoming>
      <bpmn:outgoing>Flow_1r8b04c</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step8_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1izwis9" name="核准" sourceRef="Activity_Step7" targetRef="Activity_Step8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1r8b04c" name="核准" sourceRef="Activity_Step8" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0m7gg50">
      <bpmn:incoming>Flow_05of8gw</bpmn:incoming>
      <bpmn:incoming>Flow_1ty9lgl</bpmn:incoming>
      <bpmn:outgoing>Flow_1ie8ker</bpmn:outgoing>
      <bpmn:outgoing>Flow_08ryfu8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_05of8gw" sourceRef="Activity_Step6-2" targetRef="Gateway_0m7gg50">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ty9lgl" name="無小章" sourceRef="Gateway_1d4a3yc" targetRef="Gateway_0m7gg50" />
    <bpmn:sequenceFlow id="Flow_1ie8ker" name="有騎縫章" sourceRef="Gateway_0m7gg50" targetRef="Activity_Step6-3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_pagingSeal") == true }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step6-3" name="騎縫章">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1ie8ker</bpmn:incoming>
      <bpmn:outgoing>Flow_0247zq3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="Gateway_1f77dd6">
      <bpmn:incoming>Flow_0247zq3</bpmn:incoming>
      <bpmn:incoming>Flow_08ryfu8</bpmn:incoming>
      <bpmn:outgoing>Flow_1odfqj5</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ymobqx</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0247zq3" sourceRef="Activity_Step6-3" targetRef="Gateway_1f77dd6">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_08ryfu8" name="無騎縫章" sourceRef="Gateway_0m7gg50" targetRef="Gateway_1f77dd6" />
    <bpmn:userTask id="Activity_Step6-4" name="戳記">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0ymobqx</bpmn:incoming>
      <bpmn:outgoing>Flow_0y52lpr</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1odfqj5" name="無戳記" sourceRef="Gateway_1f77dd6" targetRef="Activity_Step7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_stampSeal") == false }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0y52lpr" sourceRef="Activity_Step6-4" targetRef="Activity_Step7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ymobqx" name="有戳記" sourceRef="Gateway_1f77dd6" targetRef="Activity_Step6-4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_stampSeal") == true }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step6-1" name="大章">
      <bpmn:incoming>Flow_1trsu74</bpmn:incoming>
      <bpmn:outgoing>Flow_18v8aaz</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ifrl68</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6-1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_18v8aaz" sourceRef="Activity_Step6-1" targetRef="Gateway_1d4a3yc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ptnn2g" name="核准" sourceRef="Activity_Step4" targetRef="Gateway_0a281hn">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ifrl68" name="駁回" sourceRef="Activity_Step6-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Apply_Official_Seal">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="169" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="230" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="382" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16o97v6_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="787" y="147" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="760" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1b020yf_di" bpmnElement="Event_1b020yf">
        <dc:Bounds x="942" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14v3g9l_di" bpmnElement="Gateway_14v3g9l" isMarkerVisible="true">
        <dc:Bounds x="696" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_060p6dy_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="948" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ayks9o_di" bpmnElement="Activity_Step2-1">
        <dc:Bounds x="540" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0yi3ol4" bpmnElement="Activity_Step7">
        <dc:Bounds x="410" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0a281hn_di" bpmnElement="Gateway_0a281hn" isMarkerVisible="true">
        <dc:Bounds x="1105" y="162" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="341" y="123" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1d4a3yc_di" bpmnElement="Gateway_1d4a3yc" isMarkerVisible="true">
        <dc:Bounds x="1365" y="162" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="709" y="162" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_07konzk_di" bpmnElement="Activity_Step6-2">
        <dc:Bounds x="1460" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10i184b" bpmnElement="Activity_Step8">
        <dc:Bounds x="580" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_18s73q0" bpmnElement="Gateway_0m7gg50" isMarkerVisible="true">
        <dc:Bounds x="1606" y="162" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="709" y="162" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1f3nmrv_di" bpmnElement="Activity_Step6-3">
        <dc:Bounds x="1701" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1f77dd6_di" bpmnElement="Gateway_1f77dd6" isMarkerVisible="true">
        <dc:Bounds x="1846" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1w586dj_di" bpmnElement="Activity_Step6-4">
        <dc:Bounds x="1941" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16h17t2" bpmnElement="Activity_Step6-1">
        <dc:Bounds x="1220" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ztdxj3_di" bpmnElement="Flow_0ztdxj3">
        <di:waypoint x="188" y="187" />
        <di:waypoint x="230" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0djdrch_di" bpmnElement="Flow_0djdrch">
        <di:waypoint x="860" y="380" />
        <di:waypoint x="942" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fyedkf_di" bpmnElement="Flow_1fyedkf">
        <di:waypoint x="330" y="187" />
        <di:waypoint x="382" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="345" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0eemaff_di" bpmnElement="Flow_0eemaff">
        <di:waypoint x="482" y="187" />
        <di:waypoint x="540" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="500" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x50132_di" bpmnElement="Flow_0x50132">
        <di:waypoint x="746" y="187" />
        <di:waypoint x="787" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="750" y="169" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0somi1n_di" bpmnElement="Flow_0somi1n">
        <di:waypoint x="721" y="162" />
        <di:waypoint x="721" y="100" />
        <di:waypoint x="998" y="100" />
        <di:waypoint x="998" y="147" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="838" y="82" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s6bqya_di" bpmnElement="Flow_1s6bqya">
        <di:waypoint x="432" y="227" />
        <di:waypoint x="432" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="441" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bdhwwf_di" bpmnElement="Flow_0bdhwwf">
        <di:waypoint x="260" y="227" />
        <di:waypoint x="260" y="460" />
        <di:waypoint x="810" y="460" />
        <di:waypoint x="810" y="420" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="229" y="249" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cmkxg0_di" bpmnElement="Flow_0cmkxg0">
        <di:waypoint x="837" y="227" />
        <di:waypoint x="837" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="846" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03lz3q1_di" bpmnElement="Flow_03lz3q1">
        <di:waypoint x="887" y="187" />
        <di:waypoint x="948" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="907" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04xybqa_di" bpmnElement="Flow_04xybqa">
        <di:waypoint x="998" y="227" />
        <di:waypoint x="998" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1007" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02mn17s_di" bpmnElement="Flow_02mn17s">
        <di:waypoint x="640" y="187" />
        <di:waypoint x="696" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="657" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pi0och_di" bpmnElement="Flow_0pi0och">
        <di:waypoint x="590" y="227" />
        <di:waypoint x="590" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="599" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1trsu74_di" bpmnElement="Flow_1trsu74">
        <di:waypoint x="1155" y="187" />
        <di:waypoint x="1220" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1172" y="169" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oxs3t7_di" bpmnElement="Flow_0oxs3t7">
        <di:waypoint x="1147" y="179" />
        <di:waypoint x="1147" y="100" />
        <di:waypoint x="1390" y="100" />
        <di:waypoint x="1390" y="162" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1252" y="82" width="33" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1havifm_di" bpmnElement="Flow_1havifm">
        <di:waypoint x="1415" y="187" />
        <di:waypoint x="1460" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1421" y="169" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1izwis9_di" bpmnElement="Flow_1izwis9">
        <di:waypoint x="510" y="380" />
        <di:waypoint x="580" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="534" y="362" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r8b04c_di" bpmnElement="Flow_1r8b04c">
        <di:waypoint x="680" y="380" />
        <di:waypoint x="760" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="709" y="362" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05of8gw_di" bpmnElement="Flow_05of8gw">
        <di:waypoint x="1560" y="187" />
        <di:waypoint x="1606" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ty9lgl_di" bpmnElement="Flow_1ty9lgl">
        <di:waypoint x="1401" y="173" />
        <di:waypoint x="1401" y="100" />
        <di:waypoint x="1611" y="100" />
        <di:waypoint x="1611" y="182" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1493" y="82" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ie8ker_di" bpmnElement="Flow_1ie8ker">
        <di:waypoint x="1656" y="187" />
        <di:waypoint x="1701" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1658" y="169" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0247zq3_di" bpmnElement="Flow_0247zq3">
        <di:waypoint x="1801" y="187" />
        <di:waypoint x="1846" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08ryfu8_di" bpmnElement="Flow_08ryfu8">
        <di:waypoint x="1631" y="162" />
        <di:waypoint x="1631" y="100" />
        <di:waypoint x="1871" y="100" />
        <di:waypoint x="1871" y="162" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1731" y="82" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1odfqj5_di" bpmnElement="Flow_1odfqj5">
        <di:waypoint x="1871" y="212" />
        <di:waypoint x="1871" y="310" />
        <di:waypoint x="460" y="310" />
        <di:waypoint x="460" y="340" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1834" y="223" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y52lpr_di" bpmnElement="Flow_0y52lpr">
        <di:waypoint x="1991" y="227" />
        <di:waypoint x="1991" y="310" />
        <di:waypoint x="460" y="310" />
        <di:waypoint x="460" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ymobqx_di" bpmnElement="Flow_0ymobqx">
        <di:waypoint x="1896" y="187" />
        <di:waypoint x="1941" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1903" y="169" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_18v8aaz_di" bpmnElement="Flow_18v8aaz">
        <di:waypoint x="1320" y="187" />
        <di:waypoint x="1365" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ptnn2g_di" bpmnElement="Flow_0ptnn2g">
        <di:waypoint x="1048" y="187" />
        <di:waypoint x="1105" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1066" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ifrl68_di" bpmnElement="Flow_0ifrl68">
        <di:waypoint x="1270" y="227" />
        <di:waypoint x="1270" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1279" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
