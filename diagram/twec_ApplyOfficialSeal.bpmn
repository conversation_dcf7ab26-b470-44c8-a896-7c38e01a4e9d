<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_11l56v6" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="TWEC_ApplyOfficialSeal" name="申請合約用印 - 新 (科明)" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>Flow_0ztdxj3</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0ztdxj3</bpmn:incoming>
      <bpmn:incoming>Flow_1s6bqya</bpmn:incoming>
      <bpmn:incoming>Flow_0cmkxg0</bpmn:incoming>
      <bpmn:incoming>Flow_04xybqa</bpmn:incoming>
      <bpmn:incoming>Flow_0pi0och</bpmn:incoming>
      <bpmn:incoming>Flow_0ifrl68</bpmn:incoming>
      <bpmn:incoming>Flow_1f1im3f</bpmn:incoming>
      <bpmn:incoming>Flow_0eblh88</bpmn:incoming>
      <bpmn:outgoing>Flow_1fyedkf</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bdhwwf</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0ztdxj3" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:userTask id="Activity_Step2" name="直屬主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1fyedkf</bpmn:incoming>
      <bpmn:outgoing>Flow_0eemaff</bpmn:outgoing>
      <bpmn:outgoing>Flow_1s6bqya</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step3" name="相關單位會簽">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0x50132</bpmn:incoming>
      <bpmn:outgoing>Flow_0cmkxg0</bpmn:outgoing>
      <bpmn:outgoing>Flow_03lz3q1</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="form_data_countersigners" camunda:elementVariable="user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${countersigners_reject_count &gt; 0 || nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0bdhwwf</bpmn:incoming>
      <bpmn:incoming>Flow_1r8b04c</bpmn:incoming>
      <bpmn:outgoing>Flow_0djdrch</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:intermediateThrowEvent id="Event_1b020yf">
      <bpmn:incoming>Flow_0djdrch</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_0djdrch" sourceRef="Activity_End" targetRef="Event_1b020yf" />
    <bpmn:exclusiveGateway id="Gateway_14v3g9l">
      <bpmn:incoming>Flow_02mn17s</bpmn:incoming>
      <bpmn:outgoing>Flow_0x50132</bpmn:outgoing>
      <bpmn:outgoing>Flow_0somi1n</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1fyedkf" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0eemaff" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step2-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0x50132" name="需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "Y"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0somi1n" name="不需會簽" sourceRef="Gateway_14v3g9l" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${form_data_requireCountersign == "N"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1s6bqya" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0bdhwwf" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0cmkxg0" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4" name="法務">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0somi1n</bpmn:incoming>
      <bpmn:incoming>Flow_0f9scet</bpmn:incoming>
      <bpmn:outgoing>Flow_04xybqa</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ptnn2g</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_03lz3q1" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Step3-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_04xybqa" name="駁回" sourceRef="Activity_Step4" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step2-1" name="部門主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0eemaff</bpmn:incoming>
      <bpmn:outgoing>Flow_02mn17s</bpmn:outgoing>
      <bpmn:outgoing>Flow_0pi0och</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_02mn17s" name="核准" sourceRef="Activity_Step2-1" targetRef="Gateway_14v3g9l">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0pi0och" name="駁回" sourceRef="Activity_Step2-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step8" name="法務">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1izwis9</bpmn:incoming>
      <bpmn:outgoing>Flow_1r8b04c</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step8_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1izwis9" name="核准" sourceRef="Activity_Step7" targetRef="Activity_Step8">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1r8b04c" name="核准" sourceRef="Activity_Step8" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1odfqj5" name="無發票章" sourceRef="Gateway_0s7fqpc" targetRef="Activity_Step7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_stampSeal") == false }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0y52lpr" sourceRef="Activity_Step6-4" targetRef="Activity_Step7">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ptnn2g" name="核准" sourceRef="Activity_Step4" targetRef="Activity_Step5">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ifrl68" name="駁回" sourceRef="Activity_Step6-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step5" name="總經理">
      <bpmn:incoming>Flow_0ptnn2g</bpmn:incoming>
      <bpmn:outgoing>Flow_1n9jd7m</bpmn:outgoing>
      <bpmn:outgoing>Flow_1f1im3f</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step5_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1n9jd7m" name="核准" sourceRef="Activity_Step5" targetRef="Gateway_177vyrx">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1f1im3f" name="駁回" sourceRef="Activity_Step5" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_177vyrx">
      <bpmn:incoming>Flow_1n9jd7m</bpmn:incoming>
      <bpmn:outgoing>Flow_1bvk6hl</bpmn:outgoing>
      <bpmn:outgoing>Flow_0idomwv</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0yt8ssi">
      <bpmn:incoming>Flow_0idomwv</bpmn:incoming>
      <bpmn:incoming>Flow_1tn01lo</bpmn:incoming>
      <bpmn:outgoing>Flow_1iu5szz</bpmn:outgoing>
      <bpmn:outgoing>Flow_07z4vc2</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_1jn2daf">
      <bpmn:incoming>Flow_07z4vc2</bpmn:incoming>
      <bpmn:incoming>Flow_0bg3t1k</bpmn:incoming>
      <bpmn:outgoing>Flow_157z0oe</bpmn:outgoing>
      <bpmn:outgoing>Flow_1h3rz6c</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="Gateway_0s7fqpc">
      <bpmn:incoming>Flow_1h3rz6c</bpmn:incoming>
      <bpmn:incoming>Flow_0oqymys</bpmn:incoming>
      <bpmn:outgoing>Flow_13uea4a</bpmn:outgoing>
      <bpmn:outgoing>Flow_1odfqj5</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Activity_Step6-1" name="大章">
      <bpmn:incoming>Flow_1bvk6hl</bpmn:incoming>
      <bpmn:outgoing>Flow_1tn01lo</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ifrl68</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6-1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step6-2" name="小章">
      <bpmn:incoming>Flow_1iu5szz</bpmn:incoming>
      <bpmn:outgoing>Flow_0bg3t1k</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6-2_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step6-3" name="騎縫章">
      <bpmn:incoming>Flow_157z0oe</bpmn:incoming>
      <bpmn:outgoing>Flow_0oqymys</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6-3_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step6-4" name="發票章">
      <bpmn:incoming>Flow_13uea4a</bpmn:incoming>
      <bpmn:outgoing>Flow_0y52lpr</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step6-4_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_1bvk6hl" name="有大章" sourceRef="Gateway_177vyrx" targetRef="Activity_Step6-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_officialSeal") == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0idomwv" name="無大章&#10;" sourceRef="Gateway_177vyrx" targetRef="Gateway_0yt8ssi" />
    <bpmn:sequenceFlow id="Flow_1tn01lo" sourceRef="Activity_Step6-1" targetRef="Gateway_0yt8ssi">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1iu5szz" name="有小章" sourceRef="Gateway_0yt8ssi" targetRef="Activity_Step6-2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_personalSeal") == true }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_07z4vc2" name="無小章" sourceRef="Gateway_0yt8ssi" targetRef="Gateway_1jn2daf" />
    <bpmn:sequenceFlow id="Flow_0bg3t1k" sourceRef="Activity_Step6-2" targetRef="Gateway_1jn2daf">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_157z0oe" name="有騎縫章" sourceRef="Gateway_1jn2daf" targetRef="Activity_Step6-3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_pagingSeal") == true }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1h3rz6c" name="無騎縫章" sourceRef="Gateway_1jn2daf" targetRef="Gateway_0s7fqpc" />
    <bpmn:sequenceFlow id="Flow_0oqymys" sourceRef="Activity_Step6-3" targetRef="Gateway_0s7fqpc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13uea4a" name="有發票章" sourceRef="Gateway_0s7fqpc" targetRef="Activity_Step6-4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.hasVariable("form_data_invoiceSeal") == true }</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step3-1" name="總經理">
      <bpmn:incoming>Flow_03lz3q1</bpmn:incoming>
      <bpmn:outgoing>Flow_0f9scet</bpmn:outgoing>
      <bpmn:outgoing>Flow_0eblh88</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step3-1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfActiveInstances == 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_0f9scet" name="核准" sourceRef="Activity_Step3-1" targetRef="Activity_Step4">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0eblh88" name="駁回" sourceRef="Activity_Step3-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step7" name="起單人上傳雙方用印後合約">
      <bpmn:incoming>Flow_0y52lpr</bpmn:incoming>
      <bpmn:incoming>Flow_1odfqj5</bpmn:incoming>
      <bpmn:outgoing>Flow_1izwis9</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step7_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TWEC_ApplyOfficialSeal">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="169" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="230" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_147otnc" bpmnElement="Activity_Step2">
        <dc:Bounds x="382" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_16o97v6_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="787" y="147" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1o4xcbk_di" bpmnElement="Activity_End">
        <dc:Bounds x="770" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1b020yf_di" bpmnElement="Event_1b020yf">
        <dc:Bounds x="952" y="362" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14v3g9l_di" bpmnElement="Gateway_14v3g9l" isMarkerVisible="true">
        <dc:Bounds x="696" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_060p6dy_di" bpmnElement="Activity_Step4">
        <dc:Bounds x="1118" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0ayks9o_di" bpmnElement="Activity_Step2-1">
        <dc:Bounds x="540" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10i184b" bpmnElement="Activity_Step8">
        <dc:Bounds x="590" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1wak4pb" bpmnElement="Activity_Step5">
        <dc:Bounds x="1270" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gb8koc" bpmnElement="Gateway_177vyrx" isMarkerVisible="true">
        <dc:Bounds x="1425" y="162" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="341" y="123" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0glxhx4" bpmnElement="Gateway_0yt8ssi" isMarkerVisible="true">
        <dc:Bounds x="1655" y="162" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="709" y="162" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0z4l2tb" bpmnElement="Gateway_1jn2daf" isMarkerVisible="true">
        <dc:Bounds x="1885" y="162" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="709" y="162" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0ldo4p6" bpmnElement="Gateway_0s7fqpc" isMarkerVisible="true">
        <dc:Bounds x="2115" y="162" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0gnf4md" bpmnElement="Activity_Step6-1">
        <dc:Bounds x="1520" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0kszbvl" bpmnElement="Activity_Step6-2">
        <dc:Bounds x="1750" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1d3xar0" bpmnElement="Activity_Step6-3">
        <dc:Bounds x="1981" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1l1jbsw" bpmnElement="Activity_Step6-4">
        <dc:Bounds x="2210" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1j38f7e" bpmnElement="Activity_Step3-1">
        <dc:Bounds x="949" y="147" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_059g04f" bpmnElement="Activity_Step7">
        <dc:Bounds x="420" y="340" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_0ztdxj3_di" bpmnElement="Flow_0ztdxj3">
        <di:waypoint x="188" y="187" />
        <di:waypoint x="230" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0djdrch_di" bpmnElement="Flow_0djdrch">
        <di:waypoint x="870" y="380" />
        <di:waypoint x="952" y="380" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fyedkf_di" bpmnElement="Flow_1fyedkf">
        <di:waypoint x="330" y="187" />
        <di:waypoint x="382" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="345" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0eemaff_di" bpmnElement="Flow_0eemaff">
        <di:waypoint x="482" y="187" />
        <di:waypoint x="540" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="500" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x50132_di" bpmnElement="Flow_0x50132">
        <di:waypoint x="746" y="187" />
        <di:waypoint x="787" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="750" y="169" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0somi1n_di" bpmnElement="Flow_0somi1n">
        <di:waypoint x="721" y="162" />
        <di:waypoint x="721" y="100" />
        <di:waypoint x="1168" y="100" />
        <di:waypoint x="1168" y="147" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="923" y="82" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s6bqya_di" bpmnElement="Flow_1s6bqya">
        <di:waypoint x="432" y="227" />
        <di:waypoint x="432" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="441" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bdhwwf_di" bpmnElement="Flow_0bdhwwf">
        <di:waypoint x="260" y="227" />
        <di:waypoint x="260" y="460" />
        <di:waypoint x="820" y="460" />
        <di:waypoint x="820" y="420" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="229" y="249" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cmkxg0_di" bpmnElement="Flow_0cmkxg0">
        <di:waypoint x="837" y="227" />
        <di:waypoint x="837" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="846" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03lz3q1_di" bpmnElement="Flow_03lz3q1">
        <di:waypoint x="887" y="187" />
        <di:waypoint x="949" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="907" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04xybqa_di" bpmnElement="Flow_04xybqa">
        <di:waypoint x="1168" y="227" />
        <di:waypoint x="1168" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1177" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02mn17s_di" bpmnElement="Flow_02mn17s">
        <di:waypoint x="640" y="187" />
        <di:waypoint x="696" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="657" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pi0och_di" bpmnElement="Flow_0pi0och">
        <di:waypoint x="590" y="227" />
        <di:waypoint x="590" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="599" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1izwis9_di" bpmnElement="Flow_1izwis9">
        <di:waypoint x="520" y="380" />
        <di:waypoint x="590" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="545" y="362" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1r8b04c_di" bpmnElement="Flow_1r8b04c">
        <di:waypoint x="690" y="380" />
        <di:waypoint x="770" y="380" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="719" y="362" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1odfqj5_di" bpmnElement="Flow_1odfqj5">
        <di:waypoint x="2140" y="212" />
        <di:waypoint x="2140" y="310" />
        <di:waypoint x="470" y="310" />
        <di:waypoint x="470" y="340" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2088" y="223" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y52lpr_di" bpmnElement="Flow_0y52lpr">
        <di:waypoint x="2260" y="227" />
        <di:waypoint x="2260" y="310" />
        <di:waypoint x="470" y="310" />
        <di:waypoint x="470" y="340" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ptnn2g_di" bpmnElement="Flow_0ptnn2g">
        <di:waypoint x="1218" y="187" />
        <di:waypoint x="1270" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1233" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ifrl68_di" bpmnElement="Flow_0ifrl68">
        <di:waypoint x="1570" y="227" />
        <di:waypoint x="1570" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1579" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1n9jd7m_di" bpmnElement="Flow_1n9jd7m">
        <di:waypoint x="1370" y="187" />
        <di:waypoint x="1425" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1387" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1f1im3f_di" bpmnElement="Flow_1f1im3f">
        <di:waypoint x="1320" y="227" />
        <di:waypoint x="1320" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1329" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_030s0vh" bpmnElement="Flow_1bvk6hl">
        <di:waypoint x="1475" y="187" />
        <di:waypoint x="1520" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1478" y="169" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0v1u1f7" bpmnElement="Flow_0idomwv">
        <di:waypoint x="1467" y="179" />
        <di:waypoint x="1467" y="100" />
        <di:waypoint x="1680" y="100" />
        <di:waypoint x="1680" y="162" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1557" y="82" width="33" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1tn01lo_di" bpmnElement="Flow_1tn01lo">
        <di:waypoint x="1620" y="187" />
        <di:waypoint x="1655" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_004lw2b" bpmnElement="Flow_1iu5szz">
        <di:waypoint x="1705" y="187" />
        <di:waypoint x="1750" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1706" y="169" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0m2gw06" bpmnElement="Flow_07z4vc2">
        <di:waypoint x="1691" y="173" />
        <di:waypoint x="1691" y="100" />
        <di:waypoint x="1890" y="100" />
        <di:waypoint x="1890" y="182" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1777" y="82" width="33" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0i051b0" bpmnElement="Flow_0bg3t1k">
        <di:waypoint x="1850" y="187" />
        <di:waypoint x="1885" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_196a2rk" bpmnElement="Flow_157z0oe">
        <di:waypoint x="1935" y="187" />
        <di:waypoint x="1981" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1931" y="169" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0c8kd7l" bpmnElement="Flow_1h3rz6c">
        <di:waypoint x="1910" y="162" />
        <di:waypoint x="1910" y="100" />
        <di:waypoint x="2140" y="100" />
        <di:waypoint x="2140" y="162" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2005" y="82" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_0909h76" bpmnElement="Flow_0oqymys">
        <di:waypoint x="2081" y="187" />
        <di:waypoint x="2115" y="187" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_1v0jv94" bpmnElement="Flow_13uea4a">
        <di:waypoint x="2165" y="187" />
        <di:waypoint x="2210" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2163" y="169" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0f9scet_di" bpmnElement="Flow_0f9scet">
        <di:waypoint x="1049" y="187" />
        <di:waypoint x="1118" y="187" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1073" y="169" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0eblh88_di" bpmnElement="Flow_0eblh88">
        <di:waypoint x="999" y="227" />
        <di:waypoint x="999" y="280" />
        <di:waypoint x="280" y="280" />
        <di:waypoint x="280" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1009" y="253" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
