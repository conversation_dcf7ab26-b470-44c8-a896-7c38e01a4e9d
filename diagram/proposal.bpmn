<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1xs4lsn" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.11.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.17.0">
  <bpmn:process id="Proposal" name="質量管理文件申請" isExecutable="true" camunda:versionTag="1">
    <bpmn:extensionElements />
    <bpmn:startEvent id="StartEvent_1" name="起單">
      <bpmn:outgoing>Flow_071sd16</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Activity_Step2" name="直屬主管">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1ox5dao</bpmn:incoming>
      <bpmn:outgoing>Flow_1foal6v</bpmn:outgoing>
      <bpmn:outgoing>Flow_1rt4de9</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Activity_Step3" name="質量管理">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_1foal6v</bpmn:incoming>
      <bpmn:outgoing>Flow_0oe0u8e</bpmn:outgoing>
      <bpmn:outgoing>Flow_0u46omr</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Activity_End" name="結束" camunda:type="external" camunda:topic="common:task:end">
      <bpmn:incoming>Flow_0p5usfx</bpmn:incoming>
      <bpmn:incoming>Flow_0ueplay</bpmn:incoming>
      <bpmn:outgoing>Flow_19lpdwy</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Activity_Step1" name="起單人確認">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_071sd16</bpmn:incoming>
      <bpmn:incoming>Flow_1rt4de9</bpmn:incoming>
      <bpmn:incoming>Flow_0u46omr</bpmn:incoming>
      <bpmn:incoming>Flow_13q72f9</bpmn:incoming>
      <bpmn:outgoing>Flow_1ox5dao</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ueplay</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="Flow_071sd16" sourceRef="StartEvent_1" targetRef="Activity_Step1" />
    <bpmn:intermediateThrowEvent id="EndEvent_1">
      <bpmn:incoming>Flow_19lpdwy</bpmn:incoming>
    </bpmn:intermediateThrowEvent>
    <bpmn:sequenceFlow id="Flow_19lpdwy" sourceRef="Activity_End" targetRef="EndEvent_1" />
    <bpmn:sequenceFlow id="Flow_0p5usfx" name="核准" sourceRef="Activity_Step4-1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1ox5dao" name="核准" sourceRef="Activity_Step1" targetRef="Activity_Step2">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0ueplay" name="駁回" sourceRef="Activity_Step1" targetRef="Activity_End">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1foal6v" name="核准" sourceRef="Activity_Step2" targetRef="Activity_Step3">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1rt4de9" name="駁回" sourceRef="Activity_Step2" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0oe0u8e" name="核准" sourceRef="Activity_Step3" targetRef="Activity_Step4-1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_0u46omr" name="駁回" sourceRef="Activity_Step3" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_13q72f9" name="駁回" sourceRef="Activity_Step4-1" targetRef="Activity_Step1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approve == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:userTask id="Activity_Step4-1" name="文管發行">
      <bpmn:incoming>Flow_0oe0u8e</bpmn:incoming>
      <bpmn:outgoing>Flow_13q72f9</bpmn:outgoing>
      <bpmn:outgoing>Flow_0p5usfx</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="Activity_Step4-1_users" camunda:elementVariable="specify_user">
        <bpmn:completionCondition xsi:type="bpmn:tFormalExpression">${nrOfCompletedInstances &gt; 0}</bpmn:completionCondition>
      </bpmn:multiInstanceLoopCharacteristics>
    </bpmn:userTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Proposal">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="152" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="145" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1ma9eup_di" bpmnElement="Activity_Step2">
        <dc:Bounds x="630" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_060p6dy_di" bpmnElement="Activity_Step3">
        <dc:Bounds x="960" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_08w6lgj" bpmnElement="Activity_End">
        <dc:Bounds x="630" y="270" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xenrw8_di" bpmnElement="Activity_Step1">
        <dc:Bounds x="300" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1r40yx4_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="662" y="432" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_16vvv94" bpmnElement="Activity_Step4-1">
        <dc:Bounds x="1280" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_071sd16_di" bpmnElement="Flow_071sd16">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="300" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_19lpdwy_di" bpmnElement="Flow_19lpdwy">
        <di:waypoint x="680" y="350" />
        <di:waypoint x="680" y="432" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0p5usfx_di" bpmnElement="Flow_0p5usfx">
        <di:waypoint x="1360" y="160" />
        <di:waypoint x="1360" y="320" />
        <di:waypoint x="730" y="320" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1364" y="193" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ox5dao_di" bpmnElement="Flow_1ox5dao">
        <di:waypoint x="400" y="120" />
        <di:waypoint x="630" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="504" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ueplay_di" bpmnElement="Flow_0ueplay">
        <di:waypoint x="330" y="160" />
        <di:waypoint x="330" y="310" />
        <di:waypoint x="630" y="310" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="339" y="181" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1foal6v_di" bpmnElement="Flow_1foal6v">
        <di:waypoint x="730" y="120" />
        <di:waypoint x="960" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="834" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rt4de9_di" bpmnElement="Flow_1rt4de9">
        <di:waypoint x="680" y="160" />
        <di:waypoint x="680" y="230" />
        <di:waypoint x="370" y="230" />
        <di:waypoint x="370" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="689" y="193" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0oe0u8e_di" bpmnElement="Flow_0oe0u8e">
        <di:waypoint x="1060" y="120" />
        <di:waypoint x="1280" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1159" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u46omr_di" bpmnElement="Flow_0u46omr">
        <di:waypoint x="1010" y="160" />
        <di:waypoint x="1010" y="230" />
        <di:waypoint x="370" y="230" />
        <di:waypoint x="370" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1019" y="193" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13q72f9_di" bpmnElement="Flow_13q72f9">
        <di:waypoint x="1310" y="160" />
        <di:waypoint x="1310" y="230" />
        <di:waypoint x="370" y="230" />
        <di:waypoint x="370" y="160" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1319" y="193" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
