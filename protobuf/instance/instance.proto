syntax = "proto3";

package formInstance;

import "google/protobuf/timestamp.proto";

service FormInstance {
    rpc create(CreateFormInstanceParams) returns (FormInstanceData) {};
}

enum EnumInstanceStatus {
    Waiting = 0;
    Completed = 1;
    Canceled = 2;
    Rejected = 3;
    Reconsidered = 4;
}

message FormInstanceData {
    int32 id = 1;
    int32 formId = 2;
    optional string processInstanceId = 3;
    optional string values = 4;
    google.protobuf.Timestamp startDate = 5;
    optional google.protobuf.Timestamp finishDate = 6;
    string ownerCompanyCode = 7;
    string ownerDepartmentCode = 8;
    string ownerUserCode = 9;
    EnumInstanceStatus status = 10;
    string createdUserCode = 11;
    bool deleted = 12;
    optional google.protobuf.Timestamp createdAt = 13;
    optional google.protobuf.Timestamp updatedAt = 14;
}

message FileInfo {
    string filename = 1;
    string mimetype = 2;
    string encoding = 3;
    bytes content = 4;
}

message CreateFormInstanceParams {
    string formCode = 1;
    optional string values = 2;
    optional string companyCode = 3;
    optional string departmentCode = 4;
    string userCode = 5;
    repeated FileInfo files = 6;
    string createdUserCode = 7;
}
