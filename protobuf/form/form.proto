syntax = "proto3";

package form;

service Form {
    rpc search(SearchFormParams) returns (SearchFormResult) {};
}

message SearchFormParams {
    optional int32 id = 1;
    optional string code = 2;
    optional int32 regionId = 3;
    optional int32 offset = 4;
    optional int32 limit = 5;
}

message SearchFormResult {
    bool success = 1;
    Result result = 2;
}

message Result {
    repeated FormData data = 1;
    int32 total = 2;
    optional int32 nextId = 3;
    optional bool hasNext = 4;
}

message FormData {
    int32 id = 1;
    string name = 2;
    string code = 3;
    string processKey = 4;
    int32 version = 5;
}
