stages:
  - build
  - docker
  - deploy

default:
  image: ${GEV_DOCKER_IMAGE_DEVOPS}

build:
  stage: build
  image: node:16.13.0-alpine3.14
  script:
    - npm ci
    - npm run build

docker:dev:
  stage: docker
  only:
    - dev
  variables:
    EX_IMAGE_NAME: ***********:5000/clinico-bpm-api:dev
  script:
    - docker build --no-cache . -t "${EX_IMAGE_NAME}"
    - docker push "${EX_IMAGE_NAME}"

deploy:k8s-dev-tw:
  stage: deploy
  only:
    - dev
  variables:
    EX_CLUSTER_SERVER_URL: ${KUBE_URL}
    EX_CLUSTER_CERTIFICATE: ${KUBE_CA_PEM_FILE}
    EX_CLUSTER_USER_TOKEN: ${KUBE_TOKEN}
    EX_DEPLOY_NAMESPACE: development
    EX_DEPLOY_NAME: clinico-bpm-api-tw
    EX_IMAGE_NAME: ***********:5000/clinico-bpm-api:dev
  before_script:
    - echo "${EX_CLUSTER_CERTIFICATE}" > ./ca.crt
    - kubectl config set-cluster k8s --server="${EX_CLUSTER_SERVER_URL}"
    - kubectl config set-cluster k8s --certificate-authority=./ca.crt
    - kubectl config set-credentials gitlab --token="${EX_CLUSTER_USER_TOKEN}"
    - kubectl config set-context default --cluster=k8s --user=gitlab
    - kubectl config use-context default
  script:
    # update image and modify timestamp to force pull latest image
    - >
      kubectl -n ${EX_DEPLOY_NAMESPACE} patch deployment "${EX_DEPLOY_NAME}-deployment"
      --patch "{\"spec\": {\"template\": {
      \"spec\": {\"containers\": [{\"name\": \"${EX_DEPLOY_NAME}-0\",\"image\": \"${EX_IMAGE_NAME}\"}]},
      \"metadata\": {\"labels\": {\"redeploy\": \"$(date +%s)\"}}}}}"
  after_script:
    - rm -rf ./ca.crt

deploy:k8s-dev-cn:
  stage: deploy
  only:
    - dev
  variables:
    EX_CLUSTER_SERVER_URL: ${KUBE_URL}
    EX_CLUSTER_CERTIFICATE: ${KUBE_CA_PEM_FILE}
    EX_CLUSTER_USER_TOKEN: ${KUBE_TOKEN}
    EX_DEPLOY_NAMESPACE: development
    EX_DEPLOY_NAME: clinico-bpm-api-cn
    EX_IMAGE_NAME: ***********:5000/clinico-bpm-api:dev
  before_script:
    - echo "${EX_CLUSTER_CERTIFICATE}" > ./ca.crt
    - kubectl config set-cluster k8s --server="${EX_CLUSTER_SERVER_URL}"
    - kubectl config set-cluster k8s --certificate-authority=./ca.crt
    - kubectl config set-credentials gitlab --token="${EX_CLUSTER_USER_TOKEN}"
    - kubectl config set-context default --cluster=k8s --user=gitlab
    - kubectl config use-context default
  script:
    # update image and modify timestamp to force pull latest image
    - >
      kubectl -n ${EX_DEPLOY_NAMESPACE} patch deployment "${EX_DEPLOY_NAME}-deployment"
      --patch "{\"spec\": {\"template\": {
      \"spec\": {\"containers\": [{\"name\": \"${EX_DEPLOY_NAME}-0\",\"image\": \"${EX_IMAGE_NAME}\"}]},
      \"metadata\": {\"labels\": {\"redeploy\": \"$(date +%s)\"}}}}}"
  after_script:
    - rm -rf ./ca.crt

docker:prod:
  stage: docker
  only:
    - tags
  variables:
    AWS_REGION: ap-northeast-1
    AWS_REGISTRY_URL: ************.dkr.ecr.ap-northeast-1.amazonaws.com
    EX_ECR_IMAGE_NAME: ************.dkr.ecr.ap-northeast-1.amazonaws.com/clinico-wrokflow/clinico-bpm-api:${CI_COMMIT_TAG}
  services:
    - docker:dind
  before_script:
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_REGISTRY_URL
  script:
    - docker build --no-cache . -t "${EX_ECR_IMAGE_NAME}"
    - docker push "${EX_ECR_IMAGE_NAME}"

deploy:eks-prod:
  stage: deploy
  only:
    - tags
  variables:
    EX_CLUSTER_SERVER_URL: ${EKS_API_URL}
    EX_CLUSTER_CERTIFICATE: ${EKS_CA_PEM_FILE}
    EX_CLUSTER_USER_TOKEN: ${EKS_ACCOUNT_TOKEN}
    EX_DEPLOY_NAMESPACE: production
    EX_DEPLOYMENT_NAME: clinico-bpm-api-deployment
    EX_CONTAINER_NAME: clinico-bpm-api-0
    EX_IMAGE_NAME: ************.dkr.ecr.ap-northeast-1.amazonaws.com/clinico-wrokflow/clinico-bpm-api:${CI_COMMIT_TAG}
  before_script:
    - echo "${EX_CLUSTER_CERTIFICATE}" > ./ca.crt
    - kubectl config set-cluster k8s --server="${EX_CLUSTER_SERVER_URL}"
    - kubectl config set-cluster k8s --certificate-authority=./ca.crt
    - kubectl config set-credentials gitlab --token="${EX_CLUSTER_USER_TOKEN}"
    - kubectl config set-context default --cluster=k8s --user=gitlab
    - kubectl config use-context default
  script:
    - kubectl -n $EX_DEPLOY_NAMESPACE set image deployment $EX_DEPLOYMENT_NAME $EX_CONTAINER_NAME=$EX_IMAGE_NAME
  after_script:
    - rm -rf ./ca.crt

# ### for devops script ###
deploy-script_prod-cn-aws:
  stage: deploy
  variables:
    EEV_UPSTREAM_PROJECT_PATH: $CI_PROJECT_PATH
    EEV_UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
    EEV_UPSTREAM_BRANCH: $CI_COMMIT_BRANCH
    EEV_UPSTREAM_JOB_PREFIX: TW
  rules:
    - if: $CI_COMMIT_TAG
      variables:
        EEV_UPSTREAM_TAG: $CI_COMMIT_TAG
        EEV_UPSTREAM_BRANCH: $CI_DEFAULT_BRANCH
    - if: $EEV_UPSTREAM_TAG
      variables:
        EEV_UPSTREAM_TAG: $EEV_UPSTREAM_TAG
  trigger:
    project: devops/helm-charts/clinico/workflow/clinico-bpm-api
    branch: master
    strategy: depend

deploy-script_prod-cn-ali:
  stage: deploy
  variables:
    EEV_UPSTREAM_PROJECT_PATH: $CI_PROJECT_PATH
    EEV_UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
    EEV_UPSTREAM_BRANCH: $CI_COMMIT_BRANCH
    EEV_UPSTREAM_JOB_PREFIX: CN
  rules:
    - if: $CI_COMMIT_TAG
      variables:
        EEV_UPSTREAM_TAG: $CI_COMMIT_TAG
        EEV_UPSTREAM_BRANCH: $CI_DEFAULT_BRANCH
    - if: $EEV_UPSTREAM_TAG
      variables:
        EEV_UPSTREAM_TAG: $EEV_UPSTREAM_TAG
  trigger:
    project: devops/helm-charts/clinico/workflow/clinico-bpm-api
    branch: master
    strategy: depend

pages:
  stage: deploy
  image: node:20
  script:
    # build vitepress start
    - npm i -D vitepress
    - cp CHANGELOG.md docs
    - npx vitepress build docs
    # build vitepress end
    - echo "Building documentation..."
    - echo "This is a placeholder for the documentation build step."
    - echo "Copying documentation to public directory..."
    - mkdir public
    # move vitepress build output to public directory
    - cp -r ./docs/.vitepress/dist/* public
    - ls -la public
  artifacts:
    paths:
      - public
  only:
    - docs