require('module-alias/register');
import 'reflect-metadata';
import dotenvFlow from 'dotenv-flow';
dotenvFlow.config();

import Koa from 'koa';
import * as Sentry from '@sentry/node';
import bodyParser from 'koa-bodyparser';
import koaLogger from 'koa-logger';
import {
    getMetadataArgsStorage,
    useKoaServer,
    useContainer,
} from 'routing-controllers';
import configs from './configs';
import { buildSchemaSync } from 'type-graphql';
import { Container } from 'typedi';
import { ApolloServer } from 'apollo-server-koa';
import {
    ApolloServerPluginLandingPageDisabled,
    ApolloServerPluginLandingPageLocalDefault,
} from 'apollo-server-core';
import { Utils } from '@clinico/clinico-node-framework';
import * as reids from '@/common/databases/redis.database';
import { routingControllersToSpec } from 'routing-controllers-openapi';
import { koaSwagger } from 'koa2-swagger-ui';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import Mali, { Context } from 'mali';
import {
    GraphQLErrorInterceptor,
    gRPCErrorInterceptor,
} from '@/common/interceptors/error.interceptor';
import * as grpcRouters from '@/common/services/grpc.service';
import { CamundaClientService } from './modules/camunda/providers/camundaClient.service';
import { WSService } from './common/services/ws.service';
import { gRPCAccessKeyAuthInterceptor } from './common/interceptors/serviceAuth.interceptor';
import { validationMetadatasToSchemas } from 'class-validator-jsonschema';
import { defaultMetadataStorage } from 'class-transformer/cjs/storage';
const { version } = require('../package.json');

Sentry.init({
    dsn: configs.sentry.dsn,
    environment: process.env.NODE_ENV,
    tracesSampleRate: 1.0,
    ignoreErrors: configs.sentry.ignoreErrors,
});

Utils.SSO.initialize(configs.sso);
Utils.JWT.initialize(configs.jwt);
Utils.AWS.initialize(configs.aws);
Utils.Mailer.initialize(configs.smtp);

const bootstrap = async () => {
    // clinico database
    await ClinicoDataSource.initialize();
    // redis
    await reids.initialize(configs.database.redis);

    const app = new Koa();
    app.use(
        bodyParser({
            formLimit: configs.app.bodySize,
            jsonLimit: configs.app.bodySize,
            textLimit: configs.app.bodySize,
        }),
    );
    app.use(koaLogger());

    // restful
    useContainer(Container);
    const httpService = useKoaServer(app, {
        defaultErrorHandler: false,
        cors: true,
        controllers: [__dirname + '/**/*.controller.{ts,js}'],
        middlewares: [__dirname + '/**/*.{middleware,interceptor}.{ts,js}'],
        validation: true,
    }).listen(configs.app.port);

    // WS
    const wss = Container.get(WSService);
    await wss.init(httpService);

    // graphql
    const schema = buildSchemaSync({
        resolvers: [__dirname + '/**/*.resolver.{ts,js}'],
        globalMiddlewares: [GraphQLErrorInterceptor],
        container: Container,
        dateScalarMode: 'isoDate',
        emitSchemaFile: {
            path: './schema.gql',
            commentDescriptions: true,
            sortedSchema: true,
        },
    });

    const apolloServer = new ApolloServer({
        schema,
        debug: configs.graphql.debug,
        context: async ({ ctx }: { ctx: Koa.Context }) => {
            return ctx;
        },
        plugins: [
            configs.graphql.playground
                ? ApolloServerPluginLandingPageLocalDefault()
                : ApolloServerPluginLandingPageDisabled(),
        ],
    });

    await apolloServer.start();
    apolloServer.applyMiddleware({ app, path: '/graphql' });

    // gRPC server
    // https://mali.js.org/guide/getting_started.html
    const gRPC = new Mali(configs.grpc.proto.path);
    gRPC.use(gRPCErrorInterceptor);
    gRPC.use(gRPCAccessKeyAuthInterceptor);
    gRPC.on('error', (err, ctx: Context<any>) => {
        // error handler
        console.error(err);
    });
    gRPC.use(grpcRouters);
    await gRPC.start(`0.0.0.0:${configs.grpc.port}`, undefined, {
        'grpc.max_receive_message_length': 1024 * 1024 * 100, //100MB
        'grpc.max_send_message_length': 1024 * 1024 * 100, //100MB
    });

    // open api spec
    // https://github.com/epiphone/routing-controllers-openapi

    // Parse class-validator classes into JSON Schema:
    const schemas = validationMetadatasToSchemas({
        classTransformerMetadataStorage: defaultMetadataStorage,
        refPointerPrefix: '#/components/schemas/',
    });
    const storage = getMetadataArgsStorage();
    const spec = routingControllersToSpec(
        storage,
        {},
        {
            components: {
                schemas: Object(schemas),
            },
            info: { title: 'Clinico BPM API', version: version },
        },
    );

    // swagger ui
    // https://github.com/scttcper/koa2-swagger-ui
    app.use(
        koaSwagger({
            routePrefix: '/docs',
            swaggerOptions: { spec },
        }),
    );

    // camunda client
    const client = Container.get(CamundaClientService);
    await client.start();
};

bootstrap();
