require('module-alias/register');
import 'reflect-metadata';
import dotenvFlow from 'dotenv-flow';
dotenvFlow.config();

(async () => {
    // await ClinicoDataSource.initialize();
    // TEST: 起單
    // const formService = Container.get(FormService);
    // let form = await formService.findOneByProcessKey('Demo');
    // const formInstanceService = Container.get(FormInstanceService);
    // let instance = await formInstanceService.creae({
    //     formId: form!.id,
    //     values: '{}',
    //     ownerCompanyId: 30,
    //     ownerDepartmentId: 1414,
    //     ownerDepartmentCode: 'IT001-6',
    //     ownerUserId: 1356,
    //     ownerUserCode: 'L0975',
    //     createdUserId: 1356,
    //     variables: {
    //         'initiator': { value: 1356 },
    //     }
    // });
    // console.log(instance);
    // TEST: 核准任務
    // const instanceTaskService = Container.get(FormInstanceTaskService);
    // const { current, next } = await instanceTaskService.approve({
    //     id: 7,
    //     memo: 'Test',
    // });
    // console.log(current.status);
    // console.log(next?.status);
})();
