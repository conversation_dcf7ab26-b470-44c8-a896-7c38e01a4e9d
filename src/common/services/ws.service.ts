import { Utils } from '@clinico/clinico-node-framework';
import { Service } from 'typedi';
import { UserPayload } from '@/modules/organization/types/user.type';
import { Server } from 'socket.io';
import http from 'http';

@Service()
export class WSService {
    private io: Server;

    async init(server: http.Server): Promise<void> {
        this.io = new Server(server);

        this.io.on('connection', async (socket) => {
            const token: string = socket.handshake.auth['authorization'] || '';
            const user: UserPayload = await Utils.JWT.verify(token);
            if (user) {
                console.log(`userId: ${user.id} is connected`);
                socket.join(user.id.toString());
            }

            socket.on('disconnect', () => {
                console.log(`disconnect`);
            });
        });
    }

    send(userId: number, event: string, message: string | any): void {
        this.io.to(userId.toString()).emit(event, message);
    }
}
