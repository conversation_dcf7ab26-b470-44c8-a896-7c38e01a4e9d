import axios, { RawAxiosRequestHeaders } from 'axios';
import { Inject, Service } from 'typedi';
import { FormInstance } from '../models/clinico/bpm/formInstance.model';
import { EnumHookEvent, HookParams } from '../types/hook.type';
import configs from '@/configs';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { TaskWebhookBodyParams } from '@/modules/camunda/types/taskWebhook.type';

@Service()
export class HookService {
    @Inject()
    private formRepo: FormRepository;

    async hook(params: HookParams) {
        const form = await this.formRepo.findOne(params.formInstance.formId);
        if (!form?.hookURL) {
            return;
        }
        switch (params.event) {
            case EnumHookEvent.SubscribeStart:
            case EnumHookEvent.SubscribeEnd:
            case EnumHookEvent.Instance:
                return this.instance(form.hookURL, params.formInstance, {
                    'access-key': configs.auth.erp.key ?? '',
                    'access-secret': configs.auth.erp.secret ?? '',
                });
            case EnumHookEvent.Task: //todo
            default:
                return;
        }
    }

    async taskProcess(
        hookURL: string,
        body: TaskWebhookBodyParams,
        appendHeader?: RawAxiosRequestHeaders,
    ) {
        const hookAxios = axios.create({
            baseURL: hookURL,
            headers: Object.assign(
                {
                    Accept: 'application/json',
                },
                appendHeader,
            ),
        });
        const endpoint = '';
        return hookAxios.post(endpoint, body);
    }

    async instance(
        hookURL: string,
        formInstance: FormInstance,
        appendHeader?: RawAxiosRequestHeaders,
    ) {
        const hookAxios = axios.create({
            baseURL: hookURL,
            headers: Object.assign(
                {
                    Accept: 'application/json',
                },
                appendHeader,
            ),
        });
        const endpoint = `/${formInstance.id}`;
        await hookAxios.put(endpoint, {
            status: formInstance.status,
            values: formInstance.values,
        });
    }
}
