import { BaseError } from '@clinico/base-error';
import {
    CommonFindAndCountResult,
    CommonSearchParams,
    CommonSearchResult,
} from '../types/common.type';
import httpStatus from 'http-status';
import { FindOptionsRelations } from 'typeorm';

export abstract class CommonService<T> {
    get commonDataNotFoundMessage(): string {
        return 'Data not found';
    }

    protected toFindAndCountResult(
        obj: [T[], number],
    ): CommonFindAndCountResult<T> {
        return { rows: obj[0], count: obj[1] };
    }

    async findOne(
        id: number,
        relations?: FindOptionsRelations<T>,
    ): Promise<T | null> {
        const data = await this.search({ id: id }, relations);
        const rows = data.rows;
        return rows.length == 1 ? rows[0] : null;
    }

    async findOneOrError(
        id: number,
        relations?: FindOptionsRelations<T>,
        dataNotFoundMessage?: string,
    ): Promise<T> {
        const data = await this.findOne(id, relations);
        if (!data) {
            const message =
                dataNotFoundMessage || this.commonDataNotFoundMessage || '';
            throw new BaseError(message, httpStatus.NOT_FOUND);
        }
        return data;
    }

    async findAll(relations?: FindOptionsRelations<T>): Promise<T[]> {
        const data = await this.search({}, relations);
        return data.rows;
    }

    /**
     * perf 把關聯拉出來...
     */
    abstract search(
        params: CommonSearchParams,
        relations?: FindOptionsRelations<T>,
    ): Promise<CommonSearchResult<T>>;
}
