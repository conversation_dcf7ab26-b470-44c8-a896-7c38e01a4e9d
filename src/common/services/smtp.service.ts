import { FormInstanceMessageService } from '@/modules/form/formInstanceMessage/providers/formInstanceMessage.service';
import { UserService } from '@/modules/organization/providers/user.service';
import { Helpers, Utils } from '@clinico/clinico-node-framework';
import { Inject, Service } from 'typedi';
import { SMTPHelpers } from '../helpers/smtp.helper';
import { CommonSendMailParams } from '../types/common.smtp.type';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import { FormInstanceTaskRepository } from '../../modules/form/formInstanceTask/providers/formInstanceTask.repository';
import { TaskProperty } from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { FormInstanceMessage } from '../models/clinico/bpm/formInstanceMessage.model';
import { FormInstanceTask } from '../models/clinico/bpm/formInstanceTask.model';
import { FormInstance } from '../models/clinico/bpm/formInstance.model';
import { EnumInstanceStatus } from '@/modules/form/formInstance/types/formInstance.type';

interface NotifyOthersInfo {
    mailUserCode: string | undefined | null;
}

@Service()
export class SMTPService {
    @Inject()
    private formInstanceTaskRepo: FormInstanceTaskRepository;
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private userService: UserService;
    @Inject()
    private formInstanceMessageService: FormInstanceMessageService;

    /**
     * 簽核關卡狀態通知
     * @param formInstanceTaskId 關卡id
     */
    async sendNotify(formInstanceTaskId: number) {
        const formInstanceTask =
            await this.formInstanceTaskRepo.findOneOrError(formInstanceTaskId);

        const params = {
            formName: formInstanceTask.formInstance.form?.name ?? '',
            formCode: formInstanceTask.formInstance.form?.code ?? '',
            userCode: formInstanceTask.formInstance.ownerUser?.code ?? '',
            userName: formInstanceTask.formInstance.ownerUser?.name ?? '',
            formInstanceId: formInstanceTask.formInstance.id,
            supervisorCode: formInstanceTask.userCode,
            createUserId: formInstanceTask.formInstance.createdUserId,
            mailUserCode: formInstanceTask.userCode,
        };

        const subject = `[簽核通知][${params.formName}#${params.formInstanceId}] ${params.userCode}${params.userName}已送出申請，請主管確認`;

        const body = await SMTPHelpers.template({
            context: `您有一件${params.formName}待簽核，請撥冗查看`,
            formCode: params.formCode,
            formInstanceId: params.formInstanceId,
        });

        if (!params.mailUserCode) {
            return false;
        }
        const user = await this.userService.findOneByCode(params.mailUserCode);

        return await this.send({
            subject,
            body,
            to: user ? [user.email] : [],
            createUserId: params.createUserId,
        });
    }

    /**
     * 簽核完成通知任務內其他人
     * 以mail通知曾經簽核過的人員(直屬主管、部門主管、會簽主管、簽呈負責人)，但不需要通知David(執行長關卡)。
     * David: 'L0528-W', 'L0528'
     * formInstance owner
     * @param formInstanceId 流程id
     * @returns
     */
    async sendCompletedNotifyOthers(formInstanceId: number) {
        const formInstance =
            await this.formInstanceRepo.findOneOrError(formInstanceId);

        const params = {
            formName: formInstance.form?.name ?? '',
            formCode: formInstance.form?.code ?? '',
            userCode: formInstance.ownerUserCode,
            userName: formInstance.ownerUser?.name ?? '',
            formInstanceId: formInstance.id,
            createUserId: formInstance.createdUserId,
        };

        const formInstanceTasks = formInstance.formInstanceTasks;
        const notifyOthersMap = formInstanceTasks.reduce((pre, cur) => {
            const isSkipUserCode: boolean = [
                'L0528-W', // David
                'L0528', // David
                formInstance.ownerUserCode, // owner
            ].includes(cur.userCode!);
            if (cur.status != 1 || isSkipUserCode) {
                return pre;
            }
            const result: NotifyOthersInfo = {
                mailUserCode: cur.userCode,
            };
            pre.set(cur.userCode!, result);
            return pre;
        }, new Map<string, NotifyOthersInfo>());
        const notifyOthers = notifyOthersMap.values();

        const formStatus = this.getContextByEnumMailToStatus(
            formInstance.status,
        );

        const subject = `[簽核完成通知][${params.formName}#${params.formInstanceId}] ${params.userCode}${params.userName} 申請已簽核${formStatus}`;

        const body = await SMTPHelpers.template({
            context: `您已簽核[${params.formName}#${params.formInstanceId}](${params.userCode}${params.userName})表單，已${formStatus}簽核，請撥冗查看`,
            formCode: params.formCode,
            formInstanceId: params.formInstanceId,
        });

        for (const notifyOther of notifyOthers) {
            if (!notifyOther.mailUserCode) {
                return false;
            }
            const user = await this.userService.findOneByCode(
                notifyOther.mailUserCode,
            );

            await this.send({
                subject,
                body,
                to: user ? [user.email] : [],
                createUserId: params.createUserId,
            });
        }
    }

    /**
     * 附件上傳通知
     * @param formInstanceTaskId 關卡id
     */
    async sendAdditionalDocsNotify(formInstanceTaskId: number) {
        const formInstanceTask =
            await this.formInstanceTaskRepo.findOneOrError(formInstanceTaskId);
        const formInstance = await this.formInstanceRepo.findOneOrError(
            formInstanceTask.formInstanceId,
        );

        const formInstanceTasks = formInstance.formInstanceTasks;

        const notifyUserCodes = formInstanceTasks
            .filter((task) => {
                if (!task.property) return false;
                if (task.processTaskId != formInstanceTask.processTaskId)
                    return false;
                const property = task.property as TaskProperty;
                if (!property.allowAdditionalDocs) return false;
                if (!task.userCode) return false;
                return true;
            })
            .map((task) => task.userCode!);

        const params = {
            formName: formInstance.form?.name ?? '',
            formCode: formInstance.form?.code ?? '',
            userCode: formInstance.ownerUserCode,
            formInstanceId: formInstance.id,
            createUserId: formInstance.createdUserId,
        };

        const subject = `[簽核附件上傳通知][${params.formName}#${params.formInstanceId}] 已附件上傳，請確認簽核結果`;

        const body = await SMTPHelpers.template({
            context: `${params.formName}簽核已附件上傳，請撥冗查看簽核結果`,
            formCode: params.formCode,
            formInstanceId: params.formInstanceId,
        });

        if (notifyUserCodes.length == 0) {
            return false;
        }

        for (const userCode of notifyUserCodes) {
            const user = await this.userService.findOneByCode(userCode);

            await this.send({
                subject,
                body,
                to: user ? [user.email] : [],
                createUserId: params.createUserId,
            });
        }
    }

    /**
     * 簽核完成通知
     * @param formInstanceId 流程id
     */
    async sendCompleted(formInstanceId: number) {
        const formInstance =
            await this.formInstanceRepo.findOneOrError(formInstanceId);

        const params = {
            formName: formInstance.form?.name ?? '',
            formCode: formInstance.form?.code ?? '',
            userCode: formInstance.ownerUserCode,
            formInstanceId: formInstance.id,
            createUserId: formInstance.createdUserId,
        };

        const formStatus = this.getContextByEnumMailToStatus(
            formInstance.status,
        );

        const subject = `[簽核完成通知][${params.formName}#${params.formInstanceId}] 已${formStatus}，請確認簽核結果`;

        const body = await SMTPHelpers.template({
            context: `${params.formName}簽核${formStatus}，請撥冗查看簽核結果`,
            formCode: params.formCode,
            formInstanceId: params.formInstanceId,
        });

        if (!params.userCode) {
            return false;
        }
        const user = await this.userService.findOneByCode(params.userCode);

        return await this.send({
            subject,
            body,
            to: user ? [user.email] : [],
            createUserId: params.createUserId,
        });
    }

    /**
     * 留言信件通知歷史簽核人員
     * @param message
     * @param formInstance
     * @returns
     */
    async sendMessageToHistoryTaskUsers(
        message: FormInstanceMessage,
        formInstance: FormInstance,
    ) {
        if (!message || !message.createUser || !message.formInstanceTask)
            return false;
        const currentTask = message.formInstanceTask;
        const messageUser = message.createUser;

        const skipUserIds: number[] = [messageUser.id];

        const currentProperty = (currentTask.property as TaskProperty) ?? {};
        const { isSkipMessageMail, isSkipMessageMailUsers } = currentProperty;
        if (isSkipMessageMail) return false;

        if (isSkipMessageMailUsers?.length) {
            skipUserIds.push(...isSkipMessageMailUsers.map((i) => i.id));
        }

        const tasks: FormInstanceTask[] = [];
        if (formInstance.formInstanceTasks?.length) {
            tasks.push(...formInstance.formInstanceTasks);
        } else {
            const { rows } = await this.formInstanceTaskRepo.search({
                formInstanceId: formInstance.id,
            });
            tasks.push(...rows);
        }

        const notifyUserCodes = tasks
            .filter((task) => {
                if (!task.property) return true;
                const property = task.property as TaskProperty;
                // 指定關卡 關卡內人員跳過留言通知
                if (property.isSkipMessageMailTask) return false;
                if (!task.userCode || !task.userId) return false;
                if (skipUserIds.includes(task.userId)) return false;
                return true;
            })
            .map((task) => task.userCode!);

        const setNotifyUserCodes = Array.from(new Set(notifyUserCodes));

        if (!setNotifyUserCodes.length) return false;

        const params = {
            formName: formInstance.form?.name ?? '',
            formCode: formInstance.form?.code ?? '',
            userCode: formInstance.ownerUserCode,
            formInstanceId: formInstance.id,
            createUserId: formInstance.createdUserId,
        };

        const subject = `[留言通知][${params.formName}#${params.formInstanceId}] ${messageUser.code} ${messageUser.name}已送出留言，請確認。`;

        const regx = new RegExp(/\n/g);
        const htmlMsg = message.message?.replace(regx, '<br/>');
        const context = `
    ${messageUser.code} ${messageUser.name} 送出留言: <br/>
    ${htmlMsg}
    `;
        const body = await SMTPHelpers.template({
            context,
            formCode: params.formCode,
            formInstanceId: params.formInstanceId,
        });

        for (const userCode of setNotifyUserCodes) {
            const user = await this.userService.findOneByCode(userCode);

            await this.send({
                subject,
                body,
                to: user ? [user.email] : [],
                createUserId: params.createUserId,
            });
        }
    }

    /**
     * 再議通知
     * @param messageId 留言id
     */
    async sendReconsidered(message: FormInstanceMessage) {
        const params = {
            formName: message.formInstance.form?.name ?? '',
            formCode: message.formInstance.form?.code ?? '',
            userCode: message.formInstance.ownerUser?.code ?? '',
            formId: message.formInstance.formId,
            formInstanceId: message.formInstance.id,
            createUserId: message.createdUserId,
        };

        const subject = `[簽核再議通知][${params.formName}#${params.formInstanceId}] 已變更為再議，請確認簽核內容`;

        const body = await SMTPHelpers.template({
            context: `${params.formName}簽核變更為再議，請撥冗查看簽核內容`,
            formCode: params.formCode,
            formInstanceId: params.formInstanceId,
        });

        if (!params.userCode) {
            return false;
        }
        const user = await this.userService.findOneByCode(params.userCode);

        return await this.send({
            subject,
            body,
            to: user ? [user.email] : [],
            createUserId: params.createUserId,
        });
    }

    async send(params: CommonSendMailParams) {
        let subject = params.subject;

        if (!Helpers.Env.isProduction()) {
            subject = '[測試]' + params.subject;

            const user = await this.userService.findOneOrError(
                params.createUserId,
            );

            params.to = [user.email];
        }

        return await Utils.Mailer.send({
            to: params.to.toString(),
            subject,
            body: params.body,
        });
    }

    /* ------------- */
    private getContextByEnumMailToStatus(status: number) {
        let formStatus = '';
        switch (status) {
            case EnumInstanceStatus.Canceled:
                formStatus = `取消`;
                break;
            case EnumInstanceStatus.Rejected:
                formStatus = `駁回`;
                break;
            default:
                formStatus = `完成`;
        }
        return formStatus;
    }
}
