import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Company } from '../core/company.model';
import { Department } from '../core/department.model';
import { User } from '../core/user.model';
import { Form } from './form.model';
import { FormInstanceMessage } from './formInstanceMessage.model';
import { FormInstanceTask } from './formInstanceTask.model';
import { InstancesValuesData } from '@/modules/form/formInstance/types/formInstance.type';
import { Transform } from 'class-transformer';

@Entity({
    schema: 'bpm',
    name: 'form_instances',
})
export class FormInstance {
    @PrimaryGeneratedColumn({
        type: 'bigint',
    })
    @Transform(({ value }) => Number(value))
    id: number;

    @Index('ix_bpm_fi_form_id')
    @Column({
        type: 'integer',
        name: 'form_id',
        nullable: false,
    })
    @Transform(({ value }) => Number(value))
    formId: number;

    @Index('ix_bpm_fi_code', { unique: true })
    @Column({
        name: 'code',
        type: 'text',
        nullable: true,
    })
    code?: string;

    @Index('ix_bpm_fi_process_id')
    @Column({
        name: 'process_id',
        type: 'varchar',
        length: 128,
        nullable: true,
        comment: 'Camunda 流程 ID',
    })
    processId?: string;

    @Column({
        type: 'integer',
        name: 'version',
        nullable: true,
    })
    version: number;

    @Column({
        type: 'integer',
        name: 'form_version',
        nullable: false,
    })
    formVersion: number;

    @Index('ix_bpm_fi_process_instance_id')
    @Column({
        name: 'process_instance_id',
        type: 'varchar',
        length: 128,
        nullable: true,
        comment: 'Camunda 流程執行 ID',
    })
    processInstanceId?: string;

    @Column({
        type: 'jsonb',
        nullable: true,
    })
    values?: InstancesValuesData;

    @Index('ix_bpm_fi_reference_form_instance_id')
    @Column({
        name: 'reference_form_instance_id',
        type: 'bigint',
        nullable: true,
    })
    referenceFormInstanceId?: number;

    @Index('ix_bpm_fi_be_referenced_id')
    @Column({
        name: 'be_referenced_id',
        type: 'bigint',
        nullable: true,
    })
    beReferencedId?: number;

    @Index('ix_bpm_fi_be_referenced_status')
    @Column({
        name: 'be_referenced_status',
        type: 'integer',
        nullable: true,
    })
    beReferencedStatus?: number;

    @Index('ix_bpm_fi_start_date')
    @Column({
        name: 'start_date',
        type: 'timestamp',
        nullable: false,
    })
    startDate: Date;

    @Index('ix_bpm_fi_finish_date')
    @Column({
        name: 'finish_date',
        type: 'timestamp',
        nullable: true,
    })
    finishDate?: Date;

    @Index('ix_bpm_fi_owner_company_id')
    @Column({
        name: 'owner_company_id',
        type: 'integer',
        nullable: false,
        comment: '單據擁有人公司',
    })
    ownerCompanyId: number;

    @Index('ix_bpm_fi_owner_dept_id')
    @Column({
        name: 'owner_department_id',
        type: 'integer',
        nullable: false,
        comment: '單據擁有人部門',
    })
    ownerDepartmentId: number;

    @Index('ix_bpm_fi_owner_dept_code')
    @Column({
        name: 'owner_department_code',
        type: 'varchar',
        length: 30,
        nullable: false,
        comment: '單據擁有人部門代碼',
    })
    ownerDepartmentCode: string;

    @Index('ix_bpm_fi_owner_user_id')
    @Column({
        name: 'owner_user_id',
        type: 'integer',
        nullable: false,
        comment: '單據擁有人',
    })
    ownerUserId: number;

    @Index('ix_bpm_fi_owner_user_code')
    @Column({
        name: 'owner_user_code',
        type: 'varchar',
        length: 30,
        nullable: false,
        comment: '單據擁有人代碼',
    })
    ownerUserCode: string;

    @Index('ix_bpm_fi_status')
    @Column({
        type: 'integer',
        nullable: false,
        comment: '0: 執行中, 1: 已完成, 2: 已取消, 3: 已拒絕',
    })
    status: number;

    @Index('ix_bpm_fi_created_user_id')
    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    createdUserId: number;

    @Index('ix_bpm_fi_deleted')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @OneToMany(
        () => FormInstanceTask,
        (formInstanceTask) => formInstanceTask.formInstance,
    )
    @JoinColumn({
        foreignKeyConstraintName: 'form_instance_id',
        referencedColumnName: 'id',
    })
    formInstanceTasks: FormInstanceTask[];

    @OneToOne(() => Form)
    @JoinColumn({
        name: 'form_id',
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    form?: Form;

    @OneToOne(() => Company)
    @JoinColumn({
        name: 'owner_company_id',
        foreignKeyConstraintName: 'owner_company_id',
        referencedColumnName: 'id',
    })
    ownerCompany?: Company;

    @OneToOne(() => Department)
    @JoinColumn({
        name: 'owner_department_id',
        foreignKeyConstraintName: 'owner_department_id',
        referencedColumnName: 'id',
    })
    ownerDepartment?: Department;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'owner_user_id',
        foreignKeyConstraintName: 'owner_user_id',
        referencedColumnName: 'id',
    })
    ownerUser?: User;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'created_user_id',
        foreignKeyConstraintName: 'created_user_id',
        referencedColumnName: 'id',
    })
    createUser?: User;

    @OneToMany(
        () => FormInstanceMessage,
        (formInstanceMessage) => formInstanceMessage.formInstance,
    )
    @JoinColumn({
        foreignKeyConstraintName: 'form_instance_id',
        referencedColumnName: 'id',
    })
    formInstanceMessages: FormInstanceMessage[];
}
