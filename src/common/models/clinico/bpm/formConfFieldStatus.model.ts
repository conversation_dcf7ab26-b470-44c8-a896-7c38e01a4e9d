import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Form } from './form.model';
import { EnumFormFieldStatus } from '@/modules/form/form/types/form.settings.type';
import { FilterMatchQuery } from '@/common/helpers/matchQuery/type';

@Entity({
    schema: 'bpm',
    name: 'form_conf_field_status',
})
export class FormConfFieldStatus {
    @PrimaryGeneratedColumn({
        type: 'bigint',
    })
    id: number;

    @Column({
        type: 'integer',
        name: 'form_id',
        nullable: false,
    })
    formId: number;

    @Column({
        type: 'varchar',
        nullable: false,
    })
    step: string;

    @Column({
        name: 'form_key',
        type: 'varchar',
        nullable: false,
    })
    formKey: string;

    @Column({
        name: 'form_status',
        type: 'varchar',
        nullable: false,
    })
    formStatus: EnumFormFieldStatus;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    createdUserId: number;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    updatedUserId: number;

    @Column({
        name: 'config_filter',
        type: 'jsonb',
        nullable: true,
    })
    configFilter?: FilterMatchQuery;

    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @ManyToOne(() => Form)
    @JoinColumn({
        name: 'form_id',
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    form?: Form;
}
