import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity({
    schema: 'bpm',
    name: 'attachments',
})
export class Attachment {
    @PrimaryGeneratedColumn({
        type: 'integer',
    })
    id: number;

    @Index('ix_bpm_a_form_instance_id')
    @Column({
        type: 'bigint',
        name: 'form_instance_id',
        nullable: false,
    })
    formInstanceId: number;

    @Index('ix_bpm_a_form_instance_task_id')
    @Column({
        type: 'bigint',
        name: 'form_instance_task_id',
        nullable: true,
    })
    formInstanceTaskId?: number;

    @Column({
        type: 'bigint',
        name: 'form_instance_message_id',
        nullable: true,
    })
    formInstanceMessageId?: number;

    @Column({
        length: 200,
        type: 'varchar',
        nullable: false,
    })
    s3Key: string;

    @Column({
        length: 20,
        type: 'varchar',
        nullable: false,
    })
    source: string;

    @Column({
        name: 'group_by',
        length: 20,
        type: 'varchar',
        nullable: false,
    })
    groupBy: string;

    @Column({
        name: 'upload_user_id',
        type: 'integer',
        nullable: false,
        comment: '上傳人',
    })
    uploadUserId: number;

    @Column({
        length: 100,
        type: 'varchar',
        nullable: true,
    })
    fileName?: string;

    @Index('ix_bpm_a_deleted')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;
}
