import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Form } from './form.model';
import { FormTaskConfigInfo } from '@/modules/form/form/types/form.taskSetting.type';

@Entity({
    schema: 'bpm',
    name: 'form_conf_task_properties',
})
export class FormConfTaskProperty {
    @PrimaryGeneratedColumn({
        type: 'bigint',
    })
    id: number;

    @Column({
        type: 'integer',
        name: 'form_id',
        nullable: false,
    })
    formId: number;

    @Column({
        type: 'varchar',
        nullable: false,
    })
    step: string;

    @Column({
        name: 'property_key',
        type: 'varchar',
        nullable: false,
    })
    propertyKey: string;

    @Column({
        type: 'jsonb',
        nullable: false,
    })
    config: FormTaskConfigInfo;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    createdUserId: number;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    updatedUserId: number;

    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @ManyToOne(() => Form)
    @JoinColumn({
        name: 'form_id',
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    form?: Form;
}
