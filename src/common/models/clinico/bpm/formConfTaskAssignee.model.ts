import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Form } from './form.model';
import { FormTaskAssigneeInfo } from '@/modules/form/form/types/form.taskSetting.type';
import { FilterMatchQuery } from '@/common/helpers/matchQuery/type';

@Entity({
    schema: 'bpm',
    name: 'form_conf_task_assignees',
})
export class FormConfTaskAssignee {
    @PrimaryGeneratedColumn({
        type: 'bigint',
    })
    id: number;

    @Column({
        type: 'integer',
        name: 'form_id',
        nullable: false,
    })
    formId: number;

    @Column({
        type: 'varchar',
        nullable: false,
    })
    step: string;

    @Column({
        name: 'step_name',
        type: 'varchar',
        nullable: true,
    })
    stepName?: string;

    @Column({
        type: 'integer',
        name: 'form_version',
        nullable: true,
    })
    formVersion: number;

    @Column({
        name: 'config',
        type: 'jsonb',
        nullable: false,
    })
    config: FormTaskAssigneeInfo;

    @Column({
        name: 'config_filter',
        type: 'jsonb',
        nullable: true,
    })
    configFilter?: FilterMatchQuery;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    createdUserId: number;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    updatedUserId: number;

    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @ManyToOne(() => Form)
    @JoinColumn({
        name: 'form_id',
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    form?: Form;
}
