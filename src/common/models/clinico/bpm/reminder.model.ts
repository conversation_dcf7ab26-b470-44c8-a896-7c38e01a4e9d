import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { User } from '../core/user.model';
import { FormInstanceTask } from './formInstanceTask.model';

@Entity({
    schema: 'bpm',
    name: 'reminders',
})
export class Reminder {
    @PrimaryGeneratedColumn({
        type: 'integer',
    })
    id: number;

    @Index('ix_bpm_r_form_instance_task_id')
    @Column({
        name: 'form_instance_task_id',
        type: 'bigint',
        nullable: true,
    })
    formInstanceTaskId: number | null;

    @Index('ix_bpm_r_deleted')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @Index('ix_bpm_r_created_user_id')
    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    createdUserId: number;

    @Column({
        name: 'updated_user_id',
        type: 'integer',
        nullable: true,
        comment: '更新人',
    })
    updatedUserId: number;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'created_user_id',
        foreignKeyConstraintName: 'created_user_id',
        referencedColumnName: 'id',
    })
    createUser?: User;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'updated_user_id',
        foreignKeyConstraintName: 'updated_user_id',
        referencedColumnName: 'id',
    })
    updatedUser?: User;

    @OneToOne(() => FormInstanceTask)
    @JoinColumn({
        name: 'form_instance_task_id',
        foreignKeyConstraintName: 'form_instance_task_id',
        referencedColumnName: 'id',
    })
    formInstanceTask?: FormInstanceTask;
}
