import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { User } from '../core/user.model';
import { FormInstance } from './formInstance.model';
import { FormInstanceTask } from './formInstanceTask.model';
import { Transform } from 'class-transformer';

@Entity({
    schema: 'bpm',
    name: 'form_instance_messages',
})
export class FormInstanceMessage {
    @PrimaryGeneratedColumn({
        type: 'bigint',
    })
    @Transform(({ value }) => Number(value))
    id: number;

    @Index('ix_bpm_fim_form_instance_id')
    @Column({
        type: 'bigint',
        name: 'form_instance_id',
        nullable: false,
    })
    @Transform(({ value }) => Number(value))
    formInstanceId: number;

    @Index('ix_bpm_fim_form_instance_task_id')
    @Column({
        type: 'bigint',
        name: 'form_instance_task_id',
        nullable: true,
    })
    @Transform(({ value }) => Number(value))
    formInstanceTaskId?: number;

    @Column({
        type: 'text',
        nullable: true,
    })
    message?: string;

    @Index('ix_bpm_fim_created_user_id')
    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    createdUserId: number;

    @Index('ix_bpm_fim_deleted')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'created_user_id',
        foreignKeyConstraintName: 'created_user_id',
        referencedColumnName: 'id',
    })
    createUser?: User;

    @OneToOne(() => FormInstance)
    @JoinColumn({
        name: 'form_instance_id',
        foreignKeyConstraintName: 'form_instance_id',
        referencedColumnName: 'id',
    })
    formInstance: FormInstance;

    @OneToOne(() => FormInstanceTask)
    @JoinColumn({
        name: 'form_instance_task_id',
        foreignKeyConstraintName: 'form_instance_task_id',
        referencedColumnName: 'id',
    })
    formInstanceTask?: FormInstanceTask;
}
