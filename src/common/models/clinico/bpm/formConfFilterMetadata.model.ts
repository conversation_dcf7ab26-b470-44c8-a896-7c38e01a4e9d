import {
    <PERSON>umn,
    CreateDate<PERSON><PERSON>umn,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Form } from './form.model';
import { FilterMatchQuery } from '@/common/helpers/matchQuery/type';

@Entity({
    schema: 'bpm',
    name: 'form_conf_filter_metadata',
})
export class FormConfFilterMetadata {
    @PrimaryGeneratedColumn({
        type: 'bigint',
    })
    id: number;

    @Column({
        type: 'integer',
        name: 'form_id',
        nullable: false,
    })
    formId: number;

    @Column({
        name: 'form_filter',
        type: 'jsonb',
        nullable: false,
    })
    formFilter: FilterMatchQuery;

    @Column({
        name: 'assignees_filter',
        type: 'jsonb',
        nullable: true,
    })
    assigneesFilter: FilterMatchQuery;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    createdUserId: number;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    updatedUserId: number;

    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @ManyToOne(() => Form)
    @JoinColumn({
        name: 'form_id',
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    form?: Form;
}
