import {
    Column,
    Entity,
    Index,
    Join<PERSON><PERSON>umn,
    OneToMany,
    PrimaryGeneratedColumn,
} from 'typeorm';
import { FormInstance } from './formInstance.model';
import { FormConfFilterMetadata } from './formConfFilterMetadata.model';
import { FormConfTaskMailTo } from './formConfTaskMailTo.model';
import { FormConfTaskProperty } from './formConfTaskProperty.model';
import { FormConfTaskWebhook } from './formConfTaskwebhook.model';

@Entity({
    schema: 'bpm',
    name: 'forms',
})
export class Form {
    @PrimaryGeneratedColumn({
        type: 'integer',
    })
    id: number;

    @Index('ix_bpm_f_region_id')
    @Column({
        name: 'region_id',
        type: 'integer',
        nullable: false,
    })
    regionId: number;

    @Index('ix_bpm_f_process_key')
    @Column({
        name: 'process_key',
        type: 'varchar',
        length: 128,
        nullable: false,
        comment: 'Camunda 流程 Key',
    })
    processKey: string;

    @Column({
        length: 100,
        type: 'varchar',
        nullable: false,
    })
    code: string;

    @Column({
        type: 'integer',
        name: 'version',
        nullable: false,
    })
    version: number;

    @Column({
        name: 'i18n_code',
        length: 100,
        type: 'varchar',
        nullable: false,
    })
    i18nCode: string;

    @Column({
        length: 100,
        type: 'varchar',
        nullable: false,
    })
    name: string;

    @Column({
        type: 'boolean',
        default: false,
    })
    deleted: boolean;

    @Column({
        name: 'hook_url',
        type: 'text',
        nullable: true,
    })
    hookURL: string;

    @Column({
        name: 'initial_variables',
        type: 'jsonb',
        nullable: true,
    })
    initialVariables?: JSON;

    @Column({
        name: 'form_settings',
        type: 'jsonb',
        nullable: true,
    })
    formSettings?: JSON;

    @Column({
        name: 'task_settings',
        type: 'jsonb',
        nullable: true,
    })
    taskSettings?: JSON;

    @Column({
        name: 'form_assignees',
        type: 'jsonb',
        nullable: true,
    })
    formAssignees?: JSON;

    @Index('ix_bpm_f_enable_creating')
    @Column({
        name: 'enable_creating',
        type: 'boolean',
        nullable: false,
        default: false,
    })
    enableCreating: boolean;

    @OneToMany(() => FormInstance, (formInstance) => formInstance.form)
    @JoinColumn({
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    formInstances: FormInstance[];

    @OneToMany(() => FormConfFilterMetadata, (i) => i.form)
    @JoinColumn({
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    formConfFilterMetadatas: FormConfFilterMetadata[] | null;

    @OneToMany(() => FormConfTaskMailTo, (i) => i.form)
    @JoinColumn({
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    formConfTaskMailToList: FormConfTaskMailTo[] | null;

    @OneToMany(() => FormConfTaskWebhook, (i) => i.form)
    @JoinColumn({
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    formConfTaskWebhooks: FormConfTaskWebhook[] | null;

    @OneToMany(() => FormConfTaskProperty, (i) => i.form)
    @JoinColumn({
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    formConfTaskProperties: FormConfTaskProperty[] | null;
}
