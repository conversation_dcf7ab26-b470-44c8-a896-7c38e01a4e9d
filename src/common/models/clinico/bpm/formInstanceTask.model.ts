import {
    <PERSON>umn,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    OneToMany,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Company } from '../core/company.model';
import { Department } from '../core/department.model';
import { User } from '../core/user.model';
import { FormInstance } from './formInstance.model';
import { Reminder } from './reminder.model';
import { TaskProperty } from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { ValuesAppendFormData } from '@/modules/form/formInstance/types/formValues.type';
import { Transform } from 'class-transformer';

@Entity({
    schema: 'bpm',
    name: 'form_instance_tasks',
})
export class FormInstanceTask {
    @PrimaryGeneratedColumn({
        type: 'bigint',
    })
    @Transform(({ value }) => Number(value))
    id: number;

    @Index('ix_bpm_fit_instance_task_id')
    @Column({
        name: 'process_instance_task_id',
        type: 'varchar',
        length: 128,
        nullable: false,
        comment: 'Camunda 流程執行的任務 ID',
    })
    processInstanceTaskId: string;

    @Index('ix_bpm_fit_task_id')
    @Column({
        name: 'process_task_id',
        type: 'varchar',
        length: 128,
        nullable: false,
        comment: 'Camunda 任務 ID',
    })
    processTaskId: string;

    @Column({
        name: 'process_task_activity_type',
        type: 'varchar',
        length: 128,
        nullable: false,
        comment: 'Camunda 任務類型',
    })
    processTaskActivityType: string;

    @Index('ix_bpm_fit_form_instance_id')
    @Column({
        type: 'bigint',
        name: 'form_instance_id',
        nullable: false,
    })
    @Transform(({ value }) => Number(value))
    formInstanceId: number;

    @Column({
        type: 'varchar',
        length: 100,
        nullable: false,
    })
    name: string;

    @Index('ix_bpm_fit_company_id')
    @Column({
        name: 'company_id',
        type: 'integer',
        nullable: true,
        comment: '簽核人公司',
    })
    companyId?: number;

    @Index('ix_bpm_fit_department_id')
    @Column({
        name: 'department_id',
        type: 'integer',
        nullable: true,
        comment: '簽核人部門',
    })
    departmentId?: number | null;

    @Index('ix_bpm_fit_department_code')
    @Column({
        name: 'department_code',
        type: 'varchar',
        length: 30,
        nullable: true,
        comment: '簽核人部門代碼',
    })
    departmentCode?: string | null;

    @Index('ix_bpm_fit_user_id')
    @Column({
        name: 'user_id',
        type: 'integer',
        nullable: true,
        comment: '簽核人',
    })
    userId?: number | null;

    @Index('ix_bpm_fit_user_code')
    @Column({
        name: 'user_code',
        type: 'varchar',
        length: 30,
        nullable: true,
        comment: '簽核人代碼',
    })
    userCode?: string | null;

    @Column({
        type: 'jsonb',
        nullable: true,
    })
    values?: ValuesAppendFormData;

    @Column({
        type: 'jsonb',
        nullable: true,
    })
    property?: TaskProperty;

    @Index('ix_bpm_fit_status')
    @Column({
        type: 'integer',
        nullable: false,
        comment: '0: 待簽核, 1: 已核准, 2: 已拒絕, 3: 完成',
    })
    status: number;

    @Column({
        type: 'text',
        nullable: true,
    })
    memo?: string;

    @Index('ix_bpm_fit_group_code')
    @Column({
        name: 'group_code',
        type: 'text',
        nullable: true,
    })
    groupCode?: string;

    @Index('ix_bpm_fit_deleted')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @Column({
        name: 'updated_user_id',
        type: 'integer',
        nullable: true,
        comment: '更新人',
    })
    updatedUserId: number;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @OneToOne(() => FormInstance)
    @JoinColumn({
        name: 'form_instance_id',
        foreignKeyConstraintName: 'form_instance_id',
        referencedColumnName: 'id',
    })
    formInstance: FormInstance;

    @OneToOne(() => Company)
    @JoinColumn({
        name: 'company_id',
        foreignKeyConstraintName: 'company_id',
        referencedColumnName: 'id',
    })
    company?: Company;

    @OneToOne(() => Department)
    @JoinColumn({
        name: 'department_id',
        foreignKeyConstraintName: 'department_id',
        referencedColumnName: 'id',
    })
    department?: Department;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'user_id',
        foreignKeyConstraintName: 'user_id',
        referencedColumnName: 'id',
    })
    user?: User;

    @OneToMany(() => Reminder, (reminder) => reminder.formInstanceTask)
    @JoinColumn({
        foreignKeyConstraintName: 'form_instance_task_id',
        referencedColumnName: 'id',
    })
    reminders?: Reminder[];
}
