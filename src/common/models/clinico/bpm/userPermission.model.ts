import {
    Column,
    Entity,
    Index,
    JoinColumn,
    OneToOne,
    PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../core/user.model';

@Entity({
    schema: 'bpm',
    name: 'user_permissions',
})
export class UserPermission {
    @PrimaryGeneratedColumn({
        type: 'integer',
    })
    id: number;

    @Index('ix_bpm_up_user_id')
    @Column({
        name: 'user_id',
        type: 'integer',
        nullable: false,
    })
    userId: number;

    @Column({
        type: 'jsonb',
        nullable: true,
    })
    permission?: JSON;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'user_id',
        foreignKeyConstraintName: 'user_id',
        referencedColumnName: 'id',
    })
    user?: User;
}
