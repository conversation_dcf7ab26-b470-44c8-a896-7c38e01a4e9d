import {
    <PERSON>um<PERSON>,
    CreateDate<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ManyToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Form } from './form.model';
import { FormTaskAssigneeInfo } from '@/modules/form/form/types/form.taskSetting.type';
import {
    EnumMailToStatus,
    EnumTaskStatusStr,
} from '@/modules/form/form/types/form.settings.type';

@Entity({
    schema: 'bpm',
    name: 'form_conf_task_mailto',
})
export class FormConfTaskMailTo {
    @PrimaryGeneratedColumn({
        type: 'bigint',
    })
    id: number;

    @Column({
        type: 'integer',
        name: 'form_id',
        nullable: false,
    })
    formId: number;

    @Column({
        type: 'varchar',
        nullable: false,
    })
    step: string;

    @Column({
        type: 'varchar',
        nullable: false,
    })
    status: EnumMailToStatus;

    @Column({
        name: 'assignees',
        type: 'jsonb',
        nullable: false,
    })
    assignees: FormTaskAssigneeInfo[];

    @Column({
        name: 'skip_assignees',
        type: 'jsonb',
        nullable: false,
    })
    skipAssignees: FormTaskAssigneeInfo[];

    @Column({
        name: 'task_status',
        type: 'jsonb',
        nullable: true,
    })
    taskStatus: EnumTaskStatusStr[] | null;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    createdUserId: number;

    @Column({
        name: 'created_user_id',
        type: 'integer',
        nullable: false,
        comment: '建立人',
    })
    updatedUserId: number;

    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @ManyToOne(() => Form)
    @JoinColumn({
        name: 'form_id',
        foreignKeyConstraintName: 'form_id',
        referencedColumnName: 'id',
    })
    form?: Form;
}
