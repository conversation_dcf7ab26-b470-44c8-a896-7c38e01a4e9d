import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    OneToMany,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { UserPosition } from './userPosition.model';

@Entity({
    schema: 'core',
    name: 'users',
})
export class User {
    @PrimaryGeneratedColumn({
        type: 'integer',
    })
    id: number;

    @Index('ix_core_u_code')
    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    code: string;

    @Index('ix_core_u_name')
    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
    })
    name: string;

    @Index('ix_core_u_account')
    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    account: string;

    @Index('ix_core_u_email')
    @Column({
        type: 'varchar',
        length: 300,
        nullable: false,
    })
    email: string;

    @Index('ix_core_u_mobile')
    @Column({
        type: 'varchar',
        length: 30,
        nullable: true,
        select: false,
    })
    mobile?: string;

    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    type: string;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: true,
    })
    country?: string;

    @Column({
        name: 'inauguration_date',
        type: 'date',
        nullable: false,
        comment: '入職日',
    })
    inaugurationDate: Date;

    @Column({
        name: 'resignation_date',
        type: 'date',
        nullable: true,
        comment: '離職日',
    })
    resignationDate?: Date;

    @Column({
        name: 'leave_without_pay_date1',
        type: 'date',
        nullable: true,
        comment: '留職停薪日(起)',
    })
    leaveWithoutPayDate1?: Date;

    @Column({
        name: 'leave_without_pay_date2',
        type: 'date',
        nullable: true,
        comment: '留職停薪日(迄)',
    })
    leaveWithoutPayDate2?: Date;

    @Index('ix_core_u_disabled')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    disabled: boolean;

    @Index('ix_core_u_deleted')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    deleted: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @OneToMany(() => UserPosition, (userPosition) => userPosition.user)
    @JoinColumn({
        foreignKeyConstraintName: 'user_id',
        referencedColumnName: 'id',
    })
    userPositions: UserPosition[];
}
