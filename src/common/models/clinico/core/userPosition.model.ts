import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    Join<PERSON><PERSON>umn,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Department } from './department.model';
import { User } from './user.model';

@Entity({
    schema: 'core',
    name: 'user_positions',
})
export class UserPosition {
    @PrimaryGeneratedColumn({
        type: 'integer',
    })
    id: number;

    @Index('ix_core_up_department_id')
    @Column({
        name: 'department_id',
        type: 'integer',
        nullable: false,
    })
    departmentId: number;

    @Index('ix_core_up_user_id')
    @Column({
        name: 'user_id',
        type: 'integer',
        nullable: false,
    })
    userId: number;

    @Index('ix_core_up_user_code')
    @Column({
        name: 'code',
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    userCode: string;

    @Index('ix_core_up_title')
    @Column({
        type: 'varchar',
        length: 60,
        nullable: false,
    })
    title: string;

    @Index('ix_core_up_agent_id')
    @Column({
        name: 'agent_id',
        type: 'integer',
        nullable: true,
        comment: '代理人',
    })
    agentId?: number;

    @Index('ix_core_up_is_main')
    @Column({
        name: 'is_main',
        type: 'boolean',
        nullable: false,
        default: false,
    })
    isMain: boolean;

    @Index('ix_core_up_disabled')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    disabled: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'user_id',
        foreignKeyConstraintName: 'user_id',
        referencedColumnName: 'id',
    })
    user: User;

    @OneToOne(() => Department)
    @JoinColumn({
        name: 'department_id',
        foreignKeyConstraintName: 'department_id',
        referencedColumnName: 'id',
    })
    department: Department;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'agent_id',
        foreignKeyConstraintName: 'agent_id',
        referencedColumnName: 'id',
    })
    agent: User;
}
