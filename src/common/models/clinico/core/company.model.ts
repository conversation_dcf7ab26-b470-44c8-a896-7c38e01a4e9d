import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';

@Entity({
    schema: 'core',
    name: 'companies',
})
export class Company {
    @PrimaryGeneratedColumn({
        type: 'integer',
    })
    id: number;

    @Column({
        name: 'region_id',
        type: 'integer',
        nullable: false,
    })
    regionId: number;

    @Index('ix_core_c_code')
    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    code: string;

    @Column({
        type: 'varchar',
        length: 60,
        nullable: false,
    })
    name: string;

    @Index('ix_core_c_disabled')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    disabled: boolean;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;
}
