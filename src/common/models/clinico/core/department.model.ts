import {
    Column,
    CreateDateColumn,
    Entity,
    Index,
    JoinColumn,
    OneToOne,
    PrimaryGeneratedColumn,
    UpdateDateColumn,
} from 'typeorm';
import { Company } from './company.model';
import { User } from './user.model';

export enum EnumSourceName {
    EHR = 'ehr',
}

@Entity({
    schema: 'core',
    name: 'departments',
})
export class Department {
    @PrimaryGeneratedColumn({
        type: 'integer',
    })
    id: number;

    @Index('ix_core_d_source_name')
    @Column({
        name: 'source_name',
        type: 'enum',
        enum: 'EnumSourceName',
        nullable: false,
        default: EnumSourceName.EHR,
    })
    sourceName: EnumSourceName;

    @Index('ix_core_d_source_id')
    @Column({
        name: 'source_id',
        type: 'integer',
        nullable: false,
    })
    sourceId: number;

    @Index('ix_core_d_company_id')
    @Column({
        name: 'company_id',
        type: 'integer',
        nullable: false,
    })
    companyId: number;

    @Index('ix_core_d_code')
    @Column({
        type: 'varchar',
        length: 30,
        nullable: false,
    })
    code: string;

    @Index('ix_core_d_name')
    @Column({
        type: 'varchar',
        length: 50,
        nullable: false,
    })
    name: string;

    @Index('ix_core_d_supervisor_id')
    @Column({
        name: 'supervisor_id',
        type: 'integer',
        nullable: true,
    })
    supervisorId: number;

    @Index('ix_core_d_parent_id')
    @Column({
        name: 'parent_id',
        type: 'integer',
        nullable: true,
    })
    parentId: number;

    @Index('ix_core_d_disabled')
    @Column({
        type: 'boolean',
        nullable: false,
        default: false,
    })
    disabled: boolean;

    @Column({
        type: 'varchar',
        name: 'dep_type',
        length: 10,
        nullable: true,
    })
    depType: string;

    @Column({
        type: 'varchar',
        name: 'dep_type_name',
        length: 30,
        nullable: true,
    })
    depTypeName: string;

    @Column({
        type: 'varchar',
        name: 'approval_level',
        length: 10,
        nullable: true,
    })
    approvalLevel: string;

    @CreateDateColumn({
        name: 'created_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    createdAt: Date;

    @UpdateDateColumn({
        name: 'updated_at',
        default: () => 'CURRENT_TIMESTAMP',
    })
    updatedAt: Date;

    @OneToOne(() => Company)
    @JoinColumn({
        name: 'company_id',
        foreignKeyConstraintName: 'company_id',
        referencedColumnName: 'id',
    })
    company: Company;

    @OneToOne(() => User)
    @JoinColumn({
        name: 'supervisor_id',
        foreignKeyConstraintName: 'supervisor_id',
        referencedColumnName: 'id',
    })
    supervisor?: User;

    @OneToOne(() => Department)
    @JoinColumn({
        name: 'parent_id',
        foreignKeyConstraintName: 'parent_id',
        referencedColumnName: 'id',
    })
    parent?: Department;
}
