import { OpenAPIParam } from 'routing-controllers-openapi';

export namespace OpenAPIParams {
    export const createInstance: OpenAPIParam = {
        description: '建立實例',
        requestBody: {
            content: {
                'application/json': {
                    examples: {
                        提案: {
                            value: {
                                formId: 2,
                                ownerCompanyId: 30,
                                ownerDepartmentId: 1414,
                                attachmentS3Keys: [],
                            },
                        },
                        制定: {
                            value: {
                                formId: 3,
                                values: '{"countersigners": ["30,1414,1862","30,1414,1877"],"requireCountersign": "Y","supervisor": "30,1414,1862"}',
                                ownerCompanyId: 30,
                                ownerDepartmentId: 1414,
                                attachmentS3Keys: [],
                            },
                        },
                        簽呈: {
                            value: {
                                formId: 4,
                                values: '{"countersigners": ["30,1414,1862"],"requireCountersign": "Y"}',
                                ownerCompanyId: 30,
                                ownerDepartmentId: 1414,
                                attachmentS3Keys: [],
                            },
                        },
                    },
                    schema: {
                        properties: {
                            formId: {
                                type: 'number',
                                description: '表單',
                            },
                            values: {
                                type: 'string',
                                description: '表單資料(JSON)',
                            },
                            ownerCompanyId: {
                                type: 'number',
                                description: '送簽者的公司',
                            },
                            ownerDepartmentId: {
                                type: 'number',
                                description: '送簽者的部門',
                            },
                            referenceFormInstanceId: {
                                type: 'number',
                                description: '參考的表單來源實例',
                            },
                            attachmentS3Keys: {
                                type: 'array',
                                items: {
                                    type: 'string',
                                    description: '上傳檔案的key',
                                },
                            },
                        },
                    },
                },
            },
        },
    };
    export const approveTask: OpenAPIParam = {
        description: '核准',
        requestBody: {
            content: {
                'application/json': {
                    example: {
                        values: '{}',
                        memo: '',
                        attachmentS3Keys: [],
                    },
                    schema: {
                        properties: {
                            values: {
                                type: 'string',
                                description: '表單資料(JSON)',
                            },
                            memo: {
                                type: 'string',
                                description: '備註',
                            },
                            attachmentS3Keys: {
                                type: 'array',
                                items: {
                                    type: 'string',
                                    description: '上傳檔案的key',
                                },
                            },
                        },
                    },
                },
            },
        },
    };
    export const additionalDocsTask: OpenAPIParam = {
        description: '上傳附件',
        requestBody: {
            content: {
                'application/json': {
                    example: {
                        values: '{}',
                        attachmentS3Keys: [],
                    },
                    schema: {
                        properties: {
                            values: {
                                type: 'string',
                                description: '表單資料(JSON)',
                            },
                            attachmentS3Keys: {
                                type: 'array',
                                items: {
                                    type: 'string',
                                    description: '上傳檔案的key',
                                },
                            },
                        },
                    },
                },
            },
        },
    };
    export const rejectTask: OpenAPIParam = {
        description: '駁回',
        requestBody: {
            content: {
                'application/json': {
                    example: {
                        values: '{}',
                        memo: '',
                    },
                    schema: {
                        properties: {
                            values: {
                                type: 'string',
                                description: '表單資料(JSON)',
                            },
                            memo: {
                                type: 'string',
                                description: '備註',
                            },
                        },
                    },
                },
            },
        },
    };
    export const searchAttachment: OpenAPIParam = {
        description: '查詢附件',
        responses: {
            '200': {
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                url: {
                                    type: 'string',
                                },
                                size: {
                                    type: 'number',
                                },
                            },
                        },
                    },
                },
            },
        },
    };
    export const uploadAttachment: OpenAPIParam = {
        description: '上傳檔案至S3',
        requestBody: {
            content: {
                'multipart/form-data': {
                    schema: {
                        type: 'object',
                        properties: {
                            files: {
                                format: 'binary',
                                description: '上傳的檔案',
                            },
                        },
                    },
                },
            },
        },
    };
    export const findAllTasks: OpenAPIParam = {
        description: '查詢該實例中的關卡',
        parameters: [
            {
                name: 'limit',
                in: 'query',
                required: false,
                description: 'page size',
            },
            {
                name: 'offset',
                in: 'query',
                required: false,
                description: 'skip',
            },
        ],
    };
    export const searchInstance: OpenAPIParam = {
        description: '查詢登入者的關卡',
        parameters: [
            {
                name: 'formId',
                in: 'query',
                required: false,
                description: '表單ID',
            },
            {
                name: 'status',
                in: 'query',
                required: false,
                description: '0: 執行中, 1: 已完成, 2: 已取消, 3: 已拒絕',
            },
        ],
    };
    export const currentAssigned: OpenAPIParam = {
        description: '登入者的待簽核清單',
        parameters: [
            {
                name: 'limit',
                in: 'query',
                required: false,
                description: 'page size',
            },
            {
                name: 'offset',
                in: 'query',
                required: false,
                description: 'skip',
            },
        ],
    };
    export const searchCompany: OpenAPIParam = {
        description: '公司查詢',
        parameters: [
            {
                name: 'code',
                in: 'query',
                required: false,
            },
            {
                name: 'name',
                in: 'query',
                required: false,
            },
            {
                name: 'disabled',
                in: 'query',
                required: false,
            },
            {
                name: 'limit',
                in: 'query',
                required: false,
                description: 'page size',
            },
            {
                name: 'offset',
                in: 'query',
                required: false,
                description: 'skip',
            },
        ],
    };
    export const searchDepartment: OpenAPIParam = {
        description: '部門查詢',
        parameters: [
            {
                name: 'companyId',
                in: 'query',
                required: false,
            },
            {
                name: 'companyName',
                in: 'query',
                required: false,
            },
            {
                name: 'code',
                in: 'query',
                required: false,
            },
            {
                name: 'name',
                in: 'query',
                required: false,
            },
            {
                name: 'parentId',
                in: 'query',
                required: false,
            },
            {
                name: 'parentCode',
                in: 'query',
                required: false,
            },
            {
                name: 'supervisorId',
                in: 'query',
                required: false,
            },
            {
                name: 'supervisorCode',
                in: 'query',
                required: false,
            },
            {
                name: 'disabled',
                in: 'query',
                required: false,
            },
        ],
    };
    export const findReminders: OpenAPIParam = {
        description: '查詢登入者的催單',
        parameters: [
            {
                name: 'limit',
                in: 'query',
                required: false,
                description: 'page size',
            },
            {
                name: 'offset',
                in: 'query',
                required: false,
                description: 'skip',
            },
        ],
    };
    export const createReminder: OpenAPIParam = {
        description: '新增催單',
        requestBody: {
            content: {
                'application/json': {
                    example: {
                        formInstanceId: 1,
                    },
                    schema: {
                        properties: {
                            formInstanceId: {
                                type: 'number',
                                description: '表單實例',
                            },
                        },
                    },
                },
            },
        },
    };
    export const internalCreateInstance: OpenAPIParam = {
        description: '建立實例',
        requestBody: {
            content: {
                'application/json': {
                    examples: {
                        報價: {
                            value: {
                                formCode: 'Quotation_CN',
                                orderCode: 'EQO0000050',
                                userCode: 'L1500',
                                createdUserCode: 'L1500',
                                attachmentS3Keys: [],
                            },
                        },
                    },
                    schema: {
                        properties: {
                            quotationOrderCode: {
                                type: 'string',
                                description: '報價單號',
                            },
                            values: {
                                type: 'string',
                                description: '表單資料(JSON)',
                            },
                            companyCode: {
                                type: 'string',
                                description: '送簽者的公司代碼',
                            },
                            departmentCode: {
                                type: 'string',
                                description: '送簽者的部門代碼',
                            },
                            userCode: {
                                type: 'string',
                                description: '送簽者的代碼',
                            },
                            attachmentS3Keys: {
                                type: 'array',
                                items: {
                                    type: 'string',
                                    description: '上傳檔案的key',
                                },
                            },
                        },
                    },
                },
            },
        },
    };
}
