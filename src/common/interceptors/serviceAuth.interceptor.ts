import Koa from 'koa';
import { Context } from 'mali';
import { BaseError } from '@clinico/base-error';
import { Service } from 'typedi';
import { KoaMiddlewareInterface } from 'routing-controllers';
import httpStatus from 'http-status';
import configs from '@/configs';

// GRPC
export async function gRPCAccessKeyAuthInterceptor(
    ctx: Context<any>,
    next: any,
) {
    const key = <string>ctx.metadata['access-key'] || '';
    const secret = <string>ctx.metadata['access-secret'] || '';

    await checkAuth({ key, secret });
    await next();
}

@Service()
export class InternalAuthKoaInterceptor implements KoaMiddlewareInterface {
    async use(
        context: Koa.Context,
        next: (err?: any) => Promise<any>,
    ): Promise<any> {
        const key = <string>context.header['access-key'] || '';
        const secret = <string>context.header['access-secret'] || '';

        await checkAuth({ key, secret });
        await next();
    }
}

const checkAuth = async (params: {
    key: string;
    secret: string;
}): Promise<void> => {
    if (
        configs.auth.key !== params.key ||
        configs.auth.secret !== params.secret
    )
        throw new BaseError('Access denied', httpStatus.UNAUTHORIZED);
};
