import Koa from 'koa';
import { Utils } from '@clinico/clinico-node-framework';
import { KoaMiddlewareInterface } from 'routing-controllers';
import { Service } from 'typedi';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';

// Koa Controller
@Service()
export class UserAuthKoaInterceptor implements KoaMiddlewareInterface {
    async use(
        context: Koa.Context,
        next: (err?: any) => Promise<any>,
    ): Promise<any> {
        const token = context.header['authorization'] || '';
        const user = await checkUserAuth(token);
        if (!user) {
            throw new BaseError('Relogin required', httpStatus.UNAUTHORIZED);
        }
        context.req['user'] = user;
        await next();
    }
}

// WS
export const UserAuthWSInterceptor = async (req: any): Promise<void> => {
    const token: string = req.headers['authorization'] || '';
    const user = await checkUserAuth(token);
    if (!user) throw new BaseError('Unauthorized', httpStatus.UNAUTHORIZED);
};

const checkUserAuth = async (token: string): Promise<any | null> => {
    return Utils.JWT.verify(token);
};
