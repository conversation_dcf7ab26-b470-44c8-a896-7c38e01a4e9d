import { ApolloError } from 'apollo-server-core';
import { Helpers } from '@clinico/clinico-node-framework';
import httpErrors, { HttpError } from 'http-errors';
import grpc from '@grpc/grpc-js';

const mappingHttpError = (BPMError: BPMError): HttpError => {
    let httpError = httpErrors[BPMError.statusCode];
    if (!httpError) httpError = httpErrors.NotImplemented;

    const instance = new httpError(BPMError.message);
    return instance;
};

const mappingGRPCStatus = (httpStatusCode: number): grpc.status => {
    httpStatusCode = httpStatusCode || -1;
    const { status } = grpc;
    switch (httpStatusCode) {
        case 400: // Bad Request
            return status.INVALID_ARGUMENT;
        case 401:
        case 407: // Proxy Authentication Required
            return status.UNAUTHENTICATED;
        case 403:
            return status.PERMISSION_DENIED;
        case 404:
            return status.NOT_FOUND;
        case 405: // Method Not Allowed
        case 451: // Unavailable For Legal Reasons
        case 503: // Service Unavailable
            return status.UNAVAILABLE;
        case 413: // Payload Too Large
        case 414: // URI Too Long
            return status.DATA_LOSS;
        case 416: // Requested Range Not Satisfiable
            return status.OUT_OF_RANGE;
        case 500:
            return status.INTERNAL;
        case 501: // Not Implemented
            return status.UNIMPLEMENTED;
        default:
            return status.ABORTED;
    }
};

export class BPMError extends Error {
    statusCode: number;

    constructor(message: string, statusCode: number) {
        super(message);
        this.name = this.constructor.name;
        this.statusCode = statusCode;
    }
}

export class BPMHttpError extends HttpError {
    constructor(error: BPMError) {
        const errorInstatnce = mappingHttpError(error);
        super(errorInstatnce.message);

        this.expose = errorInstatnce.expose;
        this.headers = errorInstatnce.headers;
        this.stack = errorInstatnce.stack;
        this.status = errorInstatnce.status;
        this.statusCode = errorInstatnce.statusCode;
    }
}

export class BPMGraphQLError extends ApolloError {
    constructor(error: BPMError) {
        const errorInstatnce = mappingHttpError(error);

        const code = errorInstatnce.name;
        const upperCaseCode =
            Helpers.Str.camelCaseToSnakeCase(code).toUpperCase();
        const statusCode = errorInstatnce.statusCode;

        super(errorInstatnce.message, upperCaseCode, { statusCode });
        Object.defineProperty(this, 'name', { value: code });
    }
}

export class BPMGRPCError extends Error {
    code: grpc.status;
    metadata: grpc.Metadata;

    constructor(error: BPMError) {
        super(error.message);

        const errorInstatnce = mappingHttpError(error);
        this.code = mappingGRPCStatus(errorInstatnce.statusCode);

        this.metadata = new grpc.Metadata();
        this.metadata.add('status', errorInstatnce.name);
        this.metadata.add('code', `${error.statusCode}`);
    }
}
