import { InstancesValuesData } from '@/modules/form/formInstance/types/formInstance.type';
import { AliasValueInfoType, AliasValueRowType } from './alias.helper.type';
import { getNestedFormValue } from './nested.helper';
import _ from 'lodash';

export function getAliasValueByInfo(
    aliasValueInfo: AliasValueInfoType,
    formValue: InstancesValuesData,
) {
    const { aliasInfo, aliasMap } = aliasValueInfo;
    const mapping = (aliasMap ?? []).reduce(
        (pre, cur) => pre.set(cur.key, cur.value),
        new Map<string | number | boolean, any>(),
    );
    return aliasInfo.reduce((pre, cur, idx) => {
        const { path, aliasKey } = cur;
        const value = getNestedFormValue(formValue, path) ?? null;
        const mappingValue = mapping.get(value) ?? value;
        pre[`_${idx}`] = mappingValue;
        pre[path] = mappingValue;
        pre[replaceAliasValuePath(path)] = mappingValue;
        if (aliasKey) {
            pre[aliasKey] = mappingValue;
        }
        return pre;
    }, {});
}

export function replaceAliasValuePath(path: string) {
    return path.replace(/\[|\]|\./gi, '');
}

export function getAliasValueRow(
    info: AliasValueRowType,
    formValue: InstancesValuesData,
) {
    const { aliasInfo, template } = info;
    const valueMap = getAliasValueByInfo(info, formValue);
    if (!template) {
        const pathValues = aliasInfo
            .map((i) => valueMap[i.path])
            .filter((i) => !!i);
        return pathValues.join(' ');
    }
    const replaceTemplate = replaceAliasValuePath(template);
    const templateValue = _.template(replaceTemplate);
    return templateValue(valueMap);
}
