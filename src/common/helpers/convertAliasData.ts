import _ from 'lodash';
import { ObjectData } from './matchQuery/type';

export function convertAliasData<T>(
    data: ObjectData | ObjectData[],
    aliasSource: ObjectData,
): T {
    const aliasRegex = /\$\{([A-Za-z._\-,0-9]+)\}|\$([A-Za-z._\-,0-9]+)/g;
    let dataStr = JSON.stringify(data);
    const matchAll = [...dataStr.matchAll(aliasRegex)];
    for (const regex of matchAll) {
        if (_.isUndefined(regex) || _.isNull(regex)) continue;
        const origValue = regex[0];
        const aliasPath = regex[1] ?? regex[2];
        if (_.isUndefined(aliasPath) || _.isUndefined(origValue)) continue;
        let aliasData = _.get(aliasSource, aliasPath);
        if (_.isUndefined(aliasData)) {
            aliasData = null;
        }
        const aliasDataStr = JSON.stringify(aliasData);
        dataStr = dataStr.replace(`"${origValue}"`, aliasDataStr);
    }
    return JSON.parse(dataStr);
}
