import _ from 'lodash';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { Helpers } from '@clinico/clinico-node-framework';

export type CountersignerVariable = {
    company_id: number;
    department_id: number;
    id: number;
};

export const merge = (params: {
    variables?: Variables;
    values?: JSON;
    prefix?: string;
}) => {
    const variables = { ...params.variables };
    const values = params.values || ({} as JSON);
    /* eslint no-empty: ["error", { "allowEmptyCatch": true }] */
    try {
        // values = JSON.parse(values);
        if (!_.isEmpty(values)) {
            for (const [key, value] of Object.entries(values)) {
                const keywords = [
                    'countersigners', //會簽人員
                    'supervisor', //部門主管
                    'generalManager', //
                ];
                if (keywords.includes(key)) {
                    //todo: refactor genUsers
                    const users = genUsers(value);
                    variables[`${params.prefix || ''}${key}`] = {
                        value: users,
                    };
                } else if (_.isArray(value)) {
                    flattenByArrayValue(variables, value, key, params.prefix);
                    variables[`${params.prefix || ''}${key}`] = {
                        value: value,
                    };
                } else if (_.isString(value)) {
                    if (value.length < 4000) {
                        variables[`${params.prefix || ''}${key}`] = {
                            value: value,
                        };
                    }
                } else {
                    variables[`${params.prefix || ''}${key}`] = {
                        value: value,
                    };
                }

                //自定義統計會簽結果
                if (key == 'countersigners') {
                    variables['countersigners_approved_count'] = {
                        type: 'Integer',
                        value: 0,
                    };
                    variables['countersigners_reject_count'] = {
                        type: 'Integer',
                        value: 0,
                    };
                }
            }
        }
    } catch {}
    return variables;
};

/**
 * 處理 array value，主要是處理 array 內的 object，將其打平儲存。
 * 目前有給 key: `specifyTaskUser` 使用，能從表單指定關卡簽核人員。
 * 對於 array.object 用 `key` 來辨識打平的 key
 *
 * ex1: value = [ { key: 'sign1', value: 'test1' }, { key: 'sign2', value: 'test2' }  ]
 *
 *  result: {
 *      `${prefix}_${key}_sign1_key`: 'sign1',
 *      `${prefix}_${key}_sign1_value`: 'test1',
 *      `${prefix}_${key}_sign2_key`: 'sign2',
 *      `${prefix}_${key}_sign2_value`: 'test2',
 *  }
 *
 * ---
 *
 *  ex2: value = [ { type: 'sign1', value: 'test1' }, { type: 'sign2', value: 'test2' }  ]
 *
 *  result: {
 *      `${prefix}_${key}_type`: 'sign2',
 *      `${prefix}_${key}_value`: 'test2',
 *  }
 *
 * @param variables bpm variables
 * @param value 處理的資料 any[]
 * @param key bpm variables key
 * @param prefix 前綴詞(option)
 */
function flattenByArrayValue(
    variables: Variables,
    value: any[],
    key: string,
    prefix?: string,
) {
    const itemData: any[] = [];
    for (const item of value as Array<any>) {
        if (_.isObject(item)) {
            // 是 Object 打平儲存
            const objKey = item['key'] ? `_${item['key']}` : '';
            for (const itemKey in item) {
                variables[`${prefix || ''}${key}${objKey}_${itemKey}`] = {
                    value: item[`${itemKey}`],
                };
            }
        } else {
            itemData.push(item);
        }
    }
    if (itemData.length > 0) {
        variables[`${prefix || ''}${key}`] = {
            value: itemData,
        };
    }
}

export function genUsers(
    values: any,
): CountersignerVariable | CountersignerVariable[] {
    if (typeof values == 'string') {
        const [companyId, departmentId, userId] = values.split(',');
        return {
            company_id: Helpers.Str.toIntOrNull(companyId),
            department_id: Helpers.Str.toIntOrNull(departmentId),
            id: Helpers.Str.toIntOrNull(userId),
        };
    }
    const countersigners: CountersignerVariable[] = [];
    for (const value of values) {
        const [companyId, departmentId, userId] = value.split(',');
        countersigners.push({
            company_id: Helpers.Str.toIntOrNull(companyId),
            department_id: Helpers.Str.toIntOrNull(departmentId),
            id: Helpers.Str.toIntOrNull(userId),
        });
    }
    return countersigners;
}
