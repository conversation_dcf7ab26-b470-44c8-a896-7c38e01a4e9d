import configs from '@/configs';
import { Utils } from '@clinico/clinico-node-framework';

export namespace SMTPHelpers {
    export async function template(params: {
        context: string;
        formCode: string;
        formInstanceId: number;
    }): Promise<string> {
        const templatePath = configs.templateFolder + '/email/notify.html';
        const templateData = {
            context: params.context,
            url: `${configs.webDomain}/formInstance?code=${params.formCode}&formInstanceId=${params.formInstanceId}`,
        };
        const templateBody = await Utils.HtmlTemplater.template(templatePath);

        return templateBody(templateData);
    }
}
