import { getNestedFormValue } from './nested.helper';

describe('nested.helper', () => {
  describe('getNestedFormValue', () => {
    const formValue = {
      bpmOptions: {
        formName: 'formName',
        formId: '01',
      },
      testStr: 'a',
      testInt: 1000,
      testArrInt: [1, 2, 3],
      testArrStr: ['a', 'b', 'c'],
      testArrObj: [{ a: 1 }, { b: 2 }, { c: 3 }],
    };
    it('should return an string', () => {
      const result = getNestedFormValue(formValue, 'bpmOptions.formId');
      expect(result).toEqual('01');
    });

    it('should return an number', () => {
      const result = getNestedFormValue(formValue, 'testInt');
      expect(result).toEqual(1000);
    });

    it('should return an array of number', () => {
      const result = getNestedFormValue(formValue, 'testArrInt');
      expect(result).toEqual([1, 2, 3]);
    });

    it('should return an array of string', () => {
      const result = getNestedFormValue(formValue, 'testArrStr');
      expect(result).toEqual(['a', 'b', 'c']);
    });

    it('should return an array of object', () => {
      const result = getNestedFormValue(formValue, 'testArrObj');
      expect(result).toEqual([{ a: 1 }, { b: 2 }, { c: 3 }]);
    });

    it('should return an object', () => {
      const result = getNestedFormValue(formValue, 'bpmOptions');
      expect(result).toEqual({ formName: 'formName', formId: '01' });
    });

    it('should return null', () => {
      const result = getNestedFormValue(formValue, 'bpmOptions.formName.formName');
      expect(result).toEqual(null);
    });

    it('should return int array item second [1]', () => {
      const result = getNestedFormValue(formValue, 'testArrInt.[1]');
      expect(result).toEqual(2);
    });

    it('should return int array item second [1] type: 1', () => {
      const result = getNestedFormValue(formValue, 'testArrInt.1');
      expect(result).toEqual(2);
    });

    it('should return int array item second [1] type: [1]', () => {
      const result = getNestedFormValue(formValue, 'testArrInt[1]');
      expect(result).toEqual(2);
    });

    it('should return string array item second [1]', () => {
      const result = getNestedFormValue(formValue, 'testArrStr.[1]');
      expect(result).toEqual('b');
    });

    it('should return object array item second [1]', () => {
      const result = getNestedFormValue(formValue, 'testArrObj.[1]');
      expect(result).toEqual({ b: 2 });
    });

    it('should return object array item second [1].b', () => {
      const result = getNestedFormValue(formValue, 'testArrObj.[1].b');
      expect(result).toEqual(2);
    });
  });
});