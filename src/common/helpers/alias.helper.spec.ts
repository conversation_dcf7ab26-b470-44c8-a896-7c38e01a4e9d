import { getAliasValueByInfo, getAliasValueRow } from './alias.helper';
import { AliasValueInfoType, AliasValueRowType } from './alias.helper.type';

describe('AliasHelper', () => {
  const formValue = {
    bpmOptions: {
      formName: 'formName',
      formId: '01',
    },
    testStr: 'a',
    testInt: 1000,
    testArrInt: [1, 2, 3],
    testArrStr: ['a', 'b', 'c'],
    testArrObj: [{ a: 1 }, { b: 2 }, { c: 3 }],
    testBooleanTrue: true,
    testBooleanFalse: false,
  };
  const formAliasMap = [
    {
      key: 'formName',
      value: '-FORM_NAME'
    },
    {
      key: 'a',
      value: '_A',
    },
    {
      key: 'b',
      value: '_B',
    }
  ]
  describe('getAliasValueByInfo', () => {
    // test boolean true
    it('should return alias boolean true value by info', () => {
      const aliasValueInfo: AliasValueInfoType = {
        aliasInfo: [
          {path: 'testBooleanTrue'},
          {path: 'testBooleanFalse'},
        ],
        aliasMap: [
          {
            key: true,
            value: 'YES',
          },
          {
            key: false,
            value: 'NO',
          }
        ],
      };
      const result = getAliasValueByInfo(aliasValueInfo, formValue);
      expect(result).toEqual({
        '_0': 'YES',
        'testBooleanTrue': 'YES',
        '_1': 'NO',
        'testBooleanFalse': 'NO',
      });
    });

    it('should return alias number true value by info', () => {
      const aliasValueInfo: AliasValueInfoType = {
        aliasInfo: [
          {path: 'testInt'},
        ],
        aliasMap: [
          {
            key: 100,
            value: '一百',
          },
          {
            key: 1000,
            value: '一千',
          }
        ],
      };
      const result = getAliasValueByInfo(aliasValueInfo, formValue);
      expect(result).toEqual({
        '_0': '一千',
        'testInt': '一千',
      });
    });


    it('should return alias value by info, no aliasKey, no map', () => {
      const aliasValueInfo: AliasValueInfoType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
          },
        ],
        aliasMap: [],
      };
      const result = getAliasValueByInfo(aliasValueInfo, formValue);
      expect(result).toEqual({
        '_0': '01',
        'bpmOptions.formId': '01',
        'bpmOptionsformId': '01',
      });
    });
    it('should return alias value by info, has aliasKey, no map', () => {
      const aliasValueInfo: AliasValueInfoType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
            aliasKey: '_formId',
          },
        ],
        aliasMap: [],
      };
      const result = getAliasValueByInfo(aliasValueInfo, formValue);
      expect(result).toEqual({
        '_0': '01',
        'bpmOptions.formId': '01',
        'bpmOptionsformId': '01',
        '_formId': '01',
      });
    });
    it('should return alias value by info, no aliasKey, has map', () => {
      const aliasValueInfo: AliasValueInfoType = {
        aliasInfo: [
          {
            path: 'testArrStr[1]',
          },
        ],
        aliasMap: formAliasMap,
      };
      const result = getAliasValueByInfo(aliasValueInfo, formValue);
      expect(result).toEqual({
        '_0': '_B',
        'testArrStr[1]': '_B',
        'testArrStr1': '_B',
      });
    });
  });

  describe('getAliasValueRow', () => {
    it('should return alias value row, no alias, no template', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
          },
          {
            path: 'testArrStr[1]',
          },
        ],
        aliasMap: formAliasMap,
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('01 _B');
    });

    it('should return alias value row, no alias, no template, has null info', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId.id'
          },
          {
            path: 'bpmOptions.formId',
          },
          {
            path: 'testArrStr[1]',
          },
        ],
        aliasMap: formAliasMap,
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('01 _B');
    });

    it('should return alias value row, no alias, no aliasKey, template is empty string', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
          },
          {
            path: 'testArrStr[1]',
          },
        ],
        aliasMap: formAliasMap,
        template: '',
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('01 _B');
    });

    it('should return alias value row, no alias, no aliasKey, has template', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
          },
          {
            path: 'testArrStr[1]',
          },
        ],
        template: '${testArrStr[1]}${bpmOptions.formId}',
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('b01');
    });

    it('should return alias value row, no alias, has aliasKey, has template', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
            aliasKey: '_formId',
          },
          {
            path: 'testArrStr[1]',
            aliasKey: '_item'
          },
        ],
        template: '${_item}${_formId}',
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('b01');
    });
    
    it('should return alias value row, has null info, no alias, has aliasKey, has template', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
            aliasKey: '_formId',
          },
          {
            path: 'testArrStr[1]',
            aliasKey: '_item'
          },
          {
            path: 'bpmOptions.none',
            aliasKey: 'none',
          },
        ],
        template: '${_item}${_formId}${none}',
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('b01');
    });

    it('should return alias value row, no alias, no aliasKey, has template', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
          },
          {
            path: 'testArrStr[1]',
          },
        ],
        template: "${_1}${_0}",
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('b01');
    });

    it('should return alias value row, has null info, no alias, no aliasKey, has template', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formId',
          },
          {
            path: 'testArrStr[1]',
          },
          {
            path: 'bpmOptions.none',
          },
        ],
        template: "${_1}${_0}${_2}",
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('b01');
    });

    it('should return alias value row, has alias, no aliasKey, has template', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formName',
          },
          {
            path: 'testArrStr[1]',
          },
        ],
        aliasMap: formAliasMap,
        template: '${testArrStr[1]}${bpmOptions.formName}',
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('_B-FORM_NAME');
    });

    it('should return alias value row, has alias, has aliasKey, has template', () => {
      const aliasValueRow: AliasValueRowType = {
        aliasInfo: [
          {
            path: 'bpmOptions.formName',
            aliasKey: 'formName',
          },
          {
            path: 'testArrStr[1]',
            aliasKey: 'item2',
          },
        ],
        aliasMap: formAliasMap,
        template: '${item2}${formName}',
      };
      const result = getAliasValueRow(aliasValueRow, formValue);
      expect(result).toEqual('_B-FORM_NAME');
    });
  })
});