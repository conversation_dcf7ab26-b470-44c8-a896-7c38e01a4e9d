import { Variables } from '@/modules/camunda/types/processInstance.type';
import { InstancesValuesData } from '@/modules/form/formInstance/types/formInstance.type';
import _ from 'lodash';

/**
 * property use
 * @param variables
 * @param key
 * @returns
 */
export function getNestedVariableValue(variables: Variables, key: string) {
    const nestedKeys = key.split('.');
    const variable = variables[nestedKeys[0]];
    if (!variable) return null;
    let value: any = null;
    if (nestedKeys.length == 1) {
        value = variable.value;
        return value;
    }
    value = _.get(variable.value, nestedKeys.slice(1).join('.'));
    return value;
}

/**
 * form instances value use
 * @param formValues
 * @param key
 * @returns
 */
export function getNestedFormValue(
    formValues: InstancesValuesData,
    key: string,
) {
    const value = _.get(formValues, key);
    return value ?? null;
}
