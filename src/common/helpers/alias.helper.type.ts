export type AliasValueRowInfoType = AliasValueRowType & {
    name: string;
};

export type AliasValueRowType = AliasValueInfoType & {
    template?: string;
};

export type AliasValueInfoType = {
    aliasInfo: AliasInfoType[];
    aliasMap?: AliasMapType[];
};

export type AliasMapType = {
    key: string | number | boolean;
    value: any;
};

export type AliasInfoType = {
    path: string;
    aliasKey?: string;
};
