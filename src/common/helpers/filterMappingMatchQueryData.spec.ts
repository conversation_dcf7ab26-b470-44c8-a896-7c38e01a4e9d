import {
    FilterMappingMetadata,
    filterMappingMatchQueryData,
} from './filterMappingMatchQueryData';

describe('test filterMappingMatchQueryData', () => {
    const testDataList = [
        {
            id: 1,
            code: 'A1001',
            cost: 100,
            qty: 1,
            deleted: false,
        },
        {
            id: 2,
            code: 'A1001',
            cost: 500,
            qty: 5,
            deleted: false,
        },
        {
            id: 3,
            code: 'A1001',
            cost: 500,
            qty: 12,
            deleted: false,
        },
        {
            id: 4,
            code: 'A2001',
            cost: 500,
            qty: 10,
            deleted: false,
        },
        {
            id: 5,
            code: 'B1001',
            cost: 1500,
            qty: 2,
            deleted: true,
        },
        {
            id: 6,
            code: 'B1001',
            cost: 900,
            qty: 1,
            deleted: false,
        },
    ];
    const points = {
        A1: 10,
        A2: 11,
        B1: 2,
    };

    describe('default data key: ', () => {
        it('filter mapping: ', () => {
            const filterMetadatas: FilterMappingMetadata[] = [
                {
                    filter: {
                        logic: 'and',
                        filters: [
                            {
                                field: 'data.code',
                                operator: 'contains',
                                dataType: 'string',
                                value: 'B1',
                            },
                        ],
                    },
                    mappings: [],
                },
            ];
            const result = filterMappingMatchQueryData(
                testDataList,
                filterMetadatas,
                {
                    points,
                },
            );
            const expectData = [
                {
                    id: 5,
                    code: 'B1001',
                    cost: 1500,
                    qty: 2,
                    deleted: true,
                },
                {
                    id: 6,
                    code: 'B1001',
                    cost: 900,
                    qty: 1,
                    deleted: false,
                },
            ];
            expect(result).toEqual(expectData);
        });
    });

    describe('alias data key: ', () => {
        it('filter mapping: ', () => {
            const filterMetadatas: FilterMappingMetadata[] = [
                {
                    filter: {
                        logic: 'and',
                        filters: [
                            {
                                field: 'v.code',
                                operator: 'contains',
                                dataType: 'string',
                                value: 'B1',
                            },
                        ],
                    },
                },
            ];
            const result = filterMappingMatchQueryData(
                testDataList,
                filterMetadatas,
                {
                    points,
                },
                'v',
            );
            const expectData = [
                {
                    id: 5,
                    code: 'B1001',
                    cost: 1500,
                    qty: 2,
                    deleted: true,
                },
                {
                    id: 6,
                    code: 'B1001',
                    cost: 900,
                    qty: 1,
                    deleted: false,
                },
            ];
            expect(result).toEqual(expectData);
        });
    });

    describe('error: ', () => {
        it('mapping error path, times return default 1: ', () => {
            const filterMetadatas: FilterMappingMetadata[] = [
                {
                    filter: {
                        logic: 'and',
                        filters: [
                            {
                                field: 'v.code',
                                operator: 'contains',
                                dataType: 'string',
                                value: 'B1',
                            },
                        ],
                    },
                    mappings: [],
                },
            ];
            const result = filterMappingMatchQueryData(
                testDataList,
                filterMetadatas,
                {
                    points,
                },
                'v',
            );
            const expectData = [
                {
                    id: 5,
                    code: 'B1001',
                    cost: 1500,
                    qty: 2,
                    deleted: true,
                },
                {
                    id: 6,
                    code: 'B1001',
                    cost: 900,
                    qty: 1,
                    deleted: false,
                },
            ];
            expect(result).toEqual(expectData);
        });
        it('filter error path: ', () => {
            const filterMetadatas: FilterMappingMetadata[] = [
                {
                    filter: {
                        logic: 'and',
                        filters: [
                            {
                                field: 'data.code',
                                operator: 'contains',
                                dataType: 'string',
                                value: 'B1',
                            },
                        ],
                    },
                },
            ];
            const result = filterMappingMatchQueryData(
                testDataList,
                filterMetadatas,
                {
                    points,
                },
                'v',
            );
            const expectData = [];
            expect(result).toEqual(expectData);
        });
    });
});
