import winston from 'winston';
import * as Sentry from '@sentry/node';
import axios, { AxiosError } from 'axios';

// let logger: winston.Logger;

// export { logger };

// export const initialize = (options?: winston.LoggerOptions) => {
//     let initOptions = options;
//     if (!initOptions) {
//         initOptions = {
//             level: 'debug',
//             format: winston.format.json(),
//             transports: [
//                 new winston.transports.File({
//                     filename: 'error.log',
//                     level: 'error',
//                 }),
//                 new winston.transports.File({
//                     filename: 'system.log',
//                     level: 'info',
//                 }),
//             ],
//         };
//     }
//     logger = winston.createLogger(initOptions);

//     if (!Helpers.Env.isDevelopment()) {
//         logger.add(
//             new winston.transports.Console({
//                 format: winston.format.prettyPrint(),
//             }),
//         );
//     }
// };

const logFormat = winston.format.printf(({ level, message, timestamp }) => {
    return `${timestamp} ${level}: ${message}`;
});

const initOptions = {
    level: 'debug',
    format: winston.format.combine(
        winston.format.timestamp(),
        logFormat,
        // winston.format.json(),
    ),
    transports: [
        new winston.transports.Console(),
        new winston.transports.File({
            filename: 'error.log',
            level: 'error',
        }),
        new winston.transports.File({
            filename: 'system.log',
            level: 'info',
        }),
    ],
};

const winstonLogger = winston.createLogger(initOptions);

interface Ilogger {
    err: any;
    message?: string;
    name: string;
    [key: string]: any;
}

class Logger {
    info(message: string): void {
        winstonLogger.log('info', message);
        Sentry.captureEvent({
            message,
            level: 'info',
        });
    }

    warn(message: string): void {
        winstonLogger.log('warn', message);
        Sentry.captureEvent({
            message,
            level: 'warning',
        });
    }

    error(err: Ilogger): void {
        err.message = err.name ? `[${err.name}] ${err.message}` : err.message;
        if (axios.isAxiosError(err)) {
            this.axiosError(err);
        } else {
            this.defaultError(err);
        }
    }

    private defaultError({ err, message }: Ilogger): void {
        winstonLogger.log('error', message);
        Sentry.captureException(err, (scope) => {
            scope.clear();
            scope.setLevel('error');
            scope.setExtra('message', message);
            return scope;
        });
    }

    private axiosError(err: AxiosError): void {
        const axiosErrMsg = err.response?.data?.['message'];
        err.message = axiosErrMsg
            ? `[${err.name}][${err.code}] ${axiosErrMsg}`
            : err.message;
        winstonLogger.log('error', err.message);
        Sentry.captureException(err, (scope) => {
            scope.clear();
            scope.setLevel('error');
            scope.setExtra('message', err.message);
            scope.setExtras({
                AxiosError: err,
            });
            return scope;
        });
    }
}

const logger = new Logger();
export default logger;
