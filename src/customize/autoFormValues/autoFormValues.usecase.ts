import { Inject, Service } from 'typedi';
import { ProcessKeyAutoGenValuesObj } from './autoFormValues.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Task } from '@/modules/camunda/types/task.type';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { ValuesAppendFormData } from '../../modules/form/formInstance/types/formValues.type';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { EnumTaskStatus } from '@/modules/form/formInstanceTask/types/formInstanceTask.type';
import { ProcessApplyOfficialSealUseCase } from './formCodes/processApplyOfficialSeal.usecase';
import { FormInstanceRepository } from '../../modules/form/formInstance/providers/formInstance.repository';

@Service()
export class AutoFormValuesUseCase {
    @Inject()
    private processApplyOfficialSealUCase: ProcessApplyOfficialSealUseCase;
    @Inject()
    private formRepo: FormRepository;
    @Inject()
    private repo: FormInstanceRepository;

    private autoGenValuesObj: ProcessKeyAutoGenValuesObj = {
        Apply_Official_Seal: (
            formInstance: FormInstance,
            form: Form,
            camundaTask: Task,
            status: EnumTaskStatus,
            appendFormData?: ValuesAppendFormData,
        ) =>
            this.processApplyOfficialSealUCase.genAutoInstanceValues(
                formInstance,
                form,
                camundaTask,
                status,
                appendFormData,
            ),
        TW_ApplyOfficialSeal: (
            formInstance: FormInstance,
            form: Form,
            camundaTask: Task,
            status: EnumTaskStatus,
            appendFormData?: ValuesAppendFormData,
        ) =>
            this.processApplyOfficialSealUCase.genAutoInstanceValues(
                formInstance,
                form,
                camundaTask,
                status,
                appendFormData,
            ),
        TWEC_ApplyOfficialSeal: (
            formInstance: FormInstance,
            form: Form,
            camundaTask: Task,
            status: EnumTaskStatus,
            appendFormData?: ValuesAppendFormData,
        ) =>
            this.processApplyOfficialSealUCase.genAutoInstanceValues(
                formInstance,
                form,
                camundaTask,
                status,
                appendFormData,
            ),
    };

    async autoGenFormInstanceValues(
        formInstance: FormInstance,
        camundaTask: Task,
        status: EnumTaskStatus,
        appendFormData?: ValuesAppendFormData,
    ) {
        const form = await this.formRepo.findOneOrError(formInstance.formId);
        const { processKey } = form;
        if (!(processKey in this.autoGenValuesObj)) return formInstance;
        formInstance = await this.autoGenValuesObj[processKey](
            formInstance,
            form,
            camundaTask,
            status,
            appendFormData,
        );
        return this.repo.saveFormInstance(formInstance);
    }
}
