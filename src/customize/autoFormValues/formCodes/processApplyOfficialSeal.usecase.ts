import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { IProcessAutoFormValueUseCase } from '../autoFormValues.type';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { ValuesAppendFormData } from '../../../modules/form/formInstance/types/formValues.type';
import { Task } from '@/modules/camunda/types/task.type';
import { Inject, Service } from 'typedi';
import { FormInstanceRepository } from '../../../modules/form/formInstance/providers/formInstance.repository';
import { Between } from 'typeorm';
import moment from 'moment';
import { EnumTaskStatus } from '@/modules/form/formInstanceTask/types/formInstanceTask.type';
import { FormInstanceTaskRepository } from '@/modules/form/formInstanceTask/providers/formInstanceTask.repository';
import * as StringUtil from '@/common/utils/string.util';

@Service()
export class ProcessApplyOfficialSealUseCase
    implements IProcessAutoFormValueUseCase
{
    @Inject()
    private repo: FormInstanceRepository;
    @Inject()
    private formInstanceTaskRepo: FormInstanceTaskRepository;

    private processObj = {
        Activity_Step2: (
            formInstance: FormInstance,
            form: Form,
            camundaTask: Task,
            status: EnumTaskStatus,
            appendFormData?: ValuesAppendFormData,
        ) => this.genContractStatus(formInstance, form, camundaTask, status),
        Activity_Step7: (
            formInstance: FormInstance,
            form: Form,
            camundaTask: Task,
            status: EnumTaskStatus,
            appendFormData?: ValuesAppendFormData,
        ) =>
            this.runAddContractCodeCode(
                formInstance,
                form,
                camundaTask,
                status,
            ),
    };

    /**
     *
     *
     * @param formInstance
     * @param form
     * @param camundaTask
     * @param appendFormData
     * @returns
     */
    async genAutoInstanceValues(
        formInstance: FormInstance,
        form: Form,
        camundaTask: Task,
        status: EnumTaskStatus,
        appendFormData?: ValuesAppendFormData,
    ) {
        const { taskDefinitionKey } = camundaTask;
        if (!(taskDefinitionKey in this.processObj)) return formInstance;
        return this.processObj[taskDefinitionKey](
            formInstance,
            form,
            camundaTask,
            status,
            appendFormData,
        );
    }

    /**
     * https://asking.clinico.com.tw/issues/118162
     * "contract": ["NonPayment","TheirSignedDocument"]
     *
     * 當合約類別為「無收付款」+「對方簽署文件(例如：授權書、保證函、公司函等)」簽核關卡為以下：
     * 起單人確認 -> 直屬主管 -> 部門主管 -> 法務 -> 結束
     * @param formInstance
     * @param form
     * @param camundaTask
     * @param status
     */
    private async genContractStatus(
        formInstance: FormInstance,
        form: Form,
        camundaTask: Task,
        status: EnumTaskStatus,
    ) {
        const formValues = formInstance.values;
        if (!formValues) return formInstance;
        const contract: string[] = formValues['contract'] ?? ([] as string[]);
        let contractStatus = 'Default';
        let initiatorUpload = formValues['initiator'] ?? null;
        if (
            contract.includes('NonPayment') &&
            contract.includes('TheirSignedDocument')
        ) {
            contractStatus = 'NonPayment_TheirSignedDocument';
            initiatorUpload = null;
        }
        formInstance.values = {
            ...formInstance.values,
            contractStatus,
            initiatorUpload,
        };
        return formInstance;
    }

    private async runAddContractCodeCode(
        formInstance: FormInstance,
        form: Form,
        camundaTask: Task,
        status: EnumTaskStatus,
    ) {
        if (status != EnumTaskStatus.Approved) return formInstance;
        const { taskDefinitionKey } = camundaTask;
        const formValues = formInstance.values;
        if (!formValues) return formInstance;
        const sealCompany: string = formValues['sealCompany'] ?? null;
        const contract: string[] = formValues['contract'] ?? null;
        let contractCode: string = formValues['contractCode'] ?? null;
        if (!formValues || !sealCompany || !contract) return formInstance;

        // 是否是變更的 contractCode 建立
        const isChangeContractCode = contract.includes('Changes');

        if (isChangeContractCode) {
            const origContractCode: string =
                formValues['origContractCode'] ?? null;
            if (!origContractCode) return formInstance;
            contractCode = await this.getChangeContractCodeCode(
                origContractCode,
                contractCode,
                formInstance,
                form,
                taskDefinitionKey,
                sealCompany,
            );
        } else {
            contractCode = await this.getDefaultContractCodeCode(
                contractCode,
                formInstance,
                form,
                taskDefinitionKey,
                sealCompany,
            );
        }

        formInstance.values = {
            ...formInstance.values,
            contractCode,
        };
        return formInstance;
    }

    private async getChangeContractCodeCode(
        origContractCode: string,
        contractCode: string | null,
        formInstance: FormInstance,
        form: Form,
        taskDefinitionKey: string,
        sealCompany: string,
    ) {
        if (contractCode) return contractCode;

        const idsFilterByOrigContractCode =
            await this.repo.getFormInstanceIdsByFormValueFilter({
                logic: 'and',
                filters: [
                    {
                        field: 'contractCode',
                        dataType: 'string',
                        operator: 'contains',
                        value: origContractCode,
                    },
                    {
                        field: 'contract',
                        dataType: 'arrayString',
                        operator: 'eq',
                        value: 'Changes',
                    },
                ],
            });

        const count = idsFilterByOrigContractCode.length;
        contractCode = `${origContractCode}-${count + 1}`;

        return contractCode;
    }

    /**
     * Activity_Step7
     * sealCompany
     * sealCode
     * @param formInstance
     * @param form
     * @param camundaTask
     * @param appendFormData
     */
    private async getDefaultContractCodeCode(
        contractCode: string | null,
        formInstance: FormInstance,
        form: Form,
        taskDefinitionKey: string,
        sealCompany: string,
    ) {
        if (contractCode) return contractCode;

        const formInstanceTasks =
            await this.formInstanceTaskRepo.repository.find({
                where: {
                    formInstance: {
                        formId: form.id,
                        createdAt: Between(
                            moment().startOf('year').toDate(),
                            moment().endOf('year').toDate(),
                        ),
                    },
                    processTaskId: taskDefinitionKey,
                },
                select: {
                    formInstanceId: true,
                },
            });
        const idsFilterBySealCompany =
            await this.repo.getFormInstanceIdsByFormValueFilter({
                logic: 'and',
                filters: [
                    {
                        field: 'sealCompany',
                        dataType: 'string',
                        operator: 'eq',
                        value: sealCompany,
                    },
                    {
                        field: 'origContractCode',
                        dataType: 'string',
                        operator: 'isnull',
                        value: null,
                    },
                ],
            });

        const formInstanceIds = formInstanceTasks
            .map((i) => i.formInstanceId)
            .filter((i) => idsFilterBySealCompany.includes(Number(i)));
        const formInstanceIdSet = new Set(formInstanceIds);

        const count = formInstanceIdSet.size;
        contractCode = `${sealCompany}-${moment().format(
            'YYYY',
        )}-${StringUtil.paddingLeftForInteger(count, 4, '0')}`;

        return contractCode;
    }
}
