import { Form } from '@/common/models/clinico/bpm/form.model';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Task } from '@/modules/camunda/types/task.type';
import { ValuesAppendFormData } from '../../modules/form/formInstance/types/formValues.type';
import { EnumTaskStatus } from '@/modules/form/formInstanceTask/types/formInstanceTask.type';

export type GenAutoInstanceValues = (
    formInstance: FormInstance,
    form: Form,
    camundaTask: Task,
    status: EnumTaskStatus,
    appendFormData?: ValuesAppendFormData,
) => Promise<FormInstance>;

export interface IProcessAutoFormValueUseCase {
    genAutoInstanceValues: GenAutoInstanceValues;
}

export type ProcessKeyAutoGenValuesObj = {
    [processKey: string]: GenAutoInstanceValues;
};
