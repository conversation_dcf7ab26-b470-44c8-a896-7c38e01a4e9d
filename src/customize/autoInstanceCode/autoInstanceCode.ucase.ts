import { Form } from '@/common/models/clinico/bpm/form.model';
import { Department } from '@/common/models/clinico/core/department.model';
import { Inject, Service } from 'typedi';
import { GenInstanceCodeObj } from './autoInstanceCode.type';
import _ from 'lodash';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import moment from 'moment';
import * as StringUtil from '@/common/utils/string.util';
import { GenApprovalCodeUseCase } from './formCodes/genApprovelCode.ucase';
import { GenApplySealCodeUseCase } from './formCodes/genApplySealCode.ucase';
import { GenInstituteCodeUseCase } from './formCodes/genInstituteCode.ucase';
import { GenProposalCodeUseCase } from './formCodes/genProposalCode.ucase';

@Service()
export class GenInstanceCodeUseCase {
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private genApprovalCode: GenApprovalCodeUseCase;
    @Inject()
    private genApplySealCode: GenApplySealCodeUseCase;
    @Inject()
    private genInstituteCode: GenInstituteCodeUseCase;
    @Inject()
    private genProposalCode: GenProposalCodeUseCase;

    private execObj: GenInstanceCodeObj = {
        Approval: (form: Form, ownerDepartment: Department, values?: JSON) =>
            this.genApprovalCode.genInstanceCode(form, ownerDepartment, values),
        Proposal: (form: Form, ownerDepartment: Department, values?: JSON) =>
            this.genProposalCode.genInstanceCode(form, ownerDepartment, values),
        Institute: (form: Form, ownerDepartment: Department, values?: JSON) =>
            this.genInstituteCode.genInstanceCode(
                form,
                ownerDepartment,
                values,
            ),
        Apply_Official_Seal: (
            form: Form,
            ownerDepartment: Department,
            values?: JSON,
        ) =>
            this.genApplySealCode.genInstanceCode(
                form,
                ownerDepartment,
                values,
            ),
        Apply_Document_Seal: (
            form: Form,
            ownerDepartment: Department,
            values?: JSON,
        ) =>
            this.genApplySealCode.genInstanceCode(
                form,
                ownerDepartment,
                values,
            ),
        TW_ApplyOfficialSeal: (
            form: Form,
            ownerDepartment: Department,
            values?: JSON,
        ) =>
            this.genApplySealCode.genInstanceCode(
                form,
                ownerDepartment,
                values,
            ),
        TW_ApplyDocumentSeal2: (
            form: Form,
            ownerDepartment: Department,
            values?: JSON,
        ) =>
            this.genApplySealCode.genInstanceCode(
                form,
                ownerDepartment,
                values,
            ),
        TWEC_ApplyOfficialSeal: (
            form: Form,
            ownerDepartment: Department,
            values?: JSON,
        ) =>
            this.genApplySealCode.genInstanceCode(
                form,
                ownerDepartment,
                values,
            ),
        TWEC_ApplyDocumentSeal2: (
            form: Form,
            ownerDepartment: Department,
            values?: JSON,
        ) =>
            this.genApplySealCode.genInstanceCode(
                form,
                ownerDepartment,
                values,
            ),
        default: (form: Form, ownerDepartment: Department, values?: JSON) =>
            this.genDefaultCode(form, ownerDepartment, values),
    };

    async autoGenInstanceCode(
        form: Form,
        ownerDepartment: Department,
        values?: JSON,
    ) {
        const { code } = form;
        const formCode = _.has(this.execObj, code) ? code : 'default';
        return this.execObj[formCode](form, ownerDepartment, values);
    }

    private async genDefaultCode(
        form: Form,
        ownerDepartment: Department,
        values?: JSON,
    ) {
        let count = await this.formInstanceRepo.countFormInstanceByToday(
            form.id,
        );
        count += 1;
        return `${form.code}_${moment().format(
            'YYYYMMDD',
        )}-${StringUtil.paddingLeftForInteger(count, 3, '0')}`;
    }
}
