import { Inject, Service } from 'typedi';
import { IAutoGenInstanceCode } from '../autoInstanceCode.type';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { Department } from '@/common/models/clinico/core/department.model';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import moment from 'moment';
import * as StringUtil from '@/common/utils/string.util';

@Service()
export class GenProposalCodeUseCase implements IAutoGenInstanceCode {
    @Inject()
    private formInstanceRepo: FormInstanceRepository;

    /**
     * 質量管理文件申請 GM_YYYYMMDD_NNN_TEMP
     * @param form
     * @param ownerDepartment
     * @param values
     * @returns
     */
    async genInstanceCode(
        form: Form,
        ownerDepartment: Department,
        values?: JSON,
    ) {
        let count = await this.formInstanceRepo.countFormInstanceByToday(
            form.id,
        );
        count += 1;
        return `GM-${moment().format(
            'YYYYMMDD',
        )}-${StringUtil.paddingLeftForInteger(count, 3, '0')}-TEMP`;
    }
}
