import { Inject, Service } from 'typedi';
import { IAutoGenInstanceCode } from '../autoInstanceCode.type';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { Department } from '@/common/models/clinico/core/department.model';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';

@Service()
export class GenInstituteCodeUseCase implements IAutoGenInstanceCode {
    @Inject()
    private formInstanceRepo: FormInstanceRepository;

    /**
     * 質量管理文件核定 D03-YYYYMMDD-NNN
     * @param form
     * @param ownerDepartment
     * @param values
     * @returns
     */
    async genInstanceCode(
        form: Form,
        ownerDepartment: Department,
        values?: JSON,
    ) {
        let count = await this.formInstanceRepo.countFormInstanceByToday(
            form.id,
        );
        count += 1;
        return await this.genFormInstanceCodeFromFormData(values);
    }

    private async genFormInstanceCodeFromFormData(
        formData?: any,
    ): Promise<string> {
        if (!formData) {
            return '';
        }

        const { referenceFormInstanceId } = formData;
        if (!referenceFormInstanceId) {
            return '';
        }

        const { code } = await this.formInstanceRepo.findOneOrError(
            referenceFormInstanceId,
        );
        if (!code) {
            return '';
        }
        return code.replace('-TEMP', '');
    }
}
