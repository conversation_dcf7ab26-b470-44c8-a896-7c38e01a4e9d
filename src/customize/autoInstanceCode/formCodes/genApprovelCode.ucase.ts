import { Inject, Service } from 'typedi';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { Department } from '@/common/models/clinico/core/department.model';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import moment from 'moment';
import * as StringUtil from '@/common/utils/string.util';
import { IAutoGenInstanceCode } from '../autoInstanceCode.type';

@Service()
export class GenApprovalCodeUseCase implements IAutoGenInstanceCode {
    @Inject()
    private formInstanceRepo: FormInstanceRepository;

    /**
     * 簽呈 D01-YYYYMMDD-NNN
     * @param form
     * @param ownerDepartment
     * @param values
     * @returns
     */
    async genInstanceCode(
        form: Form,
        ownerDepartment: Department,
        values?: JSON,
    ) {
        let count = await this.formInstanceRepo.countFormInstanceByToday(
            form.id,
        );
        count += 1;
        return `D01-${moment().format(
            'YYYYMMDD',
        )}-${StringUtil.paddingLeftForInteger(count, 3, '0')}`;
    }
}
