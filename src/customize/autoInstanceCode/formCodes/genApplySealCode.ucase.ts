import { Inject, Service } from 'typedi';
import { IAutoGenInstanceCode } from '../autoInstanceCode.type';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { Department } from '@/common/models/clinico/core/department.model';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import moment from 'moment';
import * as StringUtil from '@/common/utils/string.util';

@Service()
export class GenApplySealCodeUseCase implements IAutoGenInstanceCode {
    @Inject()
    private formInstanceRepo: FormInstanceRepository;

    /**
     * Apply_Official_Seal & Apply_Document_Seal
     * @param form
     * @param ownerDepartment
     * @param values
     * @returns
     */
    async genInstanceCode(
        form: Form,
        ownerDepartment: Department,
        values?: JSON,
    ) {
        let count = await this.formInstanceRepo.countFormInstanceByToday(
            form.id,
        );
        count += 1;
        return `${this.getPrefixCompanyCode(ownerDepartment)}-${moment().format(
            'YYYYMMDD',
        )}-${StringUtil.paddingLeftForInteger(count, 3, '0')}`;
    }

    private getPrefixCompanyCode(ownerDepartment: Department, prefix = 'D00') {
        const { name, code } = ownerDepartment.company;
        // 科林儀器
        const prefixCL = (_name: string) =>
            ['科林'].includes(_name) ? (prefix = 'CL') : null;
        prefixCL(name);
        // 科林國際
        const prefixHA = (_name: string) =>
            _name.includes('科林國際') ? (prefix = 'HA') : null;
        prefixHA(name);
        // 聽貝爾
        const prefixIB = (_name: string) =>
            _name.includes('聽貝爾') ? (prefix = 'IB') : null;
        prefixIB(name);
        // 濰樂
        const prefixWL = (_name: string) =>
            _name.includes('濰樂') ? (prefix = 'WL') : null;
        prefixWL(name);
        // 濰視
        const prefixWT = (_name: string) =>
            _name.includes('濰視') ? (prefix = 'WT') : null;
        prefixWT(name);
        // 科明
        const prefixEC = (_name: string) =>
            _name.includes('科明') ? (prefix = 'EC') : null;
        prefixEC(name);
        // 明睿
        const prefixMR = (_name: string) =>
            _name.includes('明睿') ? (prefix = 'MR') : null;
        prefixMR(name);
        // 達輝
        const prefixDH = (_name: string) =>
            _name.includes('達輝') ? (prefix = 'DH') : null;
        prefixDH(name);
        // 科立健
        const prefixCC = (_name: string) =>
            _name.includes('科立健') ? (prefix = 'CC') : null;
        prefixCC(name);
        return prefix;
    }
}
