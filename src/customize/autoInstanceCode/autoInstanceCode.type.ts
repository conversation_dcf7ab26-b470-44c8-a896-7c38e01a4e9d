import { Form } from '@/common/models/clinico/bpm/form.model';
import { Department } from '@/common/models/clinico/core/department.model';

export type GenInstanceCode = (
    form: Form,
    ownerDepartment: Department,
    values?: JSON,
) => Promise<string>;

export interface IAutoGenInstanceCode {
    genInstanceCode: GenInstanceCode;
}

export type GenInstanceCodeObj = {
    [formCode: string]: GenInstanceCode;
};
