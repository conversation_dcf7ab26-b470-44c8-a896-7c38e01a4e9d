import { Inject, Service } from 'typedi';
import { TaskGatewayVariableUseCase } from './taskGatewayVariable/taskGatewayVariable.ucase';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { GenInstanceCodeUseCase } from './autoInstanceCode/autoInstanceCode.ucase';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { Department } from '@/common/models/clinico/core/department.model';
import { AutoFormValuesUseCase } from './autoFormValues/autoFormValues.usecase';
import { Task } from '@/modules/camunda/types/task.type';
import { EnumTaskStatus } from '@/modules/form/formInstanceTask/types/formInstanceTask.type';
import { ValuesAppendFormData } from '@/modules/form/formInstance/types/formValues.type';

@Service()
export class CustomizeUseCase {
    @Inject()
    private taskGatewayVariableUCase: TaskGatewayVariableUseCase;
    @Inject()
    private genInstanceCodeUCase: GenInstanceCodeUseCase;
    @Inject()
    private autoFormValuesUCase: AutoFormValuesUseCase;

    async getRunCompleteGetewayVariables(
        formInstance: FormInstance,
        formData: JSON,
        approved: boolean,
        variables: Variables,
    ) {
        return this.taskGatewayVariableUCase.getRunCompleteGetewayVariables(
            formInstance,
            formData,
            approved,
            variables,
        );
    }

    async autoGenInstanceCode(
        form: Form,
        ownerDepartment: Department,
        values?: JSON,
    ) {
        return this.genInstanceCodeUCase.autoGenInstanceCode(
            form,
            ownerDepartment,
            values,
        );
    }

    async autoGenFormInstanceValues(
        formInstance: FormInstance,
        camundaTask: Task,
        status: EnumTaskStatus,
        appendFormData?: ValuesAppendFormData,
    ) {
        return this.autoFormValuesUCase.autoGenFormInstanceValues(
            formInstance,
            camundaTask,
            status,
            appendFormData,
        );
    }
}
