import { Inject, Service } from 'typedi';
import { ProcessKeyGatewayVariablesObj } from './taskGatewayVariable.type';
import { ProcessDemoGatewayUseCase } from './formCodes/processDemoGateway.ucase';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { ProcessCnQuotationGatewayUseCase } from './formCodes/processCnQuotationGateway.ucase';
import { ProcessCnQuotationOfficialSealGatewayUseCase } from './formCodes/processCnQuotationOfficialSealGateway.ucase';
import { ProcessTwApprovalGatewayUseCase } from './formCodes/processTwApprovalGateway.ucase';
import { ProcessCnExhibitionConsumptionGatewayUseCase } from './formCodes/processCnExhibitionConsumptionGateway.ucase';
import { ProcessCnForeignRequisitionGatewayUseCase } from './formCodes/processCnForeignRequisitionGateway.ucase';
import { ProcessCnRequisitionGatewayUseCase } from './formCodes/processCnRequisitionGateway.ucase';

@Service()
export class TaskGatewayVariableUseCase {
    @Inject()
    private processDemoGatewayUCase: ProcessDemoGatewayUseCase;
    @Inject()
    private processCnQuotationGatewayUCase: ProcessCnQuotationGatewayUseCase;
    @Inject()
    private processCnQuotationOfficialSealGatewayUCase: ProcessCnQuotationOfficialSealGatewayUseCase;
    @Inject()
    private processTwApprovalGatewayUCase: ProcessTwApprovalGatewayUseCase;
    @Inject()
    private processCnExhibitionConsumptionGatewayUCase: ProcessCnExhibitionConsumptionGatewayUseCase;
    @Inject()
    private processCnForeignRequisitionGatewayUCase: ProcessCnForeignRequisitionGatewayUseCase;
    @Inject()
    private processCnRequisitionGatewayUCase: ProcessCnRequisitionGatewayUseCase;
    @Inject()
    private formRepo: FormRepository;

    private gatewayVariablesObj: ProcessKeyGatewayVariablesObj = {
        Demo: (
            formInstance: FormInstance,
            form: Form,
            formData: JSON,
            approved: boolean,
            variables: Variables,
        ) =>
            this.processDemoGatewayUCase.genTaskGatewayVariables(
                formInstance,
                form,
                formData,
                approved,
                variables,
            ),
        Approval: (
            formInstance: FormInstance,
            form: Form,
            formData: JSON,
            approved: boolean,
            variables: Variables,
        ) =>
            this.processTwApprovalGatewayUCase.genTaskGatewayVariables(
                formInstance,
                form,
                formData,
                approved,
                variables,
            ),
        CN_QuotationOfficialSeal: (
            formInstance: FormInstance,
            form: Form,
            formData: JSON,
            approved: boolean,
            variables: Variables,
        ) =>
            this.processCnQuotationOfficialSealGatewayUCase.genTaskGatewayVariables(
                formInstance,
                form,
                formData,
                approved,
                variables,
            ),
        CN_Quotation: (
            formInstance: FormInstance,
            form: Form,
            formData: JSON,
            approved: boolean,
            variables: Variables,
        ) =>
            this.processCnQuotationGatewayUCase.genTaskGatewayVariables(
                formInstance,
                form,
                formData,
                approved,
                variables,
            ),
        CN_ExhibitionConsumption: (
            formInstance: FormInstance,
            form: Form,
            formData: JSON,
            approved: boolean,
            variables: Variables,
        ) =>
            this.processCnExhibitionConsumptionGatewayUCase.genTaskGatewayVariables(
                formInstance,
                form,
                formData,
                approved,
                variables,
            ),
        CN_ForeignRequisition: (
            formInstance: FormInstance,
            form: Form,
            formData: JSON,
            approved: boolean,
            variables: Variables,
        ) =>
            this.processCnForeignRequisitionGatewayUCase.genTaskGatewayVariables(
                formInstance,
                form,
                formData,
                approved,
                variables,
            ),
        CN_Requisition: (
            formInstance: FormInstance,
            form: Form,
            formData: JSON,
            approved: boolean,
            variables: Variables,
        ) =>
            this.processCnRequisitionGatewayUCase.genTaskGatewayVariables(
                formInstance,
                form,
                formData,
                approved,
                variables,
            ),
    };

    async getRunCompleteGetewayVariables(
        formInstance: FormInstance,
        formData: JSON,
        approved: boolean,
        variables: Variables,
    ) {
        const form = await this.formRepo.findOneOrError(formInstance.formId);
        const { processKey } = form;
        if (!(processKey in this.gatewayVariablesObj)) return variables;

        variables = await this.gatewayVariablesObj[processKey](
            formInstance,
            form,
            formData,
            approved,
            variables,
        );
        return variables;
    }
}
