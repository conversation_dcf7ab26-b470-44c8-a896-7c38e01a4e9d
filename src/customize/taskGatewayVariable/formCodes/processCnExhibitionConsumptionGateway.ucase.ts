import { Service } from 'typedi';
import { IProcessGatewayVariablesUseCase } from '../taskGatewayVariable.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { Form } from '@/common/models/clinico/bpm/form.model';

@Service()
export class ProcessCnExhibitionConsumptionGatewayUseCase
    implements IProcessGatewayVariablesUseCase
{
    async genTaskGatewayVariables(
        formInstance: FormInstance,
        form: Form,
        formData: JSON,
        approved: boolean,
        variables: Variables,
    ) {
        const gatewayStep3 = this.step(formInstance, form, formData);
        const gatewayStep4 = this.step(formInstance, form, formData);
        const gatewayStep5 = this.step(formInstance, form, formData);
        variables['Activity_Step1_gateway'] = {
            value: 1,
        };
        variables['Activity_Step2_gateway'] = {
            value: 1,
        };
        variables['Activity_Step3_gateway'] = {
            value: gatewayStep3,
        };
        variables['Activity_Step4_gateway'] = {
            value: gatewayStep4,
        };
        variables['Activity_Step5_gateway'] = {
            value: gatewayStep5,
        };
        return variables;
    }

    /**
     * @param formInstance
     * @param form
     * @param formData
     * @returns
     */
    private step(formInstance: FormInstance, form: Form, formData: JSON) {
        const result = 1;
        return result;
    }
}
