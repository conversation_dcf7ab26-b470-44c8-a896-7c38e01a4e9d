import { Service } from 'typedi';
import { IProcessGatewayVariablesUseCase } from '../taskGatewayVariable.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { Form } from '@/common/models/clinico/bpm/form.model';

@Service()
export class ProcessCnQuotationOfficialSealGatewayUseCase
    implements IProcessGatewayVariablesUseCase
{
    async genTaskGatewayVariables(
        formInstance: FormInstance,
        form: Form,
        formData: JSON,
        approved: boolean,
        variables: Variables,
    ) {
        const data = {
            gateway: 1,
        };
        variables['Activity_Step2_gateway'] = {
            value: 1,
        };
        variables['Activity_Step3_gateway'] = {
            value: 1,
        };
        variables['Activity_Step4_gateway'] = {
            value: 1,
        };
        return variables;
    }
}
