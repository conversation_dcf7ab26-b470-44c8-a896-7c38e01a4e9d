import { Inject, Service } from 'typedi';
import { IProcessGatewayVariablesUseCase } from '../taskGatewayVariable.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { CompanyService } from '@/modules/organization/providers/company.service';
import * as VariablesHelper from '@/common/helpers/variables.helper';
import _ from 'lodash';

@Service()
export class ProcessTwApprovalGatewayUseCase
    implements IProcessGatewayVariablesUseCase
{
    @Inject()
    private companySvc: CompanyService;

    async genTaskGatewayVariables(
        formInstance: FormInstance,
        form: Form,
        formData: JSON,
        approved: boolean,
        variables: Variables,
    ) {
        const step3 = this.step3(formInstance, form, formData);
        variables['Activity_Step3_gateway'] = {
            value: step3,
        };
        const step5 = await this.step5(formInstance, form, formData);
        variables['Activity_Step5_gateway'] = {
            value: step5,
        };
        return variables;
    }

    private step3(formInstance: FormInstance, form: Form, formData: JSON) {
        const values = formInstance.values;
        let requireCountersign: boolean = false;
        if (values?.requireCountersign && values?.requireCountersign == 'Y') {
            requireCountersign = true;
        }
        const result = requireCountersign ? 1 : 2;
        return result;
    }

    /**
     * 條件跳過簽呈負責人，目前有
     * - 科林創投: 使用公司 code: 91097947
     * @param formInstance
     * @param form
     * @param formData
     * @returns
     */
    private async step5(
        formInstance: FormInstance,
        form: Form,
        formData: JSON,
    ) {
        const values = formInstance.values;
        const initiator = values?.initiator;
        const skipCompanies = [
            '91097947', // 科林創投
        ];
        const user = VariablesHelper.genUsers(
            initiator,
        ) as VariablesHelper.CountersignerVariable;
        let isSkip = false;
        if (user && _.has(user, 'company_id')) {
            const company = await this.companySvc.findOne(user.company_id);
            if (company && skipCompanies.includes(company.code)) {
                isSkip = true;
            }
        }
        const result = isSkip ? 2 : 1;
        return result;
    }
}
