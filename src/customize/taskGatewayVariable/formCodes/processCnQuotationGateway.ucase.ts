import { Service } from 'typedi';
import { IProcessGatewayVariablesUseCase } from '../taskGatewayVariable.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { Form } from '@/common/models/clinico/bpm/form.model';

@Service()
export class ProcessCnQuotationGatewayUseCase
    implements IProcessGatewayVariablesUseCase
{
    async genTaskGatewayVariables(
        formInstance: FormInstance,
        form: Form,
        formData: JSON,
        approved: boolean,
        variables: Variables,
    ) {
        const gatewayStep3 = this.step3(formInstance, form, formData);
        const gatewayStep4 = this.step4(formInstance, form, formData);
        const gatewayStep5 = this.step5(formInstance, form, formData);
        variables['Activity_Step2_gateway'] = {
            value: 1,
        };
        variables['Activity_Step3_gateway'] = {
            value: gatewayStep3,
        };
        variables['Activity_Step4_gateway'] = {
            value: gatewayStep4,
        };
        variables['Activity_Step5_gateway'] = {
            value: gatewayStep5,
        };
        return variables;
    }

    /**
     * 1. 事業部主管
     * 以下條件滿足一項即成立
     * - 客戶為「經銷商」
     * - 訂單金額 > 50 萬
     * - 折扣率 < 60%
     * - LS (研究費) > 0 元
     * @param formInstance
     * @param form
     * @param formData
     * @returns
     */
    private step3(formInstance: FormInstance, form: Form, formData: JSON) {
        const result = 1;
        return result;
    }
    /**
     * 2. 財務主管
     * 以下條件滿足一項即成立
     * - 訂單金額 > 50 萬
     * - 折扣率 < 55%
     * - 毛利 < 30%
     * - LS (研究費) 佔比 > 5%
     * @param formInstance
     * @param form
     * @param formData
     * @returns
     */
    private step4(formInstance: FormInstance, form: Form, formData: JSON) {
        const result = 1;
        return result;
    }

    /**
     * 3. 總經理
     * 以下條件滿足一項即成立
     * - 折扣率 < 50%
     * - 毛利 < 25%
     * - LS (研究費) 佔比 > 10%
     * @param formInstance
     * @param form
     * @param formData
     * @returns
     */
    private step5(formInstance: FormInstance, form: Form, formData: JSON) {
        const result = 1;
        return result;
    }
}
