import { Form } from '@/common/models/clinico/bpm/form.model';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Variables } from '@/modules/camunda/types/processInstance.type';

export type GenTaskGatewayVariables = (
    formInstance: FormInstance,
    form: Form,
    formData: JSON,
    approved: boolean,
    variables: Variables,
) => Promise<Variables>;

export interface IProcessGatewayVariablesUseCase {
    genTaskGatewayVariables: GenTaskGatewayVariables;
}

export type ProcessKeyGatewayVariablesObj = {
    [processKey: string]: GenTaskGatewayVariables;
};
