import { Department } from '@/common/models/clinico/core/department.model';

export enum EnumGeneralManagerProcessType {
    Only = 'only',
    Skip = 'skip',
}

export type AssigneeType = 'users' | 'supervisors';

export type Assignee = {
    companyId?: number;
    companyName?: string;
    departmentId?: number;
    departmentCode?: string;
    departmentName?: string;
    userId?: number;
    userCode?: string;
    userName?: string;
    parentDepartmentId?: number;
    depth?: number;
    /**
     * 紀錄是否從 direct_departments 取得的 supervisor
     * true: 從 direct_departments 取得的 supervisor
     * false: core.positions 取得的非主管人員
     */
    notDirect?: boolean;
};

export type ExAssignee = Assignee & {
    assigneeId: string;
    parentAssigneeId?: string;
    prefix: string;
    suffix: string;
    name: string;
    [key: string]: any;
};

export type ExAssigneeByGroup = ExAssignee & {
    assignees: ExAssignee[];
    children?: ExAssigneeByGroup[];
    depth: number;
};

export type SearchSupervisorsParams = {
    regionId?: number;
    departmentCodes?: string[];
};

export type AssigneeByGroupDept = Assignee & {
    assignees: Assignee[];
    children?: AssigneeByGroupDept[];
    depth: number;
};

export type SearchDepartmentSupervisorParams = {
    companyId?: number;
    departmentId?: number;
    departmentCode?: string;
    supervisorId?: number;
};

export type SearchUserDepartmentSupervisorParams = {
    departmentId: number;
    userId: number;
};

export type FindDepartmentSupervisorResult = {
    assignee?: Assignee;
    parentDepartment?: Department;
};

export type SearchSupervisorsByDeptCodeParams = {
    /** 部門代碼 */
    code: string;
    /** 包含/略過總經理以上層級 */
    generalManagerType?: EnumGeneralManagerProcessType;
    /** 是否由上至下排序 (預設為 '否') */
    orderBy?: boolean;
    regionId?: number;
};

export type SearchSupervisorsByDeptCodesParams = {
    /** 部門代碼 */
    codes: string[];
    /** 包含/略過總經理以上層級 */
    generalManagerType?: EnumGeneralManagerProcessType;
    /** 是否由上至下排序 (預設為 '否') */
    orderBy?: boolean;
    regionId?: number;
};
