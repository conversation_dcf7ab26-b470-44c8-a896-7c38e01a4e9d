import { ClinicoDataSource } from '@/common/databases/clinico.database';
import {
    SearchSupervisorsByDeptCodesParams,
    Assignee,
    EnumGeneralManagerProcessType,
    SearchSupervisorsByDeptCodeParams,
} from '../types/assignee.type';
import { DepartmentService } from '@/modules/organization/providers/department.service';
import _ from 'lodash';
import { Inject, Service } from 'typedi';

@Service()
export class AssigneeRepository {
    @Inject()
    private departmentService: DepartmentService;

    /**
     * 由對象部門開始，向下取得所有主管
     */
    async searchSupervisorsByDeptCodes(
        params: SearchSupervisorsByDeptCodesParams,
    ): Promise<Assignee[]> {
        const sql = `
            WITH RECURSIVE direct_departments(
                id, company_id, comopany_name, code, name, 
                user_id, user_code, user_name, parent_id, dep_type, depth
            ) AS (
                SELECT 
                     d.id
                    ,d.company_id
                    ,c.name           AS comopany_name
                    ,d.code
                    ,d.name
                    ,d.supervisor_id  AS user_id
                    ,u.code           AS user_code
                    ,u.name           AS user_name
                    ,c.id
                    ,d.dep_type
                    ,1                AS depth
                FROM      core.departments d
                     JOIN core.companies   c ON d.company_id = c.id
                LEFT JOIN core.users       u ON d.supervisor_id = u.id
                WHERE d.code IN (
                    ${params.codes.map((_, index) => `$${index + 1}`)}
                ) -- 要查詢的對像部門
                  AND d.disabled = false
                  AND u.disabled = false
                UNION ALL
                SELECT 
                     d.id
                    ,d.company_id
                    ,c.name           AS comopany_name
                    ,d.code
                    ,d.name
                    ,d.supervisor_id  AS user_id
                    ,u.code           AS user_code
                    ,u.name           AS user_name
                    ,d.parent_id
                    ,d.dep_type
                    ,dp.depth + 1     AS depth
                FROM      core.departments    d
                     JOIN core.companies      c ON d.company_id = c.id
                LEFT JOIN core.users          u ON d.supervisor_id = u.id
                     JOIN direct_departments dp on d.parent_id = dp.id
                WHERE d.disabled = false AND u.disabled = false
            ),
            company_departments(
                id, company_id, company_name, code, name, 
                user_id, user_code, user_name, parent_id, dep_type, depth
            ) AS (
                SELECT 
                    distinct
                    c.id as id,
                    c.id as company_id,
                    c.name AS company_name,
                    c.code,
                    c.name,
                    null::bigint AS user_id,
                    null::text AS user_code,
                    null::text AS user_name,
                    1::bigint AS parent_id,
                    'root' as dep_type,
                    0 AS depth
                FROM core.companies c
                JOIN core.departments d ON d.company_id = c.id
                WHERE d.code IN (
                    ${params.codes.map((_, index) => `$${index + 1}`)}
                )
                and
                    d.disabled = false
                group by c.id, c."name" , c.code
            )
            select * from company_departments
            union all
            SELECT * FROM direct_departments ${
                params.orderBy ? 'ORDER BY depth' : ''
            };
        `;
        let result = await ClinicoDataSource.manager.query(sql, params.codes);

        result = await this.generalManagerProcess(
            result,
            params.generalManagerType,
        );

        const assignees: Assignee[] = [];
        for (const row of result) {
            assignees.push({
                companyId: row['company_id'],
                companyName: row['comopany_name'],
                departmentCode: row['code'],
                departmentName: row['name'],
                departmentId: row['id'],
                userId: row['user_id'],
                userCode: row['user_code'],
                userName: row['user_name'],
                parentDepartmentId: row['parent_id'],
                depth: row['depth'],
            });
        }
        return assignees;
    }

    /**
     * 由對象部門開始，取得打平的所有人員資訊
     * 以原 direct_departments 的樹狀為基準，往下產生人員
     * 而前端原設計也只會拿 assignees[0]來用...TM...
     * 所以都塞在 assignees 是沒用的要用 grouping 再處理節點
     */
    async searchUsersByDeptCodes(
        params: SearchSupervisorsByDeptCodesParams,
    ): Promise<Assignee[]> {
        // 人員打平
        /* const sql = `
            WITH RECURSIVE direct_departments(
                id, company_id, company_name, code, name, 
                user_id, user_code, user_name, parent_id, dep_type, depth
            ) AS (
                SELECT 
                     d.id
                    ,d.company_id
                    ,c.name           AS company_name
                    ,d.code
                    ,d.name
                    ,d.supervisor_id  AS user_id
                    ,u.code           AS user_code
                    ,u.name           AS user_name
                    ,d.parent_id
                    ,d.dep_type
                    ,1                AS depth
                FROM      core.departments d
                     JOIN core.companies   c ON d.company_id = c.id
                LEFT JOIN core.users       u ON d.supervisor_id = u.id
                WHERE d.code IN (
                    ${params.codes.map((_, index) => `$${index + 1}`)}
                ) -- 要查詢的對像部門
                  AND d.disabled = false
                UNION ALL
                SELECT 
                     d.id
                    ,d.company_id
                    ,c.name           AS company_name
                    ,d.code
                    ,d.name
                    ,d.supervisor_id  AS user_id
                    ,u.code           AS user_code
                    ,u.name           AS user_name
                    ,d.parent_id
                    ,d.dep_type
                    ,dp.depth + 1     AS depth
                FROM      core.departments    d
                     JOIN core.companies      c ON d.company_id = c.id
                LEFT JOIN core.users          u ON d.supervisor_id = u.id
                     JOIN direct_departments dp on d.parent_id = dp.id
                WHERE d.disabled = false
            )
            -- SELECT * FROM direct_departments 
            -- union all
            select
                up.department_id  as id
                ,d.company_id  as company_id
                ,c.name as company_name
                ,d.code as code
                ,d.name as name
                ,u.id as user_id
                ,u.code as user_code
                ,u.name as user_name
                ,0 as parent_id
                ,d.dep_type as dep_type
                ,0 as depth
            from core.user_positions up 
            join core.users u on u.id = up.user_id 
            join core.departments d on up.department_id = d.id
            join core.companies c on c.id = d.company_id
            where
                d.code in (SELECT code FROM direct_departments dd group by code, parent_id)
                --and
                --u.code not in (select distinct user_code from direct_departments where user_code is not null)
                and
                up.disabled = false
        `;
        let result = await ClinicoDataSource.manager.query(sql, params.codes); */
        // 試試樹狀，看起來前端也只會拿 assignees[0]來用...TM...
        const sql = `
            WITH RECURSIVE direct_departments(
                id, company_id, company_name, code, name, 
                user_id, user_code, user_name, parent_id, dep_type, depth
            ) AS (
                SELECT 
                        d.id
                    ,d.company_id
                    ,c.name           AS company_name
                    ,d.code
                    ,d.name
                    ,d.supervisor_id  AS user_id
                    ,u.code           AS user_code
                    ,u.name           AS user_name
                    -- ,d.parent_id
                    ,c.id as parent_id
                    ,d.dep_type
                    ,1                AS depth
                FROM      core.departments d
                        JOIN core.companies   c ON d.company_id = c.id
                LEFT JOIN core.users       u ON d.supervisor_id = u.id
            --    left join company_departments cp on d.company_id = cp.company_id
                WHERE d.code IN (
                    ${params.codes.map((_, index) => `$${index + 1}`)}
                ) -- 要查詢的對像部門
                    AND d.disabled = false
                    AND u.disabled = false
                UNION ALL
                SELECT 
                        d.id
                    ,d.company_id
                    ,c.name           AS company_name
                    ,d.code
                    ,d.name
                    ,d.supervisor_id  AS user_id
                    ,u.code           AS user_code
                    ,u.name           AS user_name
                    ,d.parent_id
                    ,d.dep_type
                    ,dp.depth + 1     AS depth
                FROM      core.departments    d
                        JOIN core.companies      c ON d.company_id = c.id
                LEFT JOIN core.users          u ON d.supervisor_id = u.id
                        JOIN direct_departments dp on d.parent_id = dp.id
                WHERE 
                d.disabled = false 
                AND u.disabled = false
            ),
            company_departments(
                id, company_id, company_name, code, name, 
                user_id, user_code, user_name, parent_id, dep_type, depth
            ) AS (
                SELECT 
                    distinct
                    c.id as id,
                    c.id as company_id,
                    c.name AS company_name,
                    c.code,
                    c.name,
                    null::bigint AS user_id,
                    null::text AS user_code,
                    null::text AS user_name,
                    1::bigint AS parent_id,
                    'root' as dep_type,
                    0 AS depth
                FROM core.companies c
                JOIN core.departments d ON d.company_id = c.id
                WHERE d.code IN (
                    ${params.codes.map((_, index) => `$${index + 1}`)}
                )
                and
                    d.disabled = false
                group by c.id, c."name" , c.code
            )
            select *, false as not_direct from company_departments
            union all
            SELECT *, false as not_direct FROM direct_departments 
            where user_id is not null
            union all
            select
                dd.id as id
                ,dd.company_id as company_id
                ,dd.company_name as company_name
                ,dd.code as code
                ,dd.name as name
                ,u.id as user_id
                ,u.code as user_code
                ,u.name as user_name
                ,dd.id as parent_id
                ,dd.dep_type as dep_type
                ,cp.max_depth + 1 as depth
                ,true as not_direct
            from direct_departments dd
            left join core.user_positions up on dd.id = up.department_id
            left join core.departments d on up.department_id = d.id
            left join core.users u on up.user_id = u.id 
            left join (SELECT code, max(depth) as max_depth FROM direct_departments dd group by code) as cp on cp.code = dd.code
            --left join (SELECT code, parent_id, max(depth) as max_depth FROM direct_departments dd group by code, parent_id) as cp on cp.code = dd.code
            where 
                u.code not in (select distinct user_code from direct_departments where user_code is not null)
                and 
                d.code in (SELECT code FROM direct_departments dd group by code, parent_id)
                and 
                up.disabled = false
                AND u.disabled = false
            ${params.orderBy ? 'ORDER BY depth' : ''};
        `;

        let result = await ClinicoDataSource.manager.query(sql, params.codes);
        result = await this.generalManagerProcess(
            result,
            params.generalManagerType,
        );
        console.log('params.generalManagerType: ', params.generalManagerType);

        const assignees: Assignee[] = [];
        for (const row of result) {
            assignees.push({
                companyId: row['company_id'],
                companyName: row['company_name'],
                departmentCode: row['code'],
                departmentName: row['name'],
                departmentId: row['id'],
                userId: row['user_id'],
                userCode: row['user_code'],
                userName: row['user_name'],
                parentDepartmentId: row['parent_id'],
                depth: row['depth'],
                notDirect: row['not_direct'],
            });
        }
        return assignees;
    }

    /**
     * 由對象部門開始，向上取得所有直屬主管
     * @param code 部門編號
     * @param orderBy 是否由上至下排序 (預設為 '否')
     */
    async findAllDirectSupervisors(
        params: SearchSupervisorsByDeptCodeParams,
    ): Promise<Assignee[]> {
        const sql = `
            WITH RECURSIVE direct_departments(
                id, company_id, comopany_name, code, name, 
                user_id, user_code, user_name, parent_id, depth
            ) AS (
                SELECT 
                     d.id
                    ,d.company_id
                    ,c.name           AS comopany_name
                    ,d.code
                    ,d.name
                    ,d.supervisor_id  AS user_id
                    ,u.code           AS user_code
                    ,u.name           AS user_name
                    ,d.parent_id
                    ,1                AS depth
                FROM      core.departments d
                     JOIN core.companies   c ON d.company_id = c.id
                LEFT JOIN core.users       u ON d.supervisor_id = u.id
                WHERE d.code = $1 -- 要查詢的對像部門
                  AND d.disabled = false
                  AND u.disabled = false
                UNION ALL
                SELECT 
                     dp.id
                    ,dp.company_id
                    ,c.name           AS comopany_name
                    ,dp.code
                    ,dp.name
                    ,dp.supervisor_id AS user_id
                    ,u.code           AS user_code
                    ,u.name           AS user_name
                    ,dp.parent_id
                    ,d.depth - 1      AS depth
                FROM      core.departments   dp
                     JOIN core.companies      c ON dp.company_id = c.id
                LEFT JOIN core.users          u ON dp.supervisor_id = u.id
                     JOIN direct_departments  d on d.parent_id = dp.id
                WHERE dp.disabled = false
                  AND dp.id != 1 -- G000 科林集團
                  AND u.disabled = false
            )
            SELECT * FROM direct_departments ${
                params.orderBy ? 'ORDER BY depth' : ''
            };
        `;
        let result = await ClinicoDataSource.manager.query(sql, [params.code]);

        result = await this.generalManagerProcess(
            result,
            params.generalManagerType,
        );

        const assignees: Assignee[] = [];
        for (const row of result) {
            assignees.push({
                companyId: row['company_id'],
                companyName: row['comopany_name'],
                departmentCode: row['code'],
                departmentName: row['name'],
                departmentId: row['id'],
                userId: row['user_id'],
                userCode: row['user_code'],
                userName: row['user_name'],
                parentDepartmentId: row['parent_id'],
                depth: row['depth'],
            });
        }
        return assignees;
    }

    private async generalManagerProcess(
        result: any,
        generalManagerType?: EnumGeneralManagerProcessType,
    ): Promise<any> {
        if (!generalManagerType) {
            return result;
        }

        switch (generalManagerType) {
            case EnumGeneralManagerProcessType.Only:
                {
                    const skipDepts =
                        await this.departmentService.findByDepTypes(
                            ['4', '5'], // 4,5 為副董級以上,
                        );
                    const skipDeptsByApprovalLevels =
                        await this.departmentService.findByApprovalLevels(
                            ['L02', 'L03'], // L02, L03 為副董級以上,
                        );
                    const skipDeptIds = skipDepts
                        .concat(skipDeptsByApprovalLevels)
                        .map((dep) => dep.id);
                    result = this.skipDepartmentCodes(result, skipDeptIds);

                    const generalManagerDepts =
                        await this.departmentService.findByDepTypes(['6']); // 6為總經理級
                    const generalManagerDeptIds = generalManagerDepts.map(
                        (dep) => dep.id,
                    );
                    result = result.filter(
                        (data) =>
                            generalManagerDeptIds.includes(data['id']) ||
                            data['parent_id'] == 1, //保留公司節點
                    );
                }
                break;
            case EnumGeneralManagerProcessType.Skip:
            default:
                {
                    const skipDepts =
                        await this.departmentService.findByDepTypes(
                            ['4', '5'], // 4,5,6為總經理級以上,
                        );
                    const skipDeptsByApprovalLevels =
                        await this.departmentService.findByApprovalLevels(
                            ['L02', 'L03'], // L02, L03 為副董級以上,
                        );
                    const skipDeptIds = skipDepts
                        .concat(skipDeptsByApprovalLevels)
                        //耳科事業群N000例外，需選擇David才能起單，不然無部門主管可選。
                        //搭配流程設計中的參數`allow_skip_type`，自動通過並隱藏部門主管
                        .filter((data) => data.code != 'N000')
                        .map((dep) => dep.id);
                    result = this.skipDepartmentCodes(result, skipDeptIds);
                }
                break;
        }
        return result;
    }

    private skipDepartmentCodes(source: any, skipIds: number[]) {
        let data = source;
        for (const skipId of skipIds) {
            data = this.changeParentDepartmentId(data, skipId);
        }
        return data;
    }

    private changeParentDepartmentId(source: any, skipId: number) {
        const skipDepts = source.filter((data) => data['id'] == skipId);
        const skipDeptIds = skipDepts.map((data) => data['id']);

        for (const skipDept of skipDepts) {
            const parents = source.filter(
                (data) => data['parent_id'] == skipDept['id'],
            );
            parents.forEach((parent) => {
                parent['parent_id'] = skipDept['parent_id'];
            });
        }

        const res = _.cloneDeep(
            source.filter((data) => !skipDeptIds.includes(data['id'])),
        );
        return res;
    }
}
