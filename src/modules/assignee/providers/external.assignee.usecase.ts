import { Inject, Service } from 'typedi';
import { ExternalAssigneeService } from './external.assignee.service';
import { User } from '@/common/models/clinico/core/user.model';
import { FormExternalAssigneeValue } from '@/modules/form/form/types/form.assignees.type';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { UserService } from '@/modules/organization/providers/user.service';

@Service()
export class ExternalAssigneeUseCase {
    @Inject()
    private formRepo: FormRepository;
    @Inject()
    private externalAssigneeSvc: ExternalAssigneeService;
    @Inject()
    private userService: UserService;

    async getExAssigneesByTreeNodes(
        formId: number,
        key: string,
        treeNodes: string[] = [],
    ) {
        const formExAssignees =
            await this.formRepo.findOneFormExternalAssigneesByKeyId(
                formId,
                key,
            );
        const filterExAssignees =
            this.externalAssigneeSvc.filterFormExternalAssigneeValue(
                formExAssignees,
                treeNodes,
            );
        const userCodes =
            this.getUserCodesByExternalAssignees(filterExAssignees);
        const { rows } = await this.userService.search({ codes: userCodes });
        const exAssigneeUserInfoMap =
            this.externalAssigneeSvc.genExAssigneeUserInfoMapByUserCode(rows);
        const exAssigneeMap =
            this.externalAssigneeSvc.genExAssigneeMapByAssigneeIdKey(
                filterExAssignees,
                exAssigneeUserInfoMap,
            );
        const exAssignees = Array.from(exAssigneeMap.values()).flat();
        return exAssignees;
    }

    getUserCodesByExternalAssignees(exAssignees: FormExternalAssigneeValue[]) {
        const userCodeSet =
            this.externalAssigneeSvc.getExAssigneeUserCodeSet(exAssignees);
        return Array.from(userCodeSet.values());
    }

    genExAssigneesByUsers(
        exAssignees: FormExternalAssigneeValue[],
        users: User[],
    ) {
        return;
    }
}
