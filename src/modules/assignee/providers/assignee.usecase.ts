import { Inject, Service } from 'typedi';
import {
    Assignee,
    AssigneeByGroupDept,
    AssigneeType,
    EnumGeneralManagerProcessType,
} from '../types/assignee.type';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { DepartmentService } from '@/modules/organization/providers/department.service';
import { AssigneeRepository } from './assignee.repository';
import {
    filterMappingMatchQueryData,
    FilterMappingMetadata,
} from '@/common/helpers/filterMappingMatchQueryData';
import { FormUseCase } from '@/modules/form/form/providers/form.usecase';
import { UserUseCase } from '@/modules/organization/providers/user.usecase';

@Service()
export class AssigneeUseCase {
    @Inject()
    private departmentService: DepartmentService;
    @Inject()
    private formRepo: FormRepository;
    @Inject()
    private repo: AssigneeRepository;
    @Inject()
    private formUCase: FormUseCase;
    @Inject()
    private userUCase: UserUseCase;

    /**
     * 依據表單設定的過濾條件，篩選簽核樹狀結構
     * @param params
     * @returns
     */
    async filterAssigneesByFormConfFilter(params: {
        assignees: Assignee[];
        formId?: number;
        userId?: number;
        type?: AssigneeType;
    }) {
        const { assignees, formId, userId } = params;
        if (!formId || !userId) {
            return assignees;
        }

        const activeFormConfFilterMetadatas =
            await this.formUCase.getMatchUserFormConfFilterMetadatas(
                formId,
                userId,
            );
        if (!activeFormConfFilterMetadatas?.length) {
            return assignees;
        }

        const filterMetadatas: FilterMappingMetadata[] =
            activeFormConfFilterMetadatas
                .filter((i) => i.assigneesFilter)
                .map((i) => ({ filter: i.assigneesFilter }));

        if (!filterMetadatas.length) {
            return assignees;
        }
        const userInfo = params.userId
            ? await this.userUCase.getFilterUserInfoById(params.userId)
            : null;
        const filterAssignees = filterMappingMatchQueryData<Assignee>(
            assignees,
            filterMetadatas,
            {
                userInfo,
                type: params.type,
            },
        );
        return Array.from(new Set(filterAssignees));
    }

    /**
     * 由「公司級別」部門開始，向下取得所有主管
     */
    async findAllSupervisorsFromRoot(params: {
        generalManagerType?: EnumGeneralManagerProcessType;
        regionId?: number;
    }): Promise<Assignee[]> {
        const depts =
            await this.departmentService.findAllCompanyLevelDepartments(
                params.regionId,
            );
        const codes = depts.map((dept) => dept.code);
        if (codes.length == 0) return [];
        return this.repo.searchSupervisorsByDeptCodes({
            codes,
            generalManagerType: params.generalManagerType,
            orderBy: true,
            regionId: params.regionId,
        });
    }

    /**
     * 由「公司級別」部門開始，向下取得所有人員
     */
    async findAllUsersFromRoot(params: {
        generalManagerType?: EnumGeneralManagerProcessType;
        regionId?: number;
    }): Promise<Assignee[]> {
        const depts =
            await this.departmentService.findAllCompanyLevelDepartments(
                params.regionId,
            );
        const codes = depts.map((dept) => dept.code);
        return this.repo.searchUsersByDeptCodes({
            codes,
            generalManagerType: params.generalManagerType,
            orderBy: true,
            regionId: params.regionId,
        });
    }

    /**
     * 群組化簽核人結構
     * 前端也只會拿 assignees[0]來用
     * 資料加入 notDirect 決定是否只產生節點人員
     * notDirect: false 原簽核人結構
     * notDirect: true  只產生節點人員: ex: 部門人員
     * @param assignees
     * @param depth 往下拿的層數
     */
    async grouping(params: {
        assignees: Assignee[];
        depth?: number;
        formId?: number;
    }): Promise<AssigneeByGroupDept[]> {
        const { assignees, depth, formId } = params;
        const skipAssignees = await this.getSkipUserCodes(formId);
        const result: AssigneeByGroupDept[] = [];

        function* allNodes(): Generator<AssigneeByGroupDept> {
            function* nodes(node: AssigneeByGroupDept) {
                yield node;
                if (node.children?.length) {
                    for (const child of node.children) {
                        yield* nodes(child);
                    }
                }
            }
            for (const node of result) {
                yield* nodes(node);
            }
        }

        for (const assignee of assignees) {
            let parentNode: AssigneeByGroupDept | null = null;
            for (const node of allNodes()) {
                if (assignee.parentDepartmentId == node.departmentId) {
                    parentNode = node;
                    break;
                }
            }

            if (
                depth &&
                depth > 0 &&
                parentNode?.depth &&
                parentNode.depth + 1 > depth
            ) {
                continue;
            }

            if (parentNode) {
                if (!parentNode.children) {
                    parentNode.children = [];
                }

                const targetNode = (parentNode.children || []).find((n) => {
                    return n.departmentId == assignee.departmentId;
                });
                if (targetNode && !assignee.notDirect) {
                    if (!targetNode.assignees) targetNode.assignees = [];
                    targetNode.assignees.push(assignee);
                } else {
                    // 產生人員節點 ex: 部門人員
                    if (assignee.notDirect) {
                        parentNode.children.push({
                            ...assignee,
                            assignees: [assignee],
                            children: [],
                            depth: parentNode.depth + 1,
                        });
                        continue;
                    }
                    parentNode.children.push({
                        ...assignee,
                        assignees:
                            !assignee.userCode ||
                            assignee.parentDepartmentId == 1 || //排除公司root
                            skipAssignees.includes(assignee.userCode) //排除指定user
                                ? []
                                : [assignee],
                        children: [],
                        depth: parentNode.depth + 1,
                    });
                }
            } else {
                if (assignee.notDirect) continue;
                result.push({
                    ...assignee,
                    assignees:
                        !assignee.userCode ||
                        assignee.parentDepartmentId == 1 || //排除公司root
                        skipAssignees.includes(assignee.userCode) //排除指定user
                            ? []
                            : [assignee],
                    children: [],
                    depth: 1,
                });
            }
        }
        return result;
    }

    async getSkipUserCodes(formId?: number) {
        const userCodes = ['L0011']; //排除Barry
        if (!formId) {
            return userCodes;
        }
        const form = await this.formRepo.findOne(formId);
        if (!form) {
            return userCodes;
        }
        switch (form.code) {
            case 'Approval': //簽呈
                return userCodes.concat(['L0528', 'L0528-W']); //排除David
            case 'CN_Approval': //中國簽呈
                return userCodes.concat(['L0528', 'L0528-W']); //排除David
            default:
                return userCodes;
        }
    }
}
