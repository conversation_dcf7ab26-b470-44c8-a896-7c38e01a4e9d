import { Service } from 'typedi';
import {
    FormExternal<PERSON>signee,
    FormExternal<PERSON>signee<PERSON>ser,
    FormExternalAssigneeValue,
    FormExternalNode,
} from '../../form/form/types/form.assignees.type';
import _ from 'lodash';
import { User } from '@/common/models/clinico/core/user.model';
import { ExAssignee, ExAssigneeByGroup } from '../types/assignee.type';

@Service()
export class ExternalAssigneeService {
    grouping(params: {
        exAssignees: ExAssignee[];
        depth?: number;
    }): ExAssigneeByGroup[] {
        const { exAssignees, depth } = params;
        const result: ExAssigneeByGroup[] = [];

        function* allNodes(): Generator<ExAssigneeByGroup> {
            function* nodes(node: ExAssigneeByGroup) {
                yield node;
                if (node.children?.length) {
                    for (const child of node.children) {
                        yield* nodes(child);
                    }
                }
            }
            for (const node of result) {
                yield* nodes(node);
            }
        }

        for (const assignee of exAssignees) {
            let parentNode: ExAssigneeByGroup | null = null;
            for (const node of allNodes()) {
                if (assignee.parentAssigneeId == node.assigneeId) {
                    parentNode = node;
                    break;
                }
            }

            if (
                depth &&
                depth > 0 &&
                parentNode?.depth &&
                parentNode.depth + 1 > depth
            ) {
                continue;
            }

            if (parentNode) {
                if (!parentNode.children) {
                    parentNode.children = [];
                }

                const targetNode = (parentNode.children || []).find((n) => {
                    return n.assigneeId == assignee.assigneeId;
                });
                if (targetNode && !assignee.notDirect) {
                    if (!targetNode.assignees) targetNode.assignees = [];
                    targetNode.assignees.push(this.genAssigneeUserId(assignee));
                } else {
                    // 產生人員節點 ex: 部門人員
                    if (assignee.notDirect) {
                        parentNode.children.push({
                            ...assignee,
                            assignees: [this.genAssigneeUserId(assignee)],
                            children: [],
                            depth: parentNode.depth + 1,
                        });
                        continue;
                    }
                    parentNode.children.push({
                        ...assignee,
                        assignees: !assignee.userCode
                            ? []
                            : [this.genAssigneeUserId(assignee)],
                        children: [],
                        depth: parentNode.depth + 1,
                    });
                }
            } else {
                if (assignee.notDirect) continue;
                result.push({
                    ...assignee,
                    assignees: !assignee.userCode
                        ? []
                        : [this.genAssigneeUserId(assignee)],
                    children: [],
                    depth: 1,
                });
            }
        }
        return result;
    }

    genAssigneeUserId(assignee: ExAssignee) {
        const exUserAssigneeId = [
            assignee.assigneeId,
            assignee.companyId,
            assignee.departmentCode,
            assignee.userCode,
            assignee.id ?? assignee.name,
        ]
            .filter((i) => i)
            .join('_');
        const assigneeUser = {
            ...assignee,
        };
        assigneeUser.assigneeId = exUserAssigneeId;
        return assigneeUser;
    }

    filterFormExternalAssigneeValue(
        formExAssignees: FormExternalAssigneeValue[],
        treeNodes: string[] = [],
    ) {
        const data = formExAssignees.filter((item) => {
            const nodes = item.nodes.map((i) => i.value);
            const isMatchFilters = (item: string) =>
                nodes.length == 0 || nodes.includes(item);
            return treeNodes.every(isMatchFilters);
        });
        return data;
    }

    getExAssigneeUserCodeSet(formExAssignees: FormExternalAssigneeValue[]) {
        const assigneeUserCodes = formExAssignees
            .map((item) => item.assignees)
            .flat()
            .map((item) => item.values)
            .flat()
            .filter((item) => !_.isUndefined(item.userCode))
            .map((item) => item.userCode ?? '');
        return new Set<string>(assigneeUserCodes);
    }

    treeByFormExternalAssigneeValue(exAssignee: FormExternalAssigneeValue[]) {
        return;
    }

    genAssigneeId(nodes: FormExternalNode[]) {
        return nodes.reduce((pre, cur) => {
            pre = `${pre.length > 0 ? `${pre}_` : ''}${cur.key}-${cur.value}`;
            return pre;
        }, '');
    }

    genExAssigneeMapByAssigneeIdKey(
        formExAssignees: FormExternalAssigneeValue[],
        exAssigneeUserInfoMap: Map<string, ExAssignee>,
    ) {
        return formExAssignees.reduce((pre, cur) => {
            this.exAssigneeMapByTreeNodes(pre, cur, exAssigneeUserInfoMap);
            return pre;
        }, new Map<string, ExAssignee[]>());
    }

    exAssigneeMapByTreeNodes(
        exAssigneeMap: Map<string, ExAssignee[]>,
        externalAssigneeValue: FormExternalAssigneeValue,
        exAssigneeUserInfoMap: Map<string, ExAssignee>,
    ) {
        const { nodes, assignees } = externalAssigneeValue;
        const nodesLength = nodes.length;
        for (let i = 1; i <= nodesLength; i++) {
            const selectNodes = nodes.slice(0, i);
            const assigneeId = this.genAssigneeId(selectNodes);
            const treeNodeAssignees = exAssigneeMap.get(assigneeId) ?? [];
            if (exAssigneeMap.has(assigneeId)) {
                continue;
            }
            if (i == nodesLength) {
                const exAssignees = this.genExAssigneesByFormAssignees(
                    nodes,
                    selectNodes,
                    assignees,
                    exAssigneeUserInfoMap,
                );
                treeNodeAssignees.push(...exAssignees);
            } else {
                const emptyExAssignee = this.genEmptyExAssignee(selectNodes);
                treeNodeAssignees.push(emptyExAssignee);
            }
            exAssigneeMap.set(assigneeId, treeNodeAssignees);
        }
        return exAssigneeMap;
    }

    getSelectNodesInfo(selectNodes: FormExternalNode[]) {
        const getAssigneeId = (slice: number) =>
            this.genAssigneeId(selectNodes.slice(0, slice));
        const getNodeName = (slice: number) =>
            selectNodes
                .slice(0, slice)
                .filter((i) => i.name?.length)
                .map((i) => i.name)
                .join('-');
        return {
            name: selectNodes.slice(-1)[0].name,
            deptName: getNodeName(selectNodes.length),
            depth: selectNodes.length,
            rootName: getNodeName(1),
            parentName: getNodeName(selectNodes.length - 1),
            assigneeId: getAssigneeId(selectNodes.length),
            parentAssigneeId: getAssigneeId(selectNodes.length - 1),
            rootId: getAssigneeId(0),
        };
    }

    genEmptyExAssignee(selectNodes: FormExternalNode[]): ExAssignee {
        const selectNodesInfo = this.getSelectNodesInfo(selectNodes);
        const exAssigneeInfo: ExAssignee = {
            name: selectNodesInfo.name ?? '',
            prefix: '',
            suffix: '',
            assigneeId: selectNodesInfo.assigneeId,
            parentAssigneeId: selectNodesInfo.parentAssigneeId,
            companyName: selectNodesInfo.rootName,
            departmentName: selectNodesInfo.deptName,
            depth: selectNodesInfo.depth,
        };
        return exAssigneeInfo;
    }
    genExAssigneesByFormAssignees(
        nodes: FormExternalNode[],
        selectNodes: FormExternalNode[],
        formAssignees: FormExternalAssignee[],
        exAssigneeUserInfoMap: Map<string, ExAssignee>,
    ): ExAssignee[] {
        const selectNodesInfo = this.getSelectNodesInfo(selectNodes);
        const isAssignee = nodes.length == selectNodes.length;
        const data = formAssignees.reduce(
            (pre, cur) => {
                const { name, values } = cur;
                for (const formAssigneeUser of values) {
                    const { prefix, suffix, userCode } = formAssigneeUser;
                    if (!userCode) continue;
                    const assigneeUser = exAssigneeUserInfoMap.get(userCode);
                    if (!assigneeUser) continue;
                    const exAssigneeInfo: ExAssignee = Object.assign(
                        {},
                        assigneeUser,
                        {
                            assigneeId: selectNodesInfo.assigneeId,
                            parentAssigneeId: selectNodesInfo.parentAssigneeId,
                            companyName: selectNodesInfo.rootName,
                            departmentName: selectNodesInfo.deptName,
                            depth: selectNodesInfo.depth,
                        },
                    );
                    if (isAssignee) {
                        exAssigneeInfo.name = name;
                        exAssigneeInfo.prefix = prefix ?? exAssigneeInfo.prefix;
                        exAssigneeInfo.suffix = suffix ?? exAssigneeInfo.suffix;
                        this.genExternalAssigneeInfoKeyValue(
                            formAssigneeUser,
                            exAssigneeInfo,
                        );
                    }
                    pre.push(exAssigneeInfo);
                }
                return pre;
            },
            <ExAssignee[]>[],
        );
        return data;
    }

    genExternalAssigneeInfoKeyValue(
        formAssigneeUser: FormExternalAssigneeUser,
        exAssigneeUserInfo: ExAssignee,
    ) {
        const keys = Object.keys(formAssigneeUser).filter(
            (i) => !['prefix', 'suffix', 'userCode', 'deptCode'].includes(i),
        );
        for (const key of keys) {
            exAssigneeUserInfo[key] = formAssigneeUser[key];
        }
    }

    genExAssigneeUserInfoMapByUserCode(users: User[]) {
        return users.reduce((pre, cur) => {
            const { userPositions, code } = cur;
            const userPosition = userPositions.filter((item) => !item.disabled);
            if (userPosition.length == 0) {
                return pre;
            }
            const exAssigneeUserInfo: ExAssignee = {
                assigneeId: '',
                parentAssigneeId: '',
                name: '',
                suffix: '',
                prefix: '',
                companyId: userPosition[0].department.companyId,
                companyName: userPosition[0].department.company.name,
                departmentId: userPosition[0].department.id,
                departmentCode: userPosition[0].department.code,
                departmentName: userPosition[0].department.name,
                userId: cur.id,
                userCode: cur.code,
                userName: cur.name,
            };
            pre.set(code, exAssigneeUserInfo);
            return pre;
        }, new Map<string, ExAssignee>());
    }
}
