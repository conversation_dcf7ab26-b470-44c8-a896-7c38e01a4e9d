import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { Helpers } from '@clinico/clinico-node-framework';
import {
    Body,
    Ctx,
    Get,
    JsonController,
    Param,
    Post,
    QueryParam,
    UseBefore,
} from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { EnumGeneralManagerProcessType } from '../types/assignee.type';
import { FormExtenalAssigneesParams } from '../types/assignee.controller.type';
import { ExternalAssigneeUseCase } from './external.assignee.usecase';
import { ExternalAssigneeService } from './external.assignee.service';
import { AssigneeUseCase } from './assignee.usecase';
import { AssigneeRepository } from './assignee.repository';
import { Context } from 'koa';
import { UserPayload } from '@/modules/organization/types/user.type';

@JsonController('/assignees')
@UseBefore(UserAuthKoaInterceptor)
@Service()
export class AssigneeController {
    @Inject()
    private assigneeUCase: AssigneeUseCase;
    @Inject()
    private assigneeRepo: AssigneeRepository;
    @Inject()
    private extenalAssigneeUCase: ExternalAssigneeUseCase;
    @Inject()
    private extenalAssigneeSvc: ExternalAssigneeService;

    @Post('/formExternalAssignees')
    async formExternalAssignees(@Body() params: FormExtenalAssigneesParams) {
        const { formId, nodes, key, grouping } = params;
        const exAssignees =
            await this.extenalAssigneeUCase.getExAssigneesByTreeNodes(
                formId,
                key,
                nodes,
            );

        const result = grouping
            ? this.extenalAssigneeSvc.grouping({ exAssignees })
            : exAssignees
                  .filter((i) => i.userCode)
                  .map((i) => this.extenalAssigneeSvc.genAssigneeUserId(i));

        return Helpers.Json.success(result);
    }

    @Get('/directSupervisors/:deptCode')
    async directSupervisors(
        @Param('deptCode') deptCode: string,
        @QueryParam('grouping') grouping?: boolean,
        @QueryParam('depth') depth?: number,
        @QueryParam('formId') formId?: number,
    ) {
        const assignees = await this.assigneeRepo.findAllDirectSupervisors({
            code: deptCode,
            generalManagerType: EnumGeneralManagerProcessType.Skip,
            orderBy: grouping,
        });
        const result = grouping
            ? await this.assigneeUCase.grouping({ assignees, depth })
            : assignees;

        return Helpers.Json.success(result);
    }

    @Get('/supervisors/region/:regionId')
    async supervisors(
        @Ctx() ctx: Context,
        @Param('regionId') regionId: number,
        @QueryParam('grouping') grouping?: boolean,
        @QueryParam('depth') depth?: number,
        @QueryParam('onlyGeneralManager') onlyGeneralManager?: boolean,
        @QueryParam('formId') formId?: number,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        const assignees = await this.assigneeUCase.findAllSupervisorsFromRoot({
            generalManagerType: onlyGeneralManager
                ? EnumGeneralManagerProcessType.Only
                : EnumGeneralManagerProcessType.Skip,
            regionId,
        });
        const matchAssignees =
            await this.assigneeUCase.filterAssigneesByFormConfFilter({
                assignees,
                formId,
                userId: userPayload.id,
                type: 'supervisors',
            });
        const result = grouping
            ? await this.assigneeUCase.grouping({
                  assignees: matchAssignees,
                  depth,
                  formId,
              })
            : matchAssignees;
        return Helpers.Json.success(result);
    }

    @Get('/users/region/:regionId')
    async users(
        @Ctx() ctx: Context,
        @Param('regionId') regionId: number,
        @QueryParam('grouping') grouping?: boolean,
        @QueryParam('depth') depth?: number,
        @QueryParam('onlyGeneralManager') onlyGeneralManager?: boolean,
        @QueryParam('formId') formId?: number,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        const assignees = await this.assigneeUCase.findAllUsersFromRoot({
            generalManagerType: onlyGeneralManager
                ? EnumGeneralManagerProcessType.Only
                : EnumGeneralManagerProcessType.Skip,
            regionId,
        });
        const matchAssignees =
            await this.assigneeUCase.filterAssigneesByFormConfFilter({
                assignees,
                formId,
                userId: userPayload.id,
                type: 'users',
            });
        const result = grouping
            ? await this.assigneeUCase.grouping({
                  assignees: matchAssignees,
                  depth,
                  formId,
              })
            : matchAssignees;

        return Helpers.Json.success(result);
    }
}
