import { WSService } from '@/common/services/ws.service';
import { Inject, Service } from 'typedi';
import {
    EnumNotificationEvent,
    Notification,
    NotificationKeys,
    NotificationParams,
} from '../types/notification.type';
import { SearchFormInstanceResult } from '@/modules/form/formInstance/types/forminstance.controller.type';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';

@Service()
export class NotificationService {
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private wsService: WSService;

    async publish(payload: NotificationKeys) {
        for (const key of payload.keys) {
            switch (key) {
                case EnumNotificationEvent.Pending:
                    {
                        const res = await this.notifications({
                            userId: payload.userId,
                        });
                        const pendingCount = res.pending ?? 0;
                        this.wsService.send(
                            payload.userId,
                            'pendingCount',
                            JSON.stringify(pendingCount),
                        );
                    }
                    break;
                default:
                    break;
            }
        }
    }

    async notifications(params: NotificationParams): Promise<Notification> {
        const pending = await this.currentAssigned(params);
        return {
            pending,
        };
    }

    async currentAssigned(params: NotificationParams): Promise<number> {
        let result: SearchFormInstanceResult | null = null;
        result = await this.formInstanceRepo.search({
            currentAssignedUserid: params.userId,
        });
        const rows = result.rows.filter((item) => item.status == 0);
        return rows.length;
    }
}
