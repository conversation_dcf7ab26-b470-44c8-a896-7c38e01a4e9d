import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { UserPayload } from '@/modules/organization/types/user.type';
import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'koa';
import { Ctx, Get, JsonController, UseBefore } from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { NotificationService } from './notification.service';

@JsonController('/notifications')
@UseBefore(UserAuthKoaInterceptor)
@Service()
export class NotificationController {
    @Inject()
    private notificationService: NotificationService;

    @Get('/')
    async notifications(@Ctx() ctx: Context) {
        const userPayload: UserPayload = ctx.req['user'];
        const notifications = await this.notificationService.notifications({
            userId: userPayload.id,
        });
        return Helpers.Json.success(notifications);
    }
}
