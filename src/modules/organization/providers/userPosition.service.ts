import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { Service } from 'typedi';
import { FindOptionsWhere, In } from 'typeorm';
import { SearchParams } from '../types/userPosition.type';
import { UserPosition } from '@/common/models/clinico/core/userPosition.model';
import _ from 'lodash';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';

@Service()
export class UserPositionService extends CommonService<UserPosition> {
    protected repository = ClinicoDataSource.getRepository(UserPosition);

    async findOneByParamsOrError(params: SearchParams) {
        const res = await this.search({ ...params });
        let data: UserPosition | null = null;
        if (res.count == 1) {
            data = res.rows[0];
        }
        if (res.count > 1) {
            const enableData = res.rows.filter((i) => !i.disabled);
            data = enableData.length > 0 ? enableData[0] : res.rows[0];
        }

        if (!data) {
            throw new BaseError(
                this.commonDataNotFoundMessage,
                httpStatus.NOT_FOUND,
            );
        }
        return data;
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<UserPosition>> {
        const filters: FindOptionsWhere<UserPosition> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.companyId) {
            filters.department = {
                companyId: params.companyId,
            };
        }
        if (params.companyCode) {
            filters.department = {
                company: {
                    code: params.companyCode,
                },
            };
        }
        if (params.departmentId) {
            filters.departmentId = params.departmentId;
        }
        if (params.departmentCode) {
            filters.department = {
                code: params.departmentCode,
            };
        }
        if (params.userId) {
            filters.userId = params.userId;
        }
        if (params.userIds) {
            filters.userId = In(params.userIds);
        }
        if (params.userCode) {
            filters.user = {
                code: params.userCode,
            };
        }
        if (params.title) {
            filters.title = params.title;
        }
        if (params.agentId) {
            filters.agentId = params.agentId;
        }
        if (params.agentCode) {
            filters.agent = {
                code: params.agentCode,
            };
        }
        if (!_.isUndefined(params.isMain) && !_.isNull(params.isMain)) {
            filters.isMain = params.isMain;
        }
        if (!_.isUndefined(params.disabled) && !_.isNull(params.disabled)) {
            filters.disabled = params.disabled;
        }

        const data = await this.repository.findAndCount({
            relations: {
                user: true,
                department: {
                    company: true,
                },
                agent: true,
            },
            where: filters,
            take: params.limit,
            skip: params.offset,
        });

        const result: CommonSearchResult<UserPosition> = {
            rows: data[0],
            count: data[1],
        };
        return result;
    }
}
