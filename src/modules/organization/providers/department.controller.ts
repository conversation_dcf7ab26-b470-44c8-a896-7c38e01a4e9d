import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { Helpers } from '@clinico/clinico-node-framework';
import {
    Get,
    JsonController,
    Param,
    QueryParams,
    UseBefore,
} from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { DepartmentService } from './department.service';
import { Department } from '@/common/models/clinico/core/department.model';
import { SearchParams } from '../types/department.controller.type';
import { OpenAPI } from 'routing-controllers-openapi';
import { OpenAPIParams } from '@/common/types/common.openapi.type';

@JsonController('/department')
@Service()
export class DepartmentController {
    @Inject()
    private departmentService: DepartmentService;

    @Get('/')
    @UseBefore(UserAuthKoaInterceptor)
    async findAll() {
        const departments = await this.departmentService.findAll();
        return Helpers.Json.success(<Department[]>departments);
    }

    @OpenAPI(OpenAPIParams.searchDepartment)
    @Get('/search')
    @UseBefore(UserAuthKoaInterceptor)
    async search(@QueryParams() params: SearchParams) {
        const departments = await this.departmentService.search(params);
        return Helpers.Json.success(departments);
    }

    @Get('/:id')
    @UseBefore(UserAuthKoaInterceptor)
    async findOne(@Param('id') id: number) {
        const department = await this.departmentService.findOne(id);
        return Helpers.Json.success(department);
    }
}
