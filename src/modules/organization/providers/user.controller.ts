import Koa from 'koa';
import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { User } from '@/common/models/clinico/core/user.model';
import { Helpers } from '@clinico/clinico-node-framework';
import {
    Ctx,
    Get,
    JsonController,
    Param,
    UseBefore,
} from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { UserService } from './user.service';

@JsonController('/user')
@Service()
export class UserController {
    @Inject()
    private userService: UserService;

    @Get('/:id')
    @UseBefore(UserAuthKoaInterceptor)
    async findOne(@Param('id') id: number, @Ctx() ctx: Koa.Context) {
        const user = await this.userService.findOneOrError(id);
        return Helpers.Json.success(<User>user);
    }
}
