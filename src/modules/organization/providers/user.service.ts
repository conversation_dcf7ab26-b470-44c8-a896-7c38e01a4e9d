import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { Service } from 'typedi';
import { FindOptionsWhere, In } from 'typeorm';
import { SearchParams } from '../types/user.type';
import { Like } from 'typeorm';
import { User } from '@/common/models/clinico/core/user.model';
import _ from 'lodash';
import { Utils } from '@clinico/clinico-node-framework';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { UserPosition } from '@/common/models/clinico/core/userPosition.model';

@Service()
export class UserService extends CommonService<User> {
    protected repository = ClinicoDataSource.getRepository(User);

    async findOneByCode(code: string): Promise<User | null> {
        const result = await this.search({ code: code, disabled: false });
        if (result.count == 0) {
            return null;
        }
        return result.rows[0];
    }

    async findOneOrErrorByCode(code: string): Promise<User> {
        const result = await this.search({ code: code, disabled: false });
        if (result.count != 1) {
            throw new BaseError('User not found', httpStatus.NOT_FOUND);
        }
        return result.rows[0];
    }

    async search(params: SearchParams): Promise<CommonSearchResult<User>> {
        const filters: FindOptionsWhere<User> = {};
        const userPositionFilters: FindOptionsWhere<UserPosition> = {};
        filters.deleted = false;
        userPositionFilters.disabled = false;
        if (params.id) {
            filters.id = params.id;
        }
        if (params.code) {
            filters.code = params.code;
        }
        if (params.codes) {
            filters.code = In(params.codes);
        }
        if (params.account) {
            filters.account = params.account;
        }
        if (params.name) {
            filters.name = Like(`%${params.name}%`);
        }
        if (params.email) {
            filters.email = params.email;
        }
        if (params.mobile) {
            filters.mobile = params.mobile;
        }
        if (!_.isUndefined(params.disabled) && !_.isNull(params.disabled)) {
            filters.disabled = params.disabled;
        }

        const data = await this.repository
            .createQueryBuilder('user')
            .innerJoinAndSelect(
                'user.userPositions',
                'userPosition',
                'userPosition.disabled = :disabled',
                { disabled: false },
            )
            .innerJoinAndSelect('userPosition.department', 'department')
            .innerJoinAndSelect('department.company', 'company')
            .where({ ...filters })
            .take(params.limit)
            .skip(params.offset)
            .getManyAndCount();

        const result: CommonSearchResult<User> = {
            rows: data[0],
            count: data[1],
        };
        return result;
    }

    async login(SSOToken: string): Promise<User> {
        try {
            const { userCode } = await Utils.SSO.verify(SSOToken);
            const user = await this.findOneOrErrorByCode(userCode);
            return user;
        } catch (error) {
            if (error.message == 'Invalid SSO Token') {
                throw new BaseError(error.message, httpStatus.UNAUTHORIZED);
            } else {
                throw new BaseError(
                    error.message,
                    httpStatus.INTERNAL_SERVER_ERROR,
                );
            }
        }
    }
}
