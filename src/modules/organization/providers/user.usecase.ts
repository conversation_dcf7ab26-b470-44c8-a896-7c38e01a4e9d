import { Inject, Service } from 'typedi';
import { UserService } from './user.service';
import { PermissionRepository } from '@/modules/permission/providers/permission.repository';
import { UserInfo } from '../types/user.type';

@Service()
export class UserUseCase {
    @Inject()
    private userService: UserService;
    @Inject()
    protected permissionRepo: PermissionRepository;
    /**
     * 產生 filterMappingMetadata 需要的人員資訊
     */
    async getFilterUserInfoById(id: number): Promise<UserInfo> {
        const [userPermission, user] = await Promise.all([
            this.permissionRepo.findByUserId(id),
            this.userService.findOneOrError(id),
        ]);
        const isSuperUser =
            userPermission?.permission?.['isSuperUser'] ?? false;

        user.userPositions = user.userPositions.filter((i) => !i.disabled);
        const userData = {
            user,
            isSuperUser,
            permission: userPermission?.permission,
            departmentIds: user.userPositions.map((i) => i.departmentId),
            departmentCodes: user.userPositions.map((i) => i.department.code),
            companyIds: user.userPositions.map((i) => i.department.companyId),
            companyCodes: user.userPositions.map(
                (i) => i.department.company.code,
            ),
            regionIds: user.userPositions.map(
                (i) => i.department.company.regionId,
            ),
            deleted: user.deleted,
            disabled: user.disabled,
        };
        return userData;
    }
}
