import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { Department } from '@/common/models/clinico/core/department.model';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { Service } from 'typedi';
import { FindOptionsWhere } from 'typeorm';
import { FindOneParams, SearchParams } from '../types/department.type';
import { Like, In, Not } from 'typeorm';
import _ from 'lodash';
import { Company } from '@/common/models/clinico/core/company.model';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';

@Service()
export class DepartmentService extends CommonService<Department> {
    protected repository = ClinicoDataSource.getRepository(Department);

    async findOneByParamsOrError(params: FindOneParams): Promise<Department> {
        const result = await this.findOneByParams(params);
        if (!result) {
            throw new BaseError('Department not found', httpStatus.NOT_FOUND);
        }
        return result;
    }

    async findOneByParams(params: FindOneParams): Promise<Department | null> {
        const result = await this.search({
            companyId: params.companyId,
            id: params.departmentId,
            code: params.departmentCode,
            disabled: false,
        });
        return result.rows.length ? result.rows[0] : null;
    }

    /**
     * 取得所有「公司級別」的部門
     */
    async findAllCompanyLevelDepartments(
        regionId?: number,
    ): Promise<Department[]> {
        const result = await this.search({
            parentId: 1, // G000 科林集團
            regionId: regionId,
            disabled: false,
        });
        return result.rows;
    }

    /** 取得「指定層級」的部門
     * @param inDepTypes
     */
    async findByApprovalLevels(approvalLevels: string[], regionId?: number) {
        const result = await this.search({
            // notParentId: 1, // 只要留通路
            approvalLevels,
            regionId: regionId,
            disabled: false,
        });
        return result.rows;
    }

    async findByDepTypes(inDepTypes: string[], regionId?: number) {
        const result = await this.search({
            notParentId: 1, // 只要留通路
            inDepTypes,
            regionId: regionId,
            disabled: false,
        });
        return result.rows;
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<Department>> {
        const filters: FindOptionsWhere<Department> = {};
        const companyFilters: FindOptionsWhere<Company> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.companyId) {
            filters.companyId = params.companyId;
        }
        if (params.companyName) {
            filters.company = {
                name: Like(`%${params.companyName}%`),
            };
        }
        if (params.code) {
            filters.code = params.code;
        }
        if (params.name) {
            filters.name = Like(`%${params.name}%`);
        }
        if (params.parentId) {
            filters.parentId = params.parentId;
        }
        if (params.notParentId) {
            filters.parentId = Not(params.notParentId);
        }
        if (params.parentCode) {
            filters.parent = {
                code: params.parentCode,
            };
        }
        if (params.supervisorId) {
            filters.supervisorId = params.supervisorId;
        }
        if (params.supervisorCode) {
            filters.supervisor = {
                code: params.supervisorCode,
            };
        }
        if (!_.isUndefined(params.disabled) && !_.isNull(params.disabled)) {
            filters.disabled = params.disabled;
        }
        if (params.regionId) {
            companyFilters.regionId = params.regionId;
        }
        if (params.inDepTypes) {
            filters.depType = In(params.inDepTypes);
        }
        if (params.approvalLevels) {
            filters.approvalLevel = In(params.approvalLevels);
        }

        const [rows, count] = await this.repository.findAndCount({
            relations: {
                company: true,
                parent: true,
                supervisor: true,
            },
            where: { ...filters, company: companyFilters },
            take: params.limit,
            skip: params.offset,
            order: {
                id: 'ASC',
            },
        });

        const result: CommonSearchResult<Department> = {
            rows,
            count,
        };

        return result;
    }
}
