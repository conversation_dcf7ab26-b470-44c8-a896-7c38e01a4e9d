import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { Company } from '@/common/models/clinico/core/company.model';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { Service } from 'typedi';
import { FindOptionsWhere } from 'typeorm';
import { SearchParams } from '../types/company.type';
import { Like } from 'typeorm';
import _ from 'lodash';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';

@Service()
export class CompanyService extends CommonService<Company> {
    protected repository = ClinicoDataSource.getRepository(Company);

    async findOneOrErrorByCode(code: string): Promise<Company> {
        const result = await this.search({ code: code });
        if (result.rows.length != 1) {
            throw new BaseError('Company not found', httpStatus.NOT_FOUND);
        }
        return result.rows[0];
    }

    async search(params: SearchParams): Promise<CommonSearchResult<Company>> {
        const filters: FindOptionsWhere<Company> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.code) {
            filters.code = params.code;
        }
        if (params.name) {
            filters.name = Like(`%${params.name}%`);
        }
        if (params.regionId) {
            filters.regionId = params.regionId;
        }
        if (!_.isUndefined(params.disabled) && !_.isNull(params.disabled)) {
            filters.disabled = params.disabled;
        }

        const data = await this.repository.findAndCount({
            where: filters,
            take: params.limit,
            skip: params.offset,
            order: {
                id: 'ASC',
            },
        });

        const result: CommonSearchResult<Company> = {
            rows: data[0],
            count: data[1],
        };
        return result;
    }
}
