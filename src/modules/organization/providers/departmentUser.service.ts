import { Service } from 'typedi';
import { DepartmentUser, SearchParams } from '../types/departmentUser.type';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';

@Service()
export class DepartmentUserService {
    async findOneOrError(params: {
        departmentId: number;
        userId: number;
    }): Promise<DepartmentUser> {
        const data = await this.search(params);

        const departmentUser = data.length == 1 ? data[0] : null;
        if (!departmentUser) {
            throw new BaseError(
                'departmentUser not found',
                httpStatus.NOT_FOUND,
            );
        }
        return departmentUser;
    }

    async search(params: SearchParams): Promise<DepartmentUser[]> {
        try {
            const queryParams: any[] = [];
            const query = `
            SELECT * FROM core.v_department_users
            WHERE 1=1
            `;

            if (params.departmentId) {
                queryParams.push(` AND department_id = ${params.departmentId}`);
            }
            if (params.departmentCode) {
                queryParams.push(
                    ` AND department_code = ${params.departmentCode}`,
                );
            }
            if (params.userId) {
                queryParams.push(` AND user_id = ${params.userId}`);
            }
            if (params.userCode) {
                queryParams.push(` AND user_code = ${params.userCode}`);
            }

            const rows = await ClinicoDataSource.query(
                query + queryParams.join(''),
            );

            return rows;
        } catch (err) {
            throw new BaseError(err.message, httpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
