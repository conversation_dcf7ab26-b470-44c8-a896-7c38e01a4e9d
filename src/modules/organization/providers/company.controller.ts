import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { Helpers } from '@clinico/clinico-node-framework';
import {
    Get,
    JsonController,
    Param,
    QueryParams,
    UseBefore,
} from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { CompanyService } from './company.service';
import { Company } from '@/common/models/clinico/core/company.model';
import { SearchParams } from '../types/company.controller.type';
import { OpenAPIParams } from '@/common/types/common.openapi.type';
import { OpenAPI } from 'routing-controllers-openapi';

@JsonController('/company')
@Service()
export class CompanyController {
    @Inject()
    private companyService: CompanyService;

    @Get('/')
    @UseBefore(UserAuthKoaInterceptor)
    async findAll() {
        const companies = await this.companyService.findAll();
        return Helpers.Json.success(<Company[]>companies);
    }

    @OpenAPI(OpenAPIParams.searchCompany)
    @Get('/search')
    @UseBefore(UserAuthKoaInterceptor)
    async search(@QueryParams() params: SearchParams) {
        const companies = await this.companyService.search(params);
        return Helpers.Json.success(companies);
    }

    @Get('/:id')
    @UseBefore(UserAuthKoaInterceptor)
    async findOne(@Param('id') id: number) {
        const company = await this.companyService.findOne(id);
        return Helpers.Json.success(company);
    }
}
