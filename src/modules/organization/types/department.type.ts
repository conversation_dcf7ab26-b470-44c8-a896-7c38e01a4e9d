import { CommonSearchParams } from '@/common/types/common.type';

export type SearchParams = CommonSearchParams & {
    companyId?: number;
    companyName?: string;
    code?: string;
    name?: string;
    parentId?: number;
    parentCode?: string;
    supervisorId?: number;
    supervisorCode?: string;
    approvalLevels?: string[];
    disabled?: boolean;
    regionId?: number;
    inDepTypes?: string[];
    notParentId?: number;
};

export type FindOneParams = {
    companyId?: number;
    departmentId?: number;
    departmentCode?: string;
};
