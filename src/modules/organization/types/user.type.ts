import { User } from '@/common/models/clinico/core/user.model';
import { CommonSearchParams } from '@/common/types/common.type';

export type SearchParams = CommonSearchParams & {
    ids?: number[];
    code?: string;
    codes?: string[];
    name?: string;
    account?: string;
    email?: string;
    mobile?: string;
    disabled?: boolean;
};

export type UserPayload = {
    id: number;
    code: string;
    email: string;
    name: string;
};

export type UserInfo = {
    user: User;
    isSuperUser: boolean;
    permission?: JSON;
    departmentIds: number[];
    departmentCodes: string[];
    companyIds: number[];
    companyCodes: string[];
    regionIds: number[];
    deleted: boolean;
    disabled: boolean;
};
