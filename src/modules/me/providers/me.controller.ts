import Ko<PERSON>, { Context } from 'koa';
import { Helpers, Utils } from '@clinico/clinico-node-framework';
import {
    BodyParam,
    Ctx,
    Get,
    JsonController,
    Post,
    UseBefore,
} from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { UserPayload } from '@/modules/organization/types/user.type';
import { UserService } from '@/modules/organization/providers/user.service';
import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { MeLoginResult, MeResult } from '../types/me.controller.type';
import { PermissionRepository } from '@/modules/permission/providers/permission.repository';
import { FormRepository } from '@/modules/form/form/providers/form.repository';

@JsonController('/me')
@Service()
export class MeController {
    @Inject()
    private userService: UserService;
    @Inject()
    private permissionRepo: PermissionRepository;
    @Inject()
    private formRepo: FormRepository;

    @Get('/')
    @UseBefore(UserAuthKoaInterceptor)
    async me(@Ctx() ctx: Koa.Context) {
        const userPayload: UserPayload = ctx.req['user'];
        const user = await this.userService.findOneOrError(userPayload.id);
        const userPermission = await this.permissionRepo.findByUserId(user.id);

        return Helpers.Json.success(<MeResult>{
            ...user,
            permission: userPermission?.permission,
        });
    }

    @Post('/login')
    async login(@BodyParam('SSOToken') SSOToken: string) {
        const user = await this.userService.login(SSOToken);
        const payload: UserPayload = {
            id: user.id,
            code: user.code,
            name: user.name,
            email: user.email,
        };
        const token = await Utils.JWT.sign(payload);
        return Helpers.Json.success(<MeLoginResult>{ ...payload, token });
    }

    @Post('/simulate')
    @UseBefore(UserAuthKoaInterceptor)
    async simulate(@BodyParam('userCode') userCode: string) {
        const user = await this.userService.findOneOrErrorByCode(userCode);
        const payload: UserPayload = {
            id: user.id,
            code: user.code,
            name: user.name,
            email: user.email,
        };
        const token = await Utils.JWT.sign(payload);
        return Helpers.Json.success(<MeLoginResult>{ ...payload, token });
    }

    @Get('/params/forms')
    @UseBefore(UserAuthKoaInterceptor)
    async getFormInstanceForms(@Ctx() ctx: Context) {
        const userPayload: UserPayload = ctx.req['user'];
        if (!userPayload || !userPayload.id) {
            return [];
        }
        const result = await this.formRepo.findByInstanceTaskUserId(
            userPayload.id,
        );
        return Helpers.Json.success(result);
    }
}
