import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { CommonSearchPageParams } from '@/common/types/common.controller.type';
import { UserPayload } from '@/modules/organization/types/user.type';
import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'koa';
import {
    Body,
    Ctx,
    Get,
    JsonController,
    Post,
    QueryParams,
    UseBefore,
} from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { ReminderService } from '@/modules/reminder/providers/reminder.service';
import { CreatereminderParams } from '@/modules/reminder/types/reminder.controller.type';
import { OpenAPI } from 'routing-controllers-openapi';
import { OpenAPIParams } from '@/common/types/common.openapi.type';

@JsonController('/reminders')
@UseBefore(UserAuthKoaInterceptor)
@Service()
export class ReminderController {
    @Inject()
    private reminderService: ReminderService;

    @OpenAPI(OpenAPIParams.findReminders)
    @Get('/')
    async reminders(
        @Ctx() ctx: Context,
        @QueryParams() params: CommonSearchPageParams,
    ): Promise<void> {
        const userPayload: UserPayload = ctx.req['user'];
        const result = await this.reminderService.search({
            ...params,
            createdUserId: userPayload.id,
        });
        return Helpers.Json.success(result);
    }

    @OpenAPI(OpenAPIParams.createReminder)
    @Post('/')
    async remind(
        @Ctx() ctx: Context,
        @Body() params: CreatereminderParams,
    ): Promise<void> {
        const userPayload: UserPayload = ctx.req['user'];
        const result = await this.reminderService.create({
            formInstanceId: params.formInstanceId,
            createdUserId: userPayload.id,
        });
        return Helpers.Json.success(result);
    }
}
