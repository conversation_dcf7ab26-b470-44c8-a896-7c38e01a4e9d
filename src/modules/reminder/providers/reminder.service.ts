import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { Inject, Service } from 'typedi';
import { FindOptionsWhere } from 'typeorm';
import { Reminder } from '@/common/models/clinico/bpm/reminder.model';
import {
    CreateReminderParams,
    SearchReminderParams,
    UpdateReminderParams,
} from '../types/reminder.type';
import { EnumTaskStatus } from '@/modules/form/formInstanceTask/types/formInstanceTask.type';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import { SMTPService } from '@/common/services/smtp.service';

@Service()
export class ReminderService extends CommonService<Reminder> {
    protected repository = ClinicoDataSource.getRepository(Reminder);

    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private smtpSvc: SMTPService;

    async search(
        params: SearchReminderParams,
    ): Promise<CommonSearchResult<Reminder>> {
        const filters: FindOptionsWhere<Reminder> = {};
        filters.deleted = false;
        if (params.id) {
            filters.id = params.id;
        }
        if (params.formInstanceTaskId) {
            filters.formInstanceTaskId = params.formInstanceTaskId;
        }
        if (params.createdUserId) {
            filters.createdUserId = params.createdUserId;
        }

        const [rows, count] = await this.repository.findAndCount({
            relations: {
                formInstanceTask: {
                    formInstance: {
                        form: true,
                    },
                    user: true,
                },
                createUser: true,
                updatedUser: true,
            },
            where: filters,
            take: params.limit,
            skip: params.offset,
            order: { id: 'DESC' },
        });

        const result: CommonSearchResult<Reminder> = {
            rows,
            count,
        };
        return result;
    }

    async create(params: CreateReminderParams): Promise<Reminder[]> {
        const reminders = await ClinicoDataSource.transaction(
            async (manager) => {
                const result: Reminder[] = [];
                const instance = await this.formInstanceRepo.findOneOrError(
                    params.formInstanceId,
                );
                const currentTasks = instance.formInstanceTasks.filter(
                    (data) => data.status == EnumTaskStatus.Waiting,
                );
                for (const allCurrentTask of currentTasks) {
                    const reminder = this.repository.create({
                        formInstanceTaskId: allCurrentTask.id,
                        createdUserId: params.createdUserId,
                    });
                    await this.validate(reminder);
                    const res = await manager.save(reminder);
                    result.push(res);
                }
                return result;
            },
        );

        // send reminder notify
        for (const reminder of reminders) {
            if (!reminder.formInstanceTaskId) continue;
            await this.smtpSvc.sendNotify(reminder.formInstanceTaskId);
        }

        return reminders;
    }

    async update(params: UpdateReminderParams): Promise<Reminder> {
        const reminder = await this.findOneOrError(params.id);
        reminder.updatedUserId = params.updatedUserId;
        return await this.repository.save(reminder);
    }

    private validate(reminder: Reminder) {
        //todo
    }
}
