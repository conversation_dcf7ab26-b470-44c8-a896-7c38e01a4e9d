import { AWSHelpers } from '@/common/helpers/AWS.helpers';
import { Utils } from '@clinico/clinico-node-framework';
import { Service } from 'typedi';
import {
    BulkMoveAttachmentParams,
    CreateAttachmentParams,
    MoveAttachmentParams,
    MoveAttachmentResult,
    UploadAttachmentParams,
} from '../types/attachment.type';
import { v4 as uuidv4 } from 'uuid';
import { Attachment } from '@/common/models/clinico/bpm/attachment.model';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { In } from 'typeorm';

@Service()
export class AttachmentService {
    protected repository = ClinicoDataSource.getRepository(Attachment);

    async getAttachmentByS3Key(s3Keys: string[]) {
        const data = await this.repository.findBy({
            s3Key: In(s3Keys),
            deleted: false,
        });
        return data;
    }

    async getAttachmentsByS3Keys(s3Keys: string[]) {
        const data = await this.repository.findBy({
            s3Key: In(s3Keys),
        });
        return data;
    }

    async validateAttachment(S3Keys?: string[]): Promise<string[]> {
        const attachmentS3Keys: string[] = [];
        for (const s3Key of S3Keys ?? []) {
            if (
                !(await Utils.AWS.S3.isObjectExist({
                    bucket: AWSHelpers.bucket(),
                    key: s3Key,
                }))
            ) {
                throw new BaseError(
                    `附加檔案不存在: ${s3Key} `,
                    httpStatus.NOT_FOUND,
                );
            }
            attachmentS3Keys.push(s3Key);
        }
        return attachmentS3Keys;
    }

    /**
     * upload file to AWS S3
     * @param params.fileName
     * @param params.body File stream or buffer
     * @param params.encoding
     * @param params.contentType
     * @returns S3 key
     */
    public async upload(params: UploadAttachmentParams): Promise<string> {
        const result = await Utils.AWS.S3.upload({
            bucket: AWSHelpers.bucket(),
            acl: Utils.AWS.S3.EnumACL.Private,
            key: this.uniqueS3key(params.fileName),
            contentEncoding: params.encoding,
            contentType: params.contentType,
            body: params.body,
        });
        return result.Key;
    }

    /** Attachment move from `draft/..` to `FORM_ID/...` */
    public async bulkMoveDraftToCurrentFolder(
        params: BulkMoveAttachmentParams,
    ): Promise<MoveAttachmentResult[]> {
        const result: MoveAttachmentResult[] = [];
        const sourceKeys = params.sourceKeys.filter((item) =>
            item.includes('draft/'),
        );
        const setSourceKeys = new Set(sourceKeys);
        for (const key of Array.from(setSourceKeys.values())) {
            const newKey = await this.moveDraftToCurrentFolder({
                processInstanceId: params.processInstanceId,
                sourceKey: key,
            });
            result.push({
                key,
                newKey,
            });
        }
        return result;
    }

    /** Attachment move from `draft/..` to `FORM_ID/...` */
    public async moveDraftToCurrentFolder(
        params: MoveAttachmentParams,
    ): Promise<string> {
        const newKey = params.sourceKey.replace(
            'draft',
            params.processInstanceId,
        );
        await Utils.AWS.S3.move({
            targetBucket: AWSHelpers.bucket(),
            sourceBucket: AWSHelpers.bucket(),
            sourceKey: params.sourceKey,
            targetKey: newKey,
        });
        return newKey;
    }

    private uniqueS3key(fileName: string): string {
        // test.jpg => jpg
        const ext = fileName.split('.').pop();
        // test.jpg => test
        const fileNameWithOutExt = ext
            ? fileName.replace(`.${ext}`, '')
            : fileName;
        // 預設會放在 draft 資料夾中
        const s3Key = `draft/${fileNameWithOutExt}_${uuidv4()}.${ext}`;

        return s3Key;
    }

    /**
     * @deprecated
     * @param params
     */
    async create(params: CreateAttachmentParams): Promise<void> {
        await ClinicoDataSource.transaction(async (manager) => {
            for (const attachmentFile of params.attachmentFiles) {
                const attachment = this.repository.create({
                    formInstanceId: params.formInstanceId,
                    formInstanceTaskId: params.formInstanceTaskId,
                    s3Key: attachmentFile.s3Key,
                    fileName: attachmentFile.fileName,
                });
                await this.validate(attachment);
                await manager.save(attachment);
            }
        });
    }

    async updateOrCreate(params: CreateAttachmentParams): Promise<void> {
        await ClinicoDataSource.transaction(async (manager) => {
            for (const attachmentFile of params.attachmentFiles) {
                const files = await this.repository.findBy({
                    s3Key: attachmentFile.s3Key,
                });
                const exist = files.length > 0;
                if (!exist) {
                    const attachment = this.repository.create({
                        formInstanceId: params.formInstanceId,
                        formInstanceTaskId: params.formInstanceTaskId,
                        formInstanceMessageId: params.formInstanceMessageId,
                        uploadUserId: params.uploadUserId,
                        source: params.source,
                        groupBy: attachmentFile.groupBy,
                        s3Key: attachmentFile.s3Key,
                        fileName: attachmentFile.fileName,
                    });
                    await this.validate(attachment);
                    await manager.save(attachment);
                } else {
                    const upsertFiles = files
                        .filter((i) => i.s3Key != attachmentFile.s3Key)
                        .map((i) => {
                            i.s3Key = attachmentFile.s3Key;
                            i.fileName = attachmentFile.fileName;
                            return i;
                        });
                    await manager.save(upsertFiles);
                }
            }
        });
    }

    async validate(attachment: Attachment) {
        //todo
    }
}
