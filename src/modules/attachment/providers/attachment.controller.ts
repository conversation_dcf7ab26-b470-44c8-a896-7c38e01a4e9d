import { Helpers, Utils } from '@clinico/clinico-node-framework';
import {
    BodyParam,
    Get,
    JsonController,
    Post,
    QueryParam,
    UploadedFiles,
    UseBefore,
} from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { AttachmentService } from './attachment.service';
import { AttachmentResponse, UploadFile } from '../types/attachment.type';
import { OpenAPI } from 'routing-controllers-openapi';
import { AWSHelpers } from '@/common/helpers/AWS.helpers';
import { OpenAPIParams } from '@/common/types/common.openapi.type';
import _ from 'lodash';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { Attachment } from '@/common/models/clinico/bpm/attachment.model';

@JsonController('/attachment')
@UseBefore(UserAuthKoaInterceptor)
@Service()
export class AttachmentController {
    @Inject()
    private attachmentService: AttachmentService;

    @OpenAPI(OpenAPIParams.searchAttachment)
    @Get('/')
    async attachment(@QueryParam('s3Key') s3Key: string) {
        const url = Utils.AWS.S3.getSignedUrl({
            bucket: AWSHelpers.bucket(),
            key: s3Key,
        });
        const S3Object = await Utils.AWS.S3.getHeadObject({
            bucket: AWSHelpers.bucket(),
            key: s3Key,
        });
        return Helpers.Json.success(<AttachmentResponse>{
            url: url,
            size: S3Object?.ContentLength ? S3Object.ContentLength : 0,
        });
    }

    @Post('/')
    async attachments(@BodyParam('s3Keys') s3Keys: string[]) {
        if (!_.isArray(s3Keys)) {
            throw new BaseError('s3Keys format error', httpStatus.BAD_REQUEST);
        }
        const attachments =
            await this.attachmentService.getAttachmentsByS3Keys(s3Keys);
        const attachmentsMap = attachments.reduce((pre, cur) => {
            const s3Key = cur.s3Key;
            pre.set(s3Key, cur);
            return pre;
        }, new Map<string, Attachment>());

        const result: AttachmentResponse[] = [];
        for (const s3Key of s3Keys) {
            const attachment = attachmentsMap.get(s3Key);
            const url = Utils.AWS.S3.getSignedUrl({
                bucket: AWSHelpers.bucket(),
                key: s3Key,
            });
            const S3Object = await Utils.AWS.S3.getHeadObject({
                bucket: AWSHelpers.bucket(),
                key: s3Key,
            });
            if (!attachment) continue;
            result.push({
                ...attachment,
                s3Key,
                url: url,
                size: S3Object?.ContentLength ? S3Object.ContentLength : 0,
            });
        }

        // sort by created date
        result.sort((a, b) => {
            if (b.createdAt.getTime() == a.createdAt.getTime()) {
                return b.id - a.id;
            }
            return b.createdAt.getTime() - a.createdAt.getTime();
        });

        return Helpers.Json.success(result);
    }

    @OpenAPI(OpenAPIParams.uploadAttachment)
    @Post('/upload')
    async upload(@UploadedFiles('files') files: UploadFile[]) {
        const s3Keys: string[] = [];
        for (const file of files) {
            const s3Key = await this.attachmentService.upload({
                fileName: file.originalname,
                encoding: file.encoding,
                contentType: file.mimetype,
                body: file.buffer,
            });
            s3Keys.push(s3Key);
        }
        return Helpers.Json.success({
            s3Keys,
        });
    }
}
