import { Attachment } from '@/common/models/clinico/bpm/attachment.model';
import { EnumValueUploadFileSource } from '@/modules/form/formInstance/types/formValues.type';
import { ReadStream } from 'fs-capacitor';

export type UploadFile = {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    buffer: Buffer;
    sizs: number;
};

export class AttachmentResponse extends Attachment {
    url: string;
    size: number;
    s3Key: string;
    // [key: string]: any;
}

export type UploadAttachmentParams = {
    fileName: string;
    body: Buffer | ReadStream;
    contentType: string;
    encoding?: string;
};

export type MoveAttachmentParams = {
    sourceKey: string;
    processInstanceId: string;
};

export type BulkMoveAttachmentParams = {
    sourceKeys: string[];
    processInstanceId: string;
};

export type MoveAttachmentResult = {
    key: string;
    newKey: string;
};

export type AttachmentFile = {
    s3Key: string;
    fileName?: string;
    groupBy?: string;
};

export type CreateAttachmentParams = {
    formInstanceId: number;
    formInstanceTaskId?: number;
    formInstanceMessageId?: number;
    source: EnumValueUploadFileSource;
    uploadUserId: number;
    attachmentFiles: AttachmentFile[];
};
