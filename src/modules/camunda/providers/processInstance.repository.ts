import { Service } from 'typedi';
import { camundaAxios } from '@/common/helpers/axios.helper';
import {
    DeletedResult,
    DeleteParams,
    ProcessActivityInstance,
    ProcessInstance,
    SearchParams,
    StartParams,
    UpdateParams,
    UpdateVariableParams,
    UpdateVariablesParams,
    Variable,
    Variables,
} from '../types/processInstance.type';
import { DefinitionXML } from '../types/process.type';

@Service()
export class ProcessInstanceRepository {
    async findOne(processId: string): Promise<ProcessInstance> {
        const endpoint = `/process-instance/${processId}`;
        const { data } = await camundaAxios.get<ProcessInstance>(endpoint);

        return data;
    }

    async search(params: SearchParams): Promise<ProcessInstance[]> {
        const endpoint = `/process-instance`;
        const { data } = await camundaAxios.post<ProcessInstance[]>(
            endpoint,
            params,
        );
        return data;
    }

    async start(params: StartParams): Promise<ProcessInstance> {
        const { key, businessKey, variables } = params;
        const endpoint = `/process-definition/key/${key}/start`;

        const { data } = await camundaAxios.post<ProcessInstance>(endpoint, {
            businessKey: businessKey,
            variables: variables,
        });

        return data;
    }

    async update(params: UpdateParams): Promise<void> {
        const { processId } = params;
        const endpoint = `/process-instance/${processId}/modification`;

        await camundaAxios.post(endpoint, {
            skipCustomListeners: params.skipCustomListeners,
            skipIoMappings: params.skipIoMappings,
            instructions: params.instructions,
            annotation: params.annotation,
        });
    }

    async updateVariable(params: UpdateVariableParams): Promise<void> {
        const { processId, varName, value, type, valueInfo } = params;
        const endpoint = `/process-instance/${processId}/variables/${varName}`;

        await camundaAxios.put(endpoint, {
            value,
            type,
            valueInfo,
        });
    }

    async updateVariables(params: UpdateVariablesParams): Promise<void> {
        const { processId, modifications, deletions } = params;
        const endpoint = `/process-instance/${processId}/variables`;

        await camundaAxios.post(endpoint, {
            modifications: modifications,
            deletions: deletions,
        });
    }

    async delete(params: DeleteParams): Promise<DeletedResult> {
        const { processInstanceIds, deleteReason } = params;
        const endpoint = `/process-instance/delete`;

        const { data } = await camundaAxios.post<DeletedResult>(endpoint, {
            processInstanceIds: processInstanceIds,
            deleteReason: deleteReason,
        });

        return data;
    }

    async findOneVariable(params: {
        processId: string;
        variable: string;
    }): Promise<Variable | null> {
        const variables = await this.findAllVariables(params.processId);
        return variables ? variables[params.variable] : null;
    }

    async findAllVariables(processId: string): Promise<Variables> {
        const endpoint = `/process-instance/${processId}/variables`;
        const { data } = await camundaAxios.get<Variables>(endpoint);

        return data;
    }

    async definitionXML(processId: string): Promise<DefinitionXML> {
        const endpoint = `/process-definition/${processId}/xml`;
        const { data } = await camundaAxios.get<DefinitionXML>(endpoint);

        return data;
    }

    async activity(processId: string): Promise<ProcessActivityInstance> {
        const endpoint = `/process-instance/${processId}/activity-instances`;
        const { data } = await camundaAxios.get(endpoint);
        const activityInstance: ProcessActivityInstance = {
            ...data,
        };
        return activityInstance;
    }
}
