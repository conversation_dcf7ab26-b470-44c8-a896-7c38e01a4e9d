import { Service } from 'typedi';
import { CountersignerVariable } from '@/common/helpers/variables.helper';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { DepartmentUser } from '@/modules/organization/types/departmentUser.type';
import { CountersignType } from '@/modules/form/formInstanceTask/types/taskAddition.type';

@Service()
export class ProcessInstanceTaskService {
    checkCountersignType(property?: any): CountersignType {
        if (!property) {
            throw new BaseError(
                '無法辨識加簽類型(property id empty)',
                httpStatus.INTERNAL_SERVER_ERROR,
            );
        }
        const json = property;

        if (json.allowAddAssignee) {
            return CountersignType.Part;
        }
        if (json.allowAddCountersign) {
            return CountersignType.All;
        }

        throw new BaseError(
            '無法辨識加簽類型',
            httpStatus.INTERNAL_SERVER_ERROR,
        );
    }

    genUpdateVariables(
        departmentUser: DepartmentUser,
        variables: Variables,
        countersignType: CountersignType,
    ): {
        keyValue: Variables;
        data: { varName: string; value: any };
    } {
        switch (countersignType) {
            case CountersignType.All: {
                const formDataCountersigners =
                    variables['form_data_countersigners'];
                const addCountersigner: CountersignerVariable = {
                    company_id: departmentUser.company_id,
                    department_id: departmentUser.department_id,
                    id: departmentUser.user_id,
                };
                const updateCountersigners = <CountersignerVariable[]>(
                    formDataCountersigners.value
                );
                updateCountersigners.push(addCountersigner);

                return {
                    keyValue: {
                        form_data_countersigners: {
                            value: updateCountersigners,
                        },
                    },
                    data: {
                        varName: 'user',
                        value: addCountersigner,
                    },
                };
            }
            case CountersignType.Part: {
                let initialDataUserCodes = variables['initial_data_user_codes'];
                if (!initialDataUserCodes) {
                    initialDataUserCodes = {
                        value: [],
                    };
                }
                const addUserCode = departmentUser.user_code;
                const updateUserCodes = <string[]>initialDataUserCodes.value;
                updateUserCodes.push(addUserCode);

                return {
                    keyValue: {
                        specify_user: {
                            value: addUserCode,
                        },
                    },
                    data: {
                        varName: 'initial_data_user_codes',
                        value: updateUserCodes,
                    },
                };
            }
            default:
                throw new BaseError(
                    '無法辨識加簽類型',
                    httpStatus.INTERNAL_SERVER_ERROR,
                );
        }
    }
}
