import { Inject, Service } from 'typedi';
import { ProcessInstanceTaskRepository } from './processInstanceTask.repository';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { Task } from '../types/task.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { EnumTaskStatus } from '@/modules/form/formInstanceTask/types/formInstanceTask.type';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import { ProcessInstanceUseCase } from './processInstance.usecase';
import { FormValuesUseCase } from '@/modules/form/formInstance/providers/formValues.usecase';
import { TaskPropertyUseCase } from '@/modules/camunda/providers/taskProperty/taskProperty.usecase';
import { Assignee } from '@/modules/assignee/types/assignee.type';
import { DepartmentUser } from '@/modules/organization/types/departmentUser.type';
import { ProcessInstanceRepository } from './processInstance.repository';
import { ProcessInstanceTaskService } from './processInstanceTask.service';
import { TaskVariableUseCase } from './taskVariable/taskVariable.usecase';
import { TaskAssigneeUseCase } from '@/modules/camunda/providers/taskAssignee/taskAssignee.usecase';
import { FormTaskConfigUseCase } from './taskProperty/form.taskConfig.ucase';
import { FormConfTaskAssigneesUseCase } from '../../form/formConf/providers/formConfTaskAssignees.usecase';

@Service()
export class ProcessInstanceTaskUseCase {
    @Inject()
    private repo: ProcessInstanceTaskRepository;
    @Inject()
    private svc: ProcessInstanceTaskService;
    @Inject()
    private taskVariableUCase: TaskVariableUseCase;
    @Inject()
    private processInstanceUCase: ProcessInstanceUseCase;
    @Inject()
    private processInstanceRepo: ProcessInstanceRepository;
    @Inject()
    private formValueUCase: FormValuesUseCase;
    @Inject()
    private taskAssigneeUCase: TaskAssigneeUseCase;
    @Inject()
    private taskPropertyUCase: TaskPropertyUseCase;
    @Inject()
    private formTaskConfigUCase: FormTaskConfigUseCase;
    @Inject()
    private formConfTaskAssigneesUCase: FormConfTaskAssigneesUseCase;

    async runAddAssignee(
        departmentUser: DepartmentUser,
        formInstanceTask: FormInstanceTask,
        formInstance: FormInstance,
    ) {
        const countersignType = this.svc.checkCountersignType(
            formInstanceTask.property,
        );
        const variables = await this.repo.findAllVariables(
            formInstanceTask.processInstanceTaskId,
        );

        // 依countersignType產生更新camunda instance的參數
        const updateVariables = this.svc.genUpdateVariables(
            { ...departmentUser },
            variables,
            countersignType,
        );

        await this.processInstanceRepo.update({
            processId: formInstanceTask.formInstance.processInstanceId!,
            instructions: [
                {
                    type: 'startBeforeActivity',
                    activityId: formInstanceTask.processTaskId,
                    variables: updateVariables.keyValue,
                },
            ],
            annotation: '加簽',
        });

        await this.processInstanceUCase.countingAddAssignee(
            formInstanceTask.formInstance.processInstanceId!,
        );

        // 取得加簽的tasks
        const camundaTasks = await this.getTasksByProcessInstanceId(
            formInstance.processInstanceId,
        );
        const differenceTasks = camundaTasks.filter(
            (task) =>
                !formInstance.formInstanceTasks
                    .map((data) => data.processInstanceTaskId)
                    .includes(task.id),
        );
        // 新增的加簽的tasks
        const differenceTask = differenceTasks[0];
        // camunda的task寫入user，用於產生formInstanceTask時的assignee
        await this.repo.updateVariable({
            id: differenceTask.id,
            varName: updateVariables.data.varName,
            value: updateVariables.data.value,
        });

        return {
            differenceTask,
        };
    }

    async getTasksByProcessInstanceId(processInstanceId?: string) {
        const camundaTasks = await this.repo.search({
            processInstanceId,
        });
        return camundaTasks;
    }

    async getTaskById(camundaTaskId: string) {
        const camundaTask = await this.repo.findOne(camundaTaskId);
        if (!camundaTask) {
            throw new BaseError('Camunda 任務不存在', httpStatus.NOT_FOUND);
        }
        return camundaTask;
    }

    async getTaskInfo(camundaTask: Task, formInstance: FormInstance) {
        const getFormTaskAssigneesFn =
            await this.formConfTaskAssigneesUCase.getGetFormTaskAssigneesFn(
                formInstance.formId,
                formInstance.formVersion,
            );

        /** get camunda variables */
        const variables = await this.repo.findAllVariables(camundaTask.id);
        /** task property */
        const property = await this.taskPropertyUCase.getTaskProperty(
            getFormTaskAssigneesFn,
            camundaTask,
            formInstance,
        );
        /** activity */
        const activity = await this.processInstanceUCase.getActivityByProcessId(
            camundaTask.processInstanceId,
        );
        const activityType = activity.childActivityInstances[0].activityType;

        /** assignee */
        const assignee = await this.taskAssigneeUCase.getTaskAssignee(
            getFormTaskAssigneesFn,
            camundaTask.taskDefinitionKey,
            formInstance,
            variables,
            property,
        );
        if (assignee) {
            await this.formTaskConfigUCase.genPropertiesByAssignee(
                camundaTask.taskDefinitionKey,
                formInstance,
                property,
                assignee,
            );
        }
        return {
            variables,
            property,
            activity,
            assignee,
            activityType,
        };
    }

    async runReturnProcess(
        formInstanceTask: FormInstanceTask,
        returnTaskActivityId: string,
    ) {
        // 更新camunda instance
        const processInstanceId =
            formInstanceTask.formInstance.processInstanceId!;
        await this.processInstanceRepo.update({
            processId: processInstanceId,
            instructions: [
                {
                    type: 'startBeforeActivity',
                    activityId: returnTaskActivityId,
                },
                {
                    type: 'cancel',
                    activityId: formInstanceTask.processTaskId,
                },
            ],
            annotation: '退回',
        });

        /** current camunda tasks */
        const currentCamundaTasks =
            await this.getTasksByProcessInstanceId(processInstanceId);
        return {
            currentCamundaTasks,
        };
    }

    async runCompleteProcess(
        status: EnumTaskStatus,
        formInstanceTask: FormInstanceTask,
        formInstance: FormInstance,
        camundaTask: Task,
        formData: JSON,
        attachmentS3Keys: string[] = [],
    ) {
        const isMultiInstance = await this.checkMultiInstance(
            formInstanceTask.processInstanceTaskId,
        );
        const approved = status == EnumTaskStatus.Approved;
        const rejected = status == EnumTaskStatus.Rejected;
        // 紀錄會簽結果
        if (isMultiInstance) {
            await this.processInstanceUCase.countingStatus(
                camundaTask.processInstanceId,
                approved,
            );
        }
        // 完成此 task 並由 camunda 決定下一關
        const tasks = await this.camundaTaskComplete(
            camundaTask,
            formInstance,
            formData,
            approved,
        );
        // 會簽被駁回，將會簽結果重置
        if (isMultiInstance && rejected) {
            await this.processInstanceUCase.resetCountingStatus(
                camundaTask.processInstanceId,
            );
        }
        // update append data to instance values
        if (approved) {
            await this.formValueUCase.instanceTaskUpdateFormData(
                camundaTask.processInstanceId,
                formInstance,
                formInstanceTask,
                formData,
                attachmentS3Keys,
                formInstanceTask.updatedUserId,
            );
        }
        return tasks;
    }

    async camundaTaskComplete(
        camundaTask: Task,
        formInstance: FormInstance,
        formData: JSON,
        approved: boolean,
    ) {
        const variables = await this.taskVariableUCase.getRunCompleteVariables(
            formInstance,
            formData,
            approved,
        );
        await this.repo.complete({
            id: camundaTask.id,
            variables: variables,
        });
        const tasks = await this.getTasksByProcessInstanceId(
            formInstance.processInstanceId,
        );
        return tasks;
    }

    async checkMultiInstance(processInstanceTaskId: string): Promise<boolean> {
        const loopCounter = await this.repo.findOneVariable({
            id: processInstanceTaskId,
            variable: 'loopCounter',
        });
        return loopCounter ? true : false;
    }

    async setCamundaTaskAssignee(
        camundaTask: Task,
        assignee?: Assignee | null,
    ) {
        if (assignee?.userCode) {
            await this.repo.setAssignee({
                id: camundaTask.id,
                assignee: `${assignee.userCode}`,
            });
        }
    }
}
