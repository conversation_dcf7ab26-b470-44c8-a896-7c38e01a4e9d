import { Inject, Service } from 'typedi';
import _ from 'lodash';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import * as VariablesHelper from '@/common/helpers/variables.helper';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { EnumPrefixVariableName } from '@/modules/form/formInstance/types/formInstance.type';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { ValuesAppendFormData } from '@/modules/form/formInstance/types/formValues.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { CustomizeUseCase } from '@/customize/customize.usecase';
import { GetFormTaskAssigneesFn } from '@/modules/form/formConf/types/formConfTaskAssignees.types';
import { FormConfTaskAssigneesUseCase } from '../../../form/formConf/providers/formConfTaskAssignees.usecase';
import { TaskAssigneeService } from '@/modules/camunda/providers/taskAssignee/taskAssignee.service';
import { FormTaskAssignees } from '@/modules/form/form/types/form.taskSetting.type';
import { TaskAssigneesFilterDataDTO } from '@/modules/form/formConf/types/taskAssigneesFilterData.dto';

@Service()
export class TaskVariableUseCase {
    @Inject()
    private formRepo: FormRepository;
    @Inject()
    private customizeUCase: CustomizeUseCase;
    @Inject()
    private formConfTaskAssigneesUCase: FormConfTaskAssigneesUseCase;
    @Inject()
    private taskAssigneeSvc: TaskAssigneeService;

    async getRunCompleteVariables(
        formInstance: FormInstance,
        formData: JSON,
        approved: boolean,
        variables: Variables = {},
    ) {
        const form = await this.formRepo.findOneOrError(formInstance.formId);
        const getFormTaskAssigneesFn =
            await this.formConfTaskAssigneesUCase.getGetFormTaskAssigneesFn(
                formInstance.formId,
                formInstance.formVersion,
            );
        variables = VariablesHelper.merge({
            variables: { approve: { value: approved } },
            values: formData,
            prefix: '',
        });
        variables = await this.updateVariables(
            getFormTaskAssigneesFn,
            formInstance,
            form,
            formData,
            variables,
        );
        variables = await this.customizeUCase.getRunCompleteGetewayVariables(
            formInstance,
            formData,
            approved,
            variables,
        );
        return variables;
    }

    getStartProcessVariables(
        getFormTaskAssigneesFn: GetFormTaskAssigneesFn,
        form: Form,
        formInstance: FormInstance,
        formData: JSON,
    ) {
        const filterData = new TaskAssigneesFilterDataDTO(
            formData,
            formInstance,
        );
        const formTaskAssignees = getFormTaskAssigneesFn(filterData);
        let variables = VariablesHelper.merge({
            variables: {
                // ...params.variables,
                form_instance_id: { value: `${formInstance.id}` },
            },
            values: formData,
            prefix: EnumPrefixVariableName.PrefixFormData,
        });
        variables = this.genVariablesByFormTaskAssignee(
            formTaskAssignees,
            formData,
            variables,
        );
        variables = this.genVariablesByFormValueTaskAssignee(
            formTaskAssignees,
            formData,
            variables,
        );
        return variables;
    }

    /**
     * 外部表單異動同步更新 camunda variables
     * - SpecifyTaskCategoryUsers
     * @param id formInstance Id
     * @param values 外部表單
     * @param variables camunda 的 variables
     * @returns
     */
    async updateVariables(
        getFormTaskAssigneesFn: GetFormTaskAssigneesFn,
        formInstance: FormInstance,
        form: Form,
        formData: JSON,
        variables: Variables,
    ) {
        const appendFormData: ValuesAppendFormData =
            formData['appendFormData'] ?? {};
        // form_data_...
        variables = VariablesHelper.merge({
            variables,
            values: appendFormData as JSON,
            prefix: EnumPrefixVariableName.PrefixFormData,
        });
        // TODO: 再確認是否要改成 _.merge
        const instanceValues = _.assign(
            {},
            formInstance.values,
            appendFormData,
        );
        const filterData = new TaskAssigneesFilterDataDTO(
            instanceValues,
            formInstance,
        );
        const formTaskAssignees = getFormTaskAssigneesFn(filterData);
        // FormTaskAssignee
        variables = this.genVariablesByFormTaskAssignee(
            formTaskAssignees,
            instanceValues as JSON,
            variables,
        );
        // FormValueTaskAssignee
        if (!instanceValues || _.isString(instanceValues)) {
            return variables;
        }
        variables = this.genVariablesByFormValueTaskAssignee(
            formTaskAssignees,
            instanceValues as JSON,
            variables,
        );
        return variables;
    }

    /**
     * form form_settings.taskSettings.assignees 產生 multi user tasks
     * formSpecifyUsers.lenght == 0 不產生
     * @param form
     * @param formData
     * @param variables
     * @returns
     */
    private genVariablesByFormTaskAssignee(
        formTaskAssignees: FormTaskAssignees,
        formData: JSON,
        variables: Variables,
    ) {
        if (!formData) return variables;
        const formSpecifyUserMap = this.taskAssigneeSvc.genFormSpecifyUserMap(
            formTaskAssignees,
            formData,
        );
        if (!formSpecifyUserMap) return variables;
        for (const [
            activityId,
            formSpecifyUsers,
        ] of formSpecifyUserMap.entries()) {
            // 如果沒有 formSpecifyUsers 還是產生一個空陣列
            // 這樣可以避免在後續處理時出現多關卡取得不到 activityId 多人簽核
            // if (formSpecifyUsers.length == 0) continue;
            const name = `${activityId}_users`;
            if (_.has(variables, name) && _.has(variables[name], 'value')) {
                const varValue = variables[name].value as any[];
                if (_.isArray(varValue)) {
                    varValue.push(...formSpecifyUsers);
                    variables[name].value = varValue;
                    continue;
                }
            }
            variables[name] = {
                value: formSpecifyUsers,
            };
        }
        return variables;
    }

    /**
     * 由 formInstance.value or appendFormData 產生 multi user tasks
     *
     * 接續 form.form_settings 從 formInstance.value 產生的 task assignee
     *
     * genVariablesByFormTaskAssignee 產生的 task assignee
     * 已經有的不能被覆蓋，但是空的會被覆蓋
     * @param form
     * @param formData
     * @param variables
     * @returns
     */
    private genVariablesByFormValueTaskAssignee(
        formTaskAssignees: FormTaskAssignees,
        formData: JSON,
        variables: Variables,
    ) {
        if (!formData) return variables;
        const formValueSpecifyUserMap =
            this.taskAssigneeSvc.genFormValueSpecifyUsersMap(
                formTaskAssignees,
                formData,
            );
        for (const [activityId, formSpecifyUsers] of [
            ...formValueSpecifyUserMap.entries(),
        ]) {
            const name = `${activityId}_users`;
            if (_.has(variables, name) && _.has(variables[name], 'value')) {
                const varValue = variables[name].value as any[];
                if (_.isArray(varValue)) {
                    varValue.push(...formSpecifyUsers);
                    variables[name].value = varValue;
                    continue;
                }
            }
            variables[name] = {
                value: formSpecifyUsers,
            };
        }
        return variables;
    }
}
