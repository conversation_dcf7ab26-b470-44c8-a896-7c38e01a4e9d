import { Inject, Service } from 'typedi';
import { ProcessInstanceRepository } from './processInstance.repository';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { TaskVariableUseCase } from './taskVariable/taskVariable.usecase';
import { ProcessInstanceTaskRepository } from './processInstanceTask.repository';
import { ProcessRepository } from './process.repository';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { FormConfTaskAssigneesUseCase } from '../../form/formConf/providers/formConfTaskAssignees.usecase';

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));
@Service()
export class ProcessInstanceUseCase {
    @Inject()
    private repo: ProcessInstanceRepository;
    @Inject()
    private processInstanceTaskRepo: ProcessInstanceTaskRepository;
    @Inject()
    private taskVariableUCase: TaskVariableUseCase;
    @Inject()
    private processRepo: ProcessRepository;
    @Inject()
    private formConfTaskAssigneesUCase: FormConfTaskAssigneesUseCase;

    async startProcess(formInstance: FormInstance, form: Form, formData: JSON) {
        const getFormTaskAssigneesFn =
            await this.formConfTaskAssigneesUCase.getGetFormTaskAssigneesFn(
                formInstance.formId,
                formInstance.formVersion,
            );
        // camunda process instance.
        const variables = this.taskVariableUCase.getStartProcessVariables(
            getFormTaskAssigneesFn,
            form,
            formInstance,
            formData,
        );

        const processInstance = await this.repo.start({
            key: form.processKey,
            businessKey: `${formInstance.id}`,
            variables: variables,
        });
        formInstance.processInstanceId = processInstance.id;

        // get current camunda task
        const tasks = await this.processInstanceTaskRepo.search({
            processInstanceId: processInstance.id,
        });

        return {
            variables,
            processInstance,
            tasks,
        };
    }

    async deleteProcessByFromInstance(
        formInstance: FormInstance,
        deleteReason: string = '使用者刪除',
    ) {
        return this.repo.delete({
            processInstanceIds: [formInstance.processInstanceId!],
            deleteReason,
        });
    }

    async getCamundaFormByProcessKey(processKey: string) {
        const camundaForm = await this.processRepo.findOne(processKey);
        if (!camundaForm) {
            throw new BaseError('流程定義不存在', httpStatus.NOT_FOUND);
        }
        return camundaForm;
    }

    async findOneDefinitionXML(params: {
        formInstance?: FormInstance;
        form?: Form;
    }): Promise<string | null> {
        let xml: string | null = null;
        const { formInstance, form } = params;
        if (!formInstance && form) {
            xml = await this.findOneDefinitionXMLByForm(form);
        }
        if (formInstance && !formInstance.processId && formInstance.form) {
            xml = await this.findOneDefinitionXMLByForm(
                form ?? formInstance.form,
            );
        }
        if (formInstance && formInstance.processId) {
            xml = await this.findOneDefinitionXMLByFormInstance(formInstance);
        }
        return xml;
    }

    async getActivityByProcessId(processId: string) {
        return this.repo.activity(processId);
    }

    async countingAddAssignee(camundaProcessInstanceId: string) {
        const count = await this.repo.findOneVariable({
            processId: camundaProcessInstanceId,
            variable: 'add_assignee_count',
        });
        if (count) {
            await this.repo.updateVariable({
                processId: camundaProcessInstanceId,
                varName: 'add_assignee_count',
                value: count.value + 1,
            });
        }
    }

    async resetCountingStatus(processInstanceId: string) {
        const approvedCount = await this.repo.findOneVariable({
            processId: processInstanceId,
            variable: 'countersigners_approved_count',
        });
        if (approvedCount) {
            await this.repo.updateVariable({
                processId: processInstanceId,
                varName: 'countersigners_approved_count',
                value: 0,
            });
        }

        const rejectCount = await this.repo.findOneVariable({
            processId: processInstanceId,
            variable: 'countersigners_reject_count',
        });
        if (rejectCount) {
            await this.repo.updateVariable({
                processId: processInstanceId,
                varName: 'countersigners_reject_count',
                value: 0,
            });
        }
    }

    async resetCountingAddAssignee(camundaProcessInstanceId: string) {
        await this.repo.updateVariable({
            processId: camundaProcessInstanceId,
            varName: 'add_assignee_count',
            value: 0,
        });
    }

    async countingStatus(camundaProcessInstanceId: string, approved: boolean) {
        const count = await this.repo.findOneVariable({
            processId: camundaProcessInstanceId,
            variable: approved
                ? 'countersigners_approved_count'
                : 'countersigners_reject_count',
        });
        if (count) {
            await this.repo.updateVariable({
                processId: camundaProcessInstanceId,
                varName: approved
                    ? 'countersigners_approved_count'
                    : 'countersigners_reject_count',
                value: count.value + 1,
            });
        }
    }

    private async findOneDefinitionXMLByForm(
        form: Form,
    ): Promise<string | null> {
        let xml: string | null = null;
        if (form) {
            const data = await this.processRepo.definitionXML(form.processKey);
            xml = data.bpmn20Xml;
        }
        return xml;
    }

    private async findOneDefinitionXMLByFormInstance(
        formInstance: FormInstance,
    ) {
        let xml: string | null = null;
        if (formInstance.processId) {
            const data = await this.repo.definitionXML(formInstance.processId);
            xml = data.bpmn20Xml;
        }
        return xml;
    }
}
