import { Inject, Service } from 'typedi';
import {
    Client,
    ClientConfig,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    logger as camundaLogger,
} from 'camunda-external-task-client-js';
import configs from '@/configs';
import _ from 'lodash';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import moment from 'moment';
import { HookService } from '@/common/services/hook.service';
import { EnumHookEvent } from '@/common/types/hook.type';
import { EnumInstanceStatus } from '../../form/formInstance/types/formInstance.type';
import LoggerUtil from '@/common/utils/logger.util';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import { FormInstanceUseCase } from '@/modules/form/formInstance/providers/formInstance.usecase';
import { FormInstanceTaskUseCase } from '@/modules/form/formInstanceTask/providers/formInstanceTask.usecase';
import { ProcessInstanceTaskRepository } from './processInstanceTask.repository';
import { TaskNotifyUseCase } from './taskNotify/taskNotify.usecase';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import { v4 as uuidv4 } from 'uuid';

@Service()
export class CamundaClientService {
    public emitter: any;

    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private formInstanceUCase: FormInstanceUseCase;
    @Inject()
    private formInstanceTaskUCase: FormInstanceTaskUseCase;
    @Inject()
    private taskNotifyUCase: TaskNotifyUseCase;
    @Inject()
    private processInstanceTaskRepo: ProcessInstanceTaskRepository;
    @Inject()
    private hookService: HookService;

    async start(): Promise<void> {
        const config: ClientConfig = {
            baseUrl: configs.camunda.url,
            use: camundaLogger,
        };

        const client = new Client(config);
        client.subscribe('common:task:end', (args: HandlerArgs) => {
            this.handlerEndTask(args);
        });
        client.subscribe('common:task:start', (args: HandlerArgs) => {
            this.handlerStartTask(args);
        });
        //todo 取消
    }

    /**
     * Camunda 'common:task:end' handler
     * @param args HandlerArgs
     * @returns
     */
    private async handlerEndTask(args: HandlerArgs) {
        const { task, taskService } = args;
        const businessKey = task.businessKey + '';
        LoggerUtil.info(`Received common:task:end [${businessKey}] `);

        const id = parseInt(businessKey);
        if (!id) {
            LoggerUtil.warn(
                `businessKey is undefined or null [${businessKey}] `,
            );
            return;
        }

        try {
            const variables = task.variables.getAll();
            const approve = variables['approve'];
            if (_.isUndefined(approve) || _.isNull(approve)) {
                LoggerUtil.warn(
                    `approve is undefined or null [${businessKey}] `,
                );
                return;
            }

            const instance = await this.formInstanceRepo.findOne(id);
            if (!instance)
                throw new BaseError(
                    `表單實例不存在 (ID: ${id})`,
                    httpStatus.NOT_FOUND,
                );

            const status = approve
                ? EnumInstanceStatus.Completed
                : EnumInstanceStatus.Rejected;

            const data: {
                id: number;
                instance: FormInstance;
                status:
                    | EnumInstanceStatus.Completed
                    | EnumInstanceStatus.Rejected;
                updateUserId: number;
            } = {
                id: id,
                instance,
                status: status,
                updateUserId: instance.ownerUserId,
            };

            await this.updateTaskStatus(data);

            await this.taskNotifyUCase.notifyEndTask(instance);

            await taskService.complete(task);
        } catch (err) {
            LoggerUtil.error(err);
            throw err;
        }
    }

    /**
     * Camunda 'common:task:start' handler
     * @param args HandlerArgs
     * @returns
     */
    private async handlerStartTask(args: HandlerArgs) {
        const { task, taskService } = args;
        const businessKey = task.businessKey + '';
        const instanceId = parseInt(businessKey);
        if (!instanceId) {
            LoggerUtil.warn(
                `businessKey is undefined or null [${businessKey}]`,
            );
            return;
        }

        try {
            await taskService.complete(task);

            const formInstance =
                await this.formInstanceRepo.findOneOrError(instanceId);

            const tasks = await this.processInstanceTaskRepo.search({
                processInstanceId: task.processInstanceId,
            });

            const formInstanceTasks: FormInstanceTask[] = [];
            const groupCode = uuidv4();
            for (const _task of tasks) {
                const formInstanceTask =
                    await this.formInstanceTaskUCase.createInstanceTask({
                        camundaTask: _task,
                        formInstance,
                        name: _task.name,
                        groupCode,
                    });
                formInstanceTasks.push(formInstanceTask);
            }

            const notifyStartTaskFnList = formInstanceTasks.map(
                (formInstanceTask) =>
                    this.taskNotifyUCase.notifyStartTask(formInstanceTask),
            );
            await Promise.all([
                this.formInstanceTaskUCase.runAutoApprove(
                    formInstanceTasks,
                    formInstance.ownerUserId ?? 0,
                ),
                ...notifyStartTaskFnList,
            ]);

            await this.hookService.hook({
                event: EnumHookEvent.SubscribeStart,
                formInstance,
            });
        } catch (err) {
            LoggerUtil.error(err);
            throw err;
        }
    }

    async updateTaskStatus(params: {
        id: number;
        instance: FormInstance;
        status: EnumInstanceStatus.Completed | EnumInstanceStatus.Rejected;
        updateUserId: number;
    }) {
        try {
            const finishDate = moment().toDate();

            const formInstance =
                await this.formInstanceUCase.updateFormInstance({
                    id: params.id,
                    status: params.status,
                    finishDate,
                    updateUserId: params.updateUserId,
                });
            params.instance = formInstance;

            await this.hookService.hook({
                event: EnumHookEvent.SubscribeEnd,
                formInstance,
            });
        } catch (err) {
            LoggerUtil.error(err);
            throw err;
        }
    }
}

const delay = async (ms: number) =>
    new Promise((resolve) => {
        LoggerUtil.info(`delay ${ms} ms...`);
        setTimeout(resolve, ms);
    });
