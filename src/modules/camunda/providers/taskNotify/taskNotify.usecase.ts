import { SMTPService } from '@/common/services/smtp.service';
import { Inject, Service } from 'typedi';
import { FormTaskMailToUseCase } from './form.taskMailTo.usecase';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import { EnumTaskStatusStr } from '@/modules/form/form/types/form.settings.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { WSService } from '@/common/services/ws.service';
import { FormInstanceTaskRepository } from '@/modules/form/formInstanceTask/providers/formInstanceTask.repository';
import _ from 'lodash';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import { FormInstanceTaskService } from '../../../form/formInstanceTask/providers/formInstanceTask.service';
import { FormInstanceMessageService } from '@/modules/form/formInstanceMessage/providers/formInstanceMessage.service';

@Service()
export class TaskNotifyUseCase {
    @Inject()
    private smtpSvc: SMTPService;
    @Inject()
    private wsSvc: WSService;
    @Inject()
    private formInstanceTaskRepo: FormInstanceTaskRepository;
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private formTaskMailToUCase: FormTaskMailToUseCase;
    @Inject()
    private formInstanceTaskSvc: FormInstanceTaskService;
    @Inject()
    private formInstanceMessageSvc: FormInstanceMessageService;

    async notifyStartTask(formInstanceTask: FormInstanceTask) {
        //信件通知
        if (formInstanceTask.userCode) {
            await this.smtpSvc.sendNotify(formInstanceTask.id);
        }
    }

    async notifyEndTask(formInstance: FormInstance) {
        // ws 通知
        await this.notifyEndTaskWs(formInstance);
        //信件通知
        await this.smtpSvc.sendCompleted(formInstance.id);
        await this.smtpSvc.sendCompletedNotifyOthers(formInstance.id);
        await this.formTaskMailToUCase.sendCompletedNotifyToSpecifyUsers(
            formInstance.id,
        );
    }

    private async notifyEndTaskWs(formInstance: FormInstance) {
        formInstance = await this.formInstanceRepo.findOneOrError(
            formInstance.id,
            {
                formInstanceTasks: true,
            },
        );
        const tasks: FormInstanceTask[] = formInstance.formInstanceTasks ?? [];
        if (tasks.length == 0) {
            const { rows } = await this.formInstanceTaskRepo.search({
                formInstanceId: formInstance.id,
            });
            tasks.push(...rows);
        }
        const userIdSet = new Set(
            tasks
                .filter((i) => !i.deleted && !_.isUndefined(i.userId))
                .map((i) => i.userId),
        );
        const userIds = Array.from(userIdSet);
        const message = {
            formInstanceId: formInstance.id,
            status: formInstance.status,
        };
        for (const userId of userIds) {
            if (!userId) continue;
            this.wsSvc.send(userId, 'instanceEnd', message);
        }
    }

    async notifyNewFormInstance(formInstanceTask?: FormInstanceTask | null) {
        //信件通知
        if (formInstanceTask?.userCode) {
            await this.smtpSvc.sendNotify(formInstanceTask.id);
        }
    }

    async notifyTaskProcess(
        current: FormInstanceTask,
        next: FormInstanceTask[],
        taskStatus: EnumTaskStatusStr,
    ) {
        const filterMailTasks =
            this.formInstanceTaskSvc.filterAllowMailNotifyTasks(
                next,
                taskStatus,
            );
        //信件通知
        for (const task of filterMailTasks) {
            if (task.userCode) {
                await this.smtpSvc.sendNotify(task.id);
            }
        }
        if (taskStatus == EnumTaskStatusStr.AdditionalDocs) {
            await this.smtpSvc.sendAdditionalDocsNotify(current.id);
        }
        await this.formTaskMailToUCase.sendNotifyToSpecifyUsers(
            current,
            taskStatus,
        );
    }

    async notifyReconsidered(
        formInstance: FormInstance,
        messageId: number,
        updateUserId: number,
        current?: FormInstanceTask | null,
    ) {
        const message =
            await this.formInstanceMessageSvc.findOneOrError(messageId);
        if (formInstance.ownerUserId != updateUserId) {
            //信件通知
            await this.smtpSvc.sendReconsidered(message);
            if (current) {
                await this.formTaskMailToUCase.sendNotifyToSpecifyUsers(
                    current,
                    EnumTaskStatusStr.Reconsidered,
                );
            }
        }
        await this.smtpSvc.sendMessageToHistoryTaskUsers(message, formInstance);
    }
}
