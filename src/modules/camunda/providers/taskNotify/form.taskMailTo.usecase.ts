import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import { Inject, Service } from 'typedi';
import { UserService } from '@/modules/organization/providers/user.service';
import { SMTPService } from '@/common/services/smtp.service';
import { SMTPHelpers } from '@/common/helpers/smtp.helper';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { FormTaskAssigneeUseCase } from '@/modules/camunda/providers/taskAssignee/form.taskAssignee.ucase';
import {
    FormTaskAssigneeInfo,
    FormTaskAssignees,
    FormTaskMailToItem,
} from '../../../form/form/types/form.taskSetting.type';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { FormService } from '@/modules/form/form/providers/form.service';
import {
    EnumTaskStatusStr,
    EnumMailToStatus,
} from '@/modules/form/form/types/form.settings.type';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { FormConfTaskAssigneesUseCase } from '../../../form/formConf/providers/formConfTaskAssignees.usecase';
import { GetFormTaskAssigneesFn } from '../../../form/formConf/types/formConfTaskAssignees.types';
import { TaskAssigneesFilterDataDTO } from '@/modules/form/formConf/types/taskAssigneesFilterData.dto';

@Service()
export class FormTaskMailToUseCase {
    @Inject()
    private svc: FormService;
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private formRepo: FormRepository;
    @Inject()
    private userService: UserService;
    @Inject()
    private formTaskAssigneeUCase: FormTaskAssigneeUseCase;
    @Inject()
    private smptSvc: SMTPService;
    @Inject()
    private formConfTaskAssigneesUCase: FormConfTaskAssigneesUseCase;

    async sendNotifyToSpecifyUsers(
        currentTask: FormInstanceTask,
        taskStatus: EnumTaskStatusStr,
    ) {
        const { formInstanceId, processTaskId } = currentTask;
        const formInstance =
            await this.formInstanceRepo.findOneOrError(formInstanceId);
        const { formId, values } = formInstance;
        const form = await this.formRepo.findOneOrError(formId);
        const taskMailToList = this.svc.getTaskMailToByActivityId(
            form,
            processTaskId,
        );
        if (!taskMailToList) return;

        for (const taskMailTo of taskMailToList) {
            await this.sendNotifyByFormTaskMailToItem(
                taskMailTo,
                taskStatus,
                formInstance,
                form,
            );
        }
    }

    async sendCompletedNotifyToSpecifyUsers(formInstanceId: number) {
        const formInstance =
            await this.formInstanceRepo.findOneOrError(formInstanceId);
        const formInstanceTasks = formInstance.formInstanceTasks;
        const currentTask = formInstanceTasks[0];
        const { formId, values } = formInstance;
        const form = await this.formRepo.findOneOrError(formId);
        const taskMailToList = this.svc.getTaskMailToByActivityId(form, 'End');
        if (!taskMailToList) return;

        for (const taskMailTo of taskMailToList) {
            await this.sendNotifyByFormTaskMailToItem(
                taskMailTo,
                EnumTaskStatusStr.Completed,
                formInstance,
                form,
            );
        }
    }

    /* ---- */

    private async getUserCodesByTaskMailTo(
        formInstance: FormInstance,
        formTaskMailTo: FormTaskMailToItem,
        sendTaskStatus: EnumTaskStatusStr,
        formTaskAssignees: FormTaskAssignees,
    ) {
        const userCodes: string[] = [];
        const { taskStatus, assignees, skipAssignees } = formTaskMailTo;

        if (taskStatus && taskStatus.length > 0) {
            const runTaskStatus = taskStatus.includes(sendTaskStatus);
            if (!runTaskStatus) return [];
        }
        if (!assignees) return [];
        for (const taskAssigneeInfo of assignees) {
            const data = await this.getUserCodesByTaskAssigneeInfo(
                taskAssigneeInfo,
                formInstance,
                formTaskAssignees,
            );
            userCodes.push(...data);
        }
        const skipUserCodes: string[] = [];
        if (!skipAssignees) return Array.from(new Set(userCodes));
        for (const taskAssigneeInfo of skipAssignees) {
            const data = await this.getUserCodesByTaskAssigneeInfo(
                taskAssigneeInfo,
                formInstance,
                formTaskAssignees,
            );
            skipUserCodes.push(...data);
        }
        const mailUserCodes = userCodes.filter(
            (i) => !skipUserCodes.includes(i),
        );
        return Array.from(new Set(mailUserCodes));
    }

    private async getUserCodesByTaskAssigneeInfo(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formInstance: FormInstance,
        formTaskAssignees: FormTaskAssignees,
    ) {
        const userCodes: string[] = [];
        const assigneeUsers =
            await this.formTaskAssigneeUCase.getTaskAssigneeUsersByTaskAssigneeInfo(
                taskAssigneeInfo,
                formInstance,
                formTaskAssignees,
            );
        for (const assigneeUser of assigneeUsers) {
            const user = await this.userService.findOne(assigneeUser.id);
            if (!user) continue;
            userCodes.push(user.code);
        }
        return userCodes;
    }

    private async sendNotifyByFormTaskMailToItem(
        taskMailTo: FormTaskMailToItem,
        taskStatus: EnumTaskStatusStr,
        formInstance: FormInstance,
        form: Form,
    ) {
        const { status } = taskMailTo;
        const getFormTaskAssigneesFn: GetFormTaskAssigneesFn =
            await this.formConfTaskAssigneesUCase.getGetFormTaskAssigneesFn(
                form.id,
                form.version,
            );
        const filterData = new TaskAssigneesFilterDataDTO(
            formInstance.values,
            formInstance,
        );
        const formTaskAssignees = getFormTaskAssigneesFn(filterData);
        if (!formTaskAssignees) return;

        const userCodes = await this.getUserCodesByTaskMailTo(
            formInstance,
            taskMailTo,
            taskStatus,
            formTaskAssignees,
        );
        const formInstanceId = formInstance.id;
        const formName = form.name;
        const formCode = form.code ?? '';
        const createUserId = formInstance.createdUserId;
        const context = this.getContextByEnumMailToStatus(
            formInstance,
            form,
            status,
        );
        const subject = this.getSubjectByEnumMailToStatus(
            formInstance,
            form,
            status,
        );

        for (const userCode of userCodes) {
            const mailUserCode = userCode;

            const body = await SMTPHelpers.template({
                context,
                formCode,
                formInstanceId,
            });

            if (!mailUserCode) {
                return false;
            }
            const user = await this.userService.findOneByCode(mailUserCode);
            await this.smptSvc.send({
                subject,
                body,
                to: user ? [user.email] : [],
                createUserId: createUserId,
            });
        }
    }

    /* ------------- */

    private getContextByEnumMailToStatus(
        formInstance: FormInstance,
        form: Form,
        status = EnumMailToStatus.Save,
    ) {
        const formName = form.name;

        let context = '';
        switch (status) {
            case EnumMailToStatus.AddAssignee:
                context = `有一件${formName}簽核已加簽`;
                break;
            case EnumMailToStatus.AdditionalDocs:
                context = `有一件${formName}簽核已新增附件`;
                break;
            case EnumMailToStatus.Save:
                context = `有一件${formName}簽核已簽核完成`;
                break;
            case EnumMailToStatus.Send:
                context = `有一件${formName}簽核已送出申請`;
                break;
            case EnumMailToStatus.Reject:
                context = `有一件${formName}簽核已被駁回`;
                break;
            case EnumMailToStatus.Complete:
                context = `有一件${formName}簽核已結束`;
                break;
            default:
                context = `有一件${formName}簽核已簽核完成`;
        }

        return context;
    }

    private getSubjectByEnumMailToStatus(
        formInstance: FormInstance,
        form: Form,
        status = EnumMailToStatus.Save,
    ) {
        const formName = `${form.name}#${formInstance.id}`;
        const userCode = formInstance.ownerUser?.code ?? '';
        const userName = formInstance.ownerUser?.name ?? '';
        let subject = '';
        switch (status) {
            case EnumMailToStatus.AddAssignee:
                subject = `[簽核提醒通知][${formName}] ${userCode}${userName} 已加簽`;
                break;
            case EnumMailToStatus.AdditionalDocs:
                subject = `[簽核提醒通知][${formName}] ${userCode}${userName} 已新增附件`;
                break;
            case EnumMailToStatus.Save:
                subject = `[簽核提醒通知][${formName}] ${userCode}${userName} 已簽核完成`;
                break;
            case EnumMailToStatus.Send:
                subject = `[簽核提醒通知][${formName}] ${userCode}${userName} 已送出申請`;
                break;
            case EnumMailToStatus.Reject:
                subject = `[簽核提醒通知][${formName}] ${userCode}${userName} 已被駁回`;
                break;
            case EnumMailToStatus.Complete:
                subject = `[簽核結束提醒通知][${formName}] ${userCode}${userName} 已簽核結束`;
                break;
            default:
                subject = `[簽核提醒通知][${formName}] ${userCode}${userName} 已簽核完成`;
        }
        return subject;
    }
}
