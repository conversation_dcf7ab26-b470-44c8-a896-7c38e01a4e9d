import { Service, Inject } from 'typedi';
import { TaskAssigneeRepository } from '@/modules/camunda/providers/taskAssignee/taskAssignee.repository';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { Task } from '../../types/task.type';
import { FormTaskConfigUseCase } from './form.taskConfig.ucase';
import { GetFormTaskAssigneesFn } from '@/modules/form/formConf/types/formConfTaskAssignees.types';
import { TaskProperty } from './taskProperty.type';

@Service()
export class TaskPropertyUseCase {
    @Inject()
    private taskAssigneeRepo: TaskAssigneeRepository;
    @Inject()
    private formTaskConfigUCase: FormTaskConfigUseCase;

    async getTaskProperty(
        getFormTaskAssigneesFn: GetFormTaskAssigneesFn,
        camundaTask: Task,
        formInstance: FormInstance,
    ) {
        const { taskDefinitionKey } = camundaTask;
        const property: TaskProperty = {};
        await this.formTaskConfigUCase.setProperties(
            getFormTaskAssigneesFn,
            taskDefinitionKey,
            formInstance,
            property,
        );
        return property;
    }
}
