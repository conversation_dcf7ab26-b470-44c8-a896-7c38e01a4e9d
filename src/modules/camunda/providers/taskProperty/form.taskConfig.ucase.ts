import { Inject, Service } from 'typedi';
import {
    Assignee<PERSON>ser,
    SpecifyUser,
    TaskProperty,
} from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import _ from 'lodash';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { FormService } from '@/modules/form/form/providers/form.service';
import {
    FormTaskConfig,
    FormTaskAssigneeInfo,
    FormTaskAssignees,
    FormTaskConfigInfoValue,
} from '@/modules/form/form/types/form.taskSetting.type';
import { FormTaskAssigneeUseCase } from '../taskAssignee/form.taskAssignee.ucase';
import { Assignee } from '@/modules/assignee/types/assignee.type';
import { GetFormTaskAssigneesFn } from '../../../form/formConf/types/formConfTaskAssignees.types';
import { TaskAssigneesFilterDataDTO } from '@/modules/form/formConf/types/taskAssigneesFilterData.dto';

@Service()
export class FormTaskConfigUseCase {
    @Inject()
    private repo: FormRepository;
    @Inject()
    private svc: FormService;
    @Inject()
    private formTaskAssigneeUCase: FormTaskAssigneeUseCase;

    async setProperties(
        getFormTaskAssigneesFn: GetFormTaskAssigneesFn,
        taskDefinitionKey: string,
        formInstance: FormInstance,
        property: TaskProperty = {},
    ) {
        const form = await this.repo.findOneOrError(formInstance.formId);
        const configs = this.svc.getTaskConfigsByActivityId(
            form,
            taskDefinitionKey,
        );
        const filterData = new TaskAssigneesFilterDataDTO(
            formInstance.values,
            formInstance,
        );
        const formTaskAssignees = getFormTaskAssigneesFn(filterData);
        if (!configs || !formTaskAssignees) return property;
        const propertyMap = await this.getPropertyMap(
            formInstance,
            configs,
            formTaskAssignees,
        );
        const mapToObj = Object.fromEntries(propertyMap.entries());
        property = Object.assign(property, mapToObj);
        return property;
    }

    async genPropertiesByAssignee(
        taskDefinitionKey: string,
        formInstance: FormInstance,
        property: TaskProperty = {},
        taskAssignee: Assignee,
    ) {
        const form = await this.repo.findOneOrError(formInstance.formId);
        const configs = this.svc.getTaskConfigsByActivityId(
            form,
            taskDefinitionKey,
        );
        if (!configs) return property;
        this.genPropertyMapByAssignee(property, configs, taskAssignee);
        return property;
    }

    private async getPropertyMap(
        formInstance: FormInstance,
        configs: FormTaskConfig,
        formTaskAssignees: FormTaskAssignees,
    ) {
        const propertyMap = new Map<
            string,
            | SpecifyUser[]
            | FormTaskAssigneeInfo[]
            | FormTaskConfigInfoValue
            | AssigneeUser[]
            | undefined
        >();
        for (const [key, formTaskConfigInfo] of Object.entries(configs)) {
            const { value, type } = formTaskConfigInfo;
            if (type == 'value') {
                propertyMap.set(key, value);
            }
            if (type == 'assignee' && _.isArray(value)) {
                const assigneeUsers = await this.getTaskAssigneeUsers(
                    value as FormTaskAssigneeInfo[],
                    formInstance,
                    formTaskAssignees,
                );
                propertyMap.set(key, assigneeUsers);
            }
        }
        return propertyMap;
    }
    private genPropertyMapByAssignee(
        property: TaskProperty,
        configs: FormTaskConfig,
        taskAssignee: Assignee,
    ) {
        for (const [key, formTaskConfigInfo] of Object.entries(configs)) {
            const { value, type, taskAssigneeFilterKey } = formTaskConfigInfo;
            const taskAssigneeFilter = taskAssigneeFilterKey
                ? property[taskAssigneeFilterKey]
                : undefined;
            if (
                type !== 'taskAssigneeFilter' ||
                !taskAssigneeFilter ||
                !_.isArray(taskAssigneeFilter)
            )
                continue;
            const target = taskAssigneeFilter
                .filter((i) => !!i.id)
                .map((i) => i.id)
                .includes(taskAssignee.userId);
            if (!target) continue;
            property[key] = value;
        }
        return property;
    }

    private async getTaskAssigneeUsers(
        assignees: FormTaskAssigneeInfo[],
        formInstance: FormInstance,
        formTaskAssignees: FormTaskAssignees,
    ) {
        const assigneeUserMap = new Map<number, AssigneeUser>();
        for (const taskAssigneeInfo of assignees) {
            const assigneeUsers =
                await this.formTaskAssigneeUCase.getTaskAssigneeUsersByTaskAssigneeInfo(
                    taskAssigneeInfo,
                    formInstance,
                    formTaskAssignees,
                );
            for (const assigneeUser of assigneeUsers) {
                assigneeUserMap.set(assigneeUser.id, assigneeUser);
            }
        }
        return Array.from(assigneeUserMap.values());
    }
}
