import { EnumTaskStatusStr } from '@/modules/form/form/types/form.settings.type';
import { EnumFormTaskAssigneeType } from '@/modules/form/form/types/form.taskSetting.type';

export enum EnumAssigneeType {
    /**
     * 起單人 (發起人)
     * TODO: 之後可移除，form.task_settings 有對應的實現方式
     */
    Initiator = 'initiator',
    /**
     * 直屬主管
     * TODO: 之後可移除，form.task_settings 有對應的實現方式
     */
    DirectSupervisor = 'direct_supervisor',
    /**
     * 指定部門主管
     * TODO: 好像沒用到，之後移除
     */
    SpecifyDepartmentHead = 'specify_department_head',
    /**
     * 指定人員
     */
    SpecifyUser = 'specify_user',
    /**
     * 指定人員清單
     * TODO: 之後可移除，form.task_settings 有對應的實現方式
     */
    SpecifyUserList = 'specify_user_list',
    /**
     * 指定人員的簽核 直屬主管
     */
    PrefixFormDirectSupervisor = 'prefix_form_direct_supervisor',
    /**
     * 指定人員的簽核 部門主管
     */
    PrefixFormDepartmentHead = 'prefix_form_department_head',
    /**
     * 表單指定簽核人員
     * user_str / user_str[]
     */
    PrefixFormSpecifyUser = 'prefix_form_specify_user', //
    /**
     * 依「流程類別」對應不同關卡簽核人
     * TODO: 之後可移除，form.task_settings 有對應的實現方式
     */
    SpecifyCategoryUser = 'specify_category_user',
    /**
     * 關卡指定人員
     * TODO: 之後可移除，form.task_settings 有對應的實現方式
     */
    SpecifyActivityUser = 'specify_activity_user',
}

// TODO: 移到 table 配置，之後移除
export enum EnumAdditionalDocsType {
    Initiator = 'initiator', // 起單人 (發起人)
    DirectSupervisor = 'direct_supervisor', // 直屬主管
    SpecifyDepartmentHead = 'specify_department_head', // 指定部門主管
}

export type FormSpecifyUser = {
    dept_code: string;
    user_code: string;
};

export enum EnumSameAssigneeFlow {
    Again = 'again', // 再簽一次
    Ignore = 'ignore', // 跳過
}

export enum EnumAllowSkipType {
    DirectSupervisor = 'direct_supervisor', // 直屬主管
    SpecifyUser = 'specify_user', //
    DirectSupervisorOrSpecifyUser = 'direct_supervisor_or_specify_user',
}

export type AssigneeUser = {
    id: number;
    departmentId: number;
    companyId: number;
};

export type SpecifyUser = {
    companyId?: number;
    deptCode: string;
    userCode: string;
};

export type SpecifyActivityUser = {
    activityId: string;
    userCode: string;
    deptCode: string;
};

export type SpecifyCategoryUser = SpecifyActivityUser & {
    category: string;
};

export type TaskProperty = {
    assigneeType?: EnumAssigneeType | EnumFormTaskAssigneeType; // 簽核對像類型
    companyId?: string; // 簽核對像為「指定部門或人員」時使用
    departmentId?: string; // 簽核對像為「指定部門或人員」時使用
    deptCode?: string; // 簽核對像為「指定部門或人員」時使用
    userCode?: string; // 簽核對像為「指定人員」時使用
    allowAddAssignee?: boolean; // 是否允許加簽(或簽)
    allowAddCountersign?: boolean; // 是否允許加簽(會簽)
    sameAssigneeFlow?: EnumSameAssigneeFlow; // 與上一關相同簽核人時的流程
    userCodes?: string[]; // 簽核對像為「指定人員清單」時使用
    user?: AssigneeUser;
    users?: AssigneeUser[];
    onlyApproved?: boolean; // 只能核准
    /**
     * 附件對像: 延續使用 部分簽核對像類型
     */
    additionalDocsType?: EnumAdditionalDocsType;
    /**
     * 是否可以附件
     */
    allowAdditionalDocs?: boolean;
    /**
     * 可以附件的人員
     */
    additionalDocsUsers?: AssigneeUser[];
    appendFormData?: string[];
    specifyUser?: SpecifyUser;
    isSkipTaskSkipMail?: boolean; // 跳過任務是否跳過發信
    allowSkipNotifyMailTaskStatus?: EnumTaskStatusStr[]; // 允許跳過信件通知的任務狀態 預設 Approved
    allowSkipNotifyMailUsers?: AssigneeUser[]; // 允許跳過信件通知的人員
    allowSkipUsers?: AssigneeUser[]; // 允許跳關的人員
    filterAllowSkipUsers?: AssigneeUser[]; // 過濾允許跳關的人員
    allowSkipType?: EnumAllowSkipType; // 是否允許跳過關卡簽核
    allowSkipUserCodes?: string[]; // 指定允許跳過關卡簽核人員
    allowReturn?: boolean; // 是否允許退回
    /* 留言信件通知 */
    /**
     * 關卡判斷 是否跳過所有人留言信件通知
     */
    isSkipMessageMail?: boolean;
    /**
     * 關卡判斷 是否跳過 指定人員留言信件通知
     */
    isSkipMessageMailUsers?: AssigneeUser[];
    /**
     * 指定關卡 關卡內人員跳過留言通知
     */
    isSkipMessageMailTask?: boolean;
    /**
     * 指定是否代理簽核
     */
    isProxyApproval?: boolean;
    /**
     * 允許上傳的檔案副檔名
     */
    allowUploadFileExtensions?: string[] | null;
    /**
     * 允許表單編輯者
     *
     * 供 currentTask 上配置，供前端判斷是否允許使用者編輯表單
     */
    allowFormEditorUsers?: AssigneeUser[];
    allowFormEditor?: boolean;
};
