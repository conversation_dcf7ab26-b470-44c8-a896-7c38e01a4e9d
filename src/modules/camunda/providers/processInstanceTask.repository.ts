import { Service } from 'typedi';
import { camundaAxios } from '@/common/helpers/axios.helper';
import {
    CompleteParams,
    SearchHistoryParams,
    SetAssigneeParams,
    Task,
    TaskHistory,
    UpdateVariableParams,
} from '../types/task.type';
import type { SearchParams } from '../types/task.type';
import type { Variable, Variables } from '../types/processInstance.type';

@Service()
export class ProcessInstanceTaskRepository {
    async findOne(id: string): Promise<Task> {
        const endpoint = `/task/${id}`;
        const { data } = await camundaAxios.get<Task>(endpoint);

        return data;
    }

    async findOneVariable(params: {
        id: string;
        variable: string;
    }): Promise<Variable | null> {
        const variables = await this.findAllVariables(params.id);
        return variables ? variables[params.variable] : null;
    }

    async findAllVariables(id: string): Promise<Variables> {
        const endpoint = `/task/${id}/variables`;
        const { data } = await camundaAxios.get<Variables>(endpoint);

        return data;
    }

    async search(params: SearchParams): Promise<Task[]> {
        const endpoint = `/task`;
        const { data } = await camundaAxios.post<Task[]>(endpoint, params);

        return data;
    }

    async searchHistory(params: SearchHistoryParams): Promise<TaskHistory[]> {
        const endpoint = `/history/task`;
        const { data } = await camundaAxios.post<TaskHistory[]>(
            endpoint,
            params,
        );
        return data;
    }

    async setAssignee(params: SetAssigneeParams): Promise<void> {
        const { id, assignee } = params;
        const endpoint = `/task/${id}/assignee`;

        await camundaAxios.post(endpoint, { userId: assignee });
    }

    async complete(params: CompleteParams): Promise<void> {
        const { id, variables } = params;
        const endpoint = `/task/${id}/complete`;

        await camundaAxios.post(endpoint, {
            variables: variables,
        });
    }

    async updateVariable(params: UpdateVariableParams): Promise<void> {
        const { id, varName, value, type, valueInfo } = params;
        const endpoint = `/task/${id}/variables/${varName}`;

        await camundaAxios.put(endpoint, {
            value,
            type,
            valueInfo,
        });
    }
}
