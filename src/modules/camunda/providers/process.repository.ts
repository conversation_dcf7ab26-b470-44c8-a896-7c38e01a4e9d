import { Service } from 'typedi';
import { camundaAxios } from '@/common/helpers/axios.helper';
import { DefinitionXML, Process } from '../types/process.type';

@Service()
export class ProcessRepository {
    async findOne(key: string): Promise<Process> {
        const endpoint = `/process-definition/key/${key}`;
        const { data } = await camundaAxios.get<Process>(endpoint);

        return data;
    }

    async definitionXML(key: string): Promise<DefinitionXML> {
        const endpoint = `/process-definition/key/${key}/xml`;
        const { data } = await camundaAxios.get<DefinitionXML>(endpoint);

        return data;
    }
}
