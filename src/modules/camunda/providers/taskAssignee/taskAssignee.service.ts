import { getNestedFormValue } from '@/common/helpers/nested.helper';
import { FormSpecifyUser } from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import {
    FormTaskAssigneeInfo,
    FormTaskSpecifyUser,
    EnumFormTaskAssigneeType,
    FormTaskAssignees,
} from '@/modules/form/form/types/form.taskSetting.type';
import { InstancesValuesData } from '@/modules/form/formInstance/types/formInstance.type';
import _ from 'lodash';
import { Service } from 'typedi';

@Service()
export class TaskAssigneeService {
    getUserStrList(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ) {
        const { key } = taskAssigneeInfo;
        if (!key) return [];
        const data = getNestedFormValue(formValues, key);
        if (!data) return [];
        const userStrList: string[] = [];
        if (_.isArray(data)) {
            const strData = data.filter((i) => _.isString(i));
            userStrList.push(...strData);
        } else if (_.isString(data)) {
            userStrList.push(data);
        }
        return userStrList;
    }

    getFormSpecifyUserList(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ): FormSpecifyUser[] {
        const { key } = taskAssigneeInfo;
        if (!key) return [];
        const data = getNestedFormValue(formValues, key);
        if (!data) return [];
        const userStrList: FormSpecifyUser[] = [];
        if (_.isArray(data)) {
            const specifyUsers = data.filter((i) => _.isObject(i));
            userStrList.push(...this.transformFormSpecifyUsers(specifyUsers));
        } else if (_.isObject(data)) {
            userStrList.push(...this.transformFormSpecifyUsers([data]));
        }
        return userStrList;
    }

    transformFormSpecifyUsers(formSpecifyUsers: FormTaskSpecifyUser[]) {
        const mapFormSpecifyUser = (
            i: FormTaskSpecifyUser,
        ): FormSpecifyUser => ({
            user_code: i.user_code ?? i.userCode ?? '',
            dept_code: i.dept_code ?? i.deptCode ?? i.departmentCode ?? '',
        });
        const result: FormSpecifyUser[] =
            formSpecifyUsers.map(mapFormSpecifyUser);
        return result;
    }

    /**
     * specify_user 目前沒有固定的值 可能是 userStr, userStr[], FormSpecifyUser, FormSpecifyUser[]
     * @param taskAssigneeInfo
     * @param formValues
     * @returns
     */
    getFormValueTaskSpecifyUsers(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ): any[] {
        const { type, key } = taskAssigneeInfo;
        if (type != 'specify_user' || !key) return [];
        // 關卡多人員簽核 不拿 variables 了
        // const value = getNestedVariableValue(variables, key);
        const value = getNestedFormValue(formValues, key);
        if (!value) {
            return [];
        }
        if (!_.isArray(value)) {
            return [value];
        }
        const valueArray = value.map((i) => {
            if (_.isString(i)) {
                return i;
            }
            if (_.isObject(i)) {
                return this.transformFormSpecifyUsers([i])[0];
            }
            return i;
        });
        return valueArray;
    }

    getFormTaskSpecifyUsers(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ): FormSpecifyUser[] {
        const { type, specifyUser, filterKey } = taskAssigneeInfo;
        if (type != 'specify_user' || !specifyUser) return [];

        if (!filterKey) return this.transformFormSpecifyUsers(specifyUser);
        const formValue = getNestedFormValue(formValues, filterKey);
        if (!formValue) {
            return [];
        }
        const filterValue = formValue as string;
        const users = specifyUser.filter(
            (i) => !i.filterBy || i.filterBy == filterValue,
        );
        return this.transformFormSpecifyUsers(users);
    }

    /**
     * 由 formInstance.value or appendFormData 產生 multi user tasks
     * @param form
     * @param formData
     * @returns
     */
    genFormValueSpecifyUsersMap(
        formTaskAssignees: FormTaskAssignees,
        formData: JSON,
    ) {
        let result: Map<string, any[]> = new Map();
        if (!formTaskAssignees || !formData) return result;
        result = Object.entries(formTaskAssignees).reduce((pre, cur) => {
            const [activityId, taskAssigneeInfo] = cur;
            if (!taskAssigneeInfo?.length) return pre;
            for (const taskAssigneeInfoItem of taskAssigneeInfo) {
                if (
                    taskAssigneeInfoItem.type !=
                        EnumFormTaskAssigneeType.SpecifyUser &&
                    !('key' in taskAssigneeInfoItem)
                )
                    continue;

                const specifyUsers = this.getFormValueTaskSpecifyUsers(
                    taskAssigneeInfoItem,
                    formData,
                );

                if (specifyUsers || _.isArray(specifyUsers)) {
                    const getSpecifyUsers = pre.get(activityId) ?? [];
                    getSpecifyUsers.push(...specifyUsers);
                    const setSpecifyUsers = new Set(getSpecifyUsers);
                    pre.set(activityId, Array.from(setSpecifyUsers));
                }
            }
            return pre;
        }, new Map<string, any[]>());
        return result;
    }

    genFormSpecifyUserMap(
        formTaskAssignees: FormTaskAssignees,
        formData: JSON,
    ): Map<string, FormSpecifyUser[]> {
        let result: Map<string, FormSpecifyUser[]> = new Map();
        if (!formTaskAssignees) return result;
        result = Object.entries(formTaskAssignees).reduce((pre, cur) => {
            const [activityId, taskAssigneeInfo] = cur;
            // 如果沒有 taskAssigneeInfo 或者是空陣列，就預設 activityId = []
            // 這樣可以避免在後續處理時出現多關卡取得不到 activityId 多人簽核
            if (!taskAssigneeInfo?.length) {
                pre.set(activityId, []);
                return pre;
            }
            for (const taskAssigneeInfoItem of taskAssigneeInfo) {
                if (
                    taskAssigneeInfoItem.type !=
                        EnumFormTaskAssigneeType.SpecifyUser &&
                    !('specifyUser' in taskAssigneeInfoItem)
                )
                    continue;
                const specifyUsers = this.getFormTaskSpecifyUsers(
                    taskAssigneeInfoItem,
                    formData,
                );
                const getSpecifyUsers = pre.get(activityId) ?? [];
                getSpecifyUsers.push(...specifyUsers);
                const setSpecifyUsers = new Set(getSpecifyUsers);
                pre.set(activityId, Array.from(setSpecifyUsers));
            }
            return pre;
        }, new Map<string, FormSpecifyUser[]>());
        return result;
    }
}
