import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import {
    AssigneeUser,
    EnumAssigneeType,
    SpecifyUser,
    TaskProperty,
} from '../taskProperty/taskProperty.type';
import { Assignee } from '@/modules/assignee/types/assignee.type';
import {
    EnumExtraAssigneeType,
    FormTaskAssigneeInfo,
} from '@/modules/form/form/types/form.taskSetting.type';

export type GetAssignees = {
    [key in EnumAssigneeType]: (
        owner: Owner,
        processTaskId: string,
        formInstance: FormInstance,
        property: TaskProperty,
    ) => Promise<Assignee[]>;
};

export type Owner = {
    companyId: number;
    departmentId: number;
    departmentCode: string;
    userId: number;
    userCode: string;
};

export type SearchParams = {
    formInstance: FormInstance;
    property: TaskProperty;
    processTaskId: string;
};

export type ExtraAssigneeType = {
    type: EnumExtraAssigneeType;
    key: string;
};

export type BaseStepTaskAssigneeType = {
    stepSpecifyUsers: SpecifyUser[];
    stepAssigneeUsers: AssigneeUser[];
    stepAssigneeExtraTypes: ExtraAssigneeType[];
    stepRefSteps: string[];
};
export type BaseRefStepTaskAssigneeType = {
    refStepSpecifyUsers: SpecifyUser[];
    refStepAssigneeUsers: AssigneeUser[];
    refStepAssigneeExtraTypes: ExtraAssigneeType[];
};

export type StepTaskAssigneeType = BaseStepTaskAssigneeType &
    BaseRefStepTaskAssigneeType;
export type ProcessTaskAssigneeType = {
    processTaskId: string;
    formTaskAssignees: FormTaskAssigneeInfo[];
} & StepTaskAssigneeType;
