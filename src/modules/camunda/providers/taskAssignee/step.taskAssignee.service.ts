import { getNestedFormValue } from '@/common/helpers/nested.helper';
import {
    FormTaskAssignees,
    FormTaskAssigneeInfo,
    EnumFormTaskAssigneeType,
    EnumExtraAssigneeType,
    FormTaskSpecifyUser,
} from '@/modules/form/form/types/form.taskSetting.type';
import { InstancesValuesData } from '@/modules/form/formInstance/types/formInstance.type';
import { Helpers } from '@clinico/clinico-node-framework';
import _ from 'lodash';
import { Service } from 'typedi';
import { SpecifyUser, AssigneeUser } from '../taskProperty/taskProperty.type';
import { BaseStepTaskAssigneeType } from './taskAssignee.type';

@Service()
export class StepTaskAssigneeService {
    genBaseStepTaskAssigneesMap(
        formTaskAssignees: FormTaskAssignees,
        formValues: JSON,
    ) {
        const data = Array.from(Object.entries(formTaskAssignees)).reduce(
            (pre, [activityId, taskAssigneeInfos]) => {
                const baseInfo: BaseStepTaskAssigneeType = {
                    stepSpecifyUsers: [],
                    stepAssigneeUsers: [],
                    stepAssigneeExtraTypes: [],
                    stepRefSteps: [],
                };
                const baseStepTaskAssignee = this.genBaseStepTaskAssignee(
                    taskAssigneeInfos,
                    formValues,
                );
                const info: BaseStepTaskAssigneeType = {
                    ...baseInfo,
                    ...baseStepTaskAssignee,
                };
                pre.set(activityId, info);
                return pre;
            },
            new Map<string, BaseStepTaskAssigneeType>(),
        );
        return data;
    }

    private genBaseStepTaskAssignee(
        taskAssigneeInfos: FormTaskAssigneeInfo[],
        formValues: JSON,
    ): BaseStepTaskAssigneeType {
        const result: BaseStepTaskAssigneeType = {
            stepSpecifyUsers: [],
            stepAssigneeUsers: [],
            stepAssigneeExtraTypes: [],
            stepRefSteps: [],
        };
        for (const taskAssigneeInfo of taskAssigneeInfos) {
            const { type, key } = taskAssigneeInfo;
            switch (taskAssigneeInfo.type) {
                case EnumFormTaskAssigneeType.SpecifyUser:
                    {
                        const stepSpecifyUsers = this.getStepSpecifyUsers(
                            taskAssigneeInfo,
                            formValues,
                        );
                        result.stepSpecifyUsers.push(...stepSpecifyUsers);
                        const formValuesStepSpecifyUsers =
                            this.getStepSpecifyUsersByFormValue(
                                taskAssigneeInfo,
                                formValues,
                            );
                        result.stepSpecifyUsers.push(
                            ...formValuesStepSpecifyUsers,
                        );
                        const stepAssigneeUsers =
                            this.getStepAssigneeUsersByFormValue(
                                taskAssigneeInfo,
                                formValues,
                            );
                        result.stepAssigneeUsers.push(...stepAssigneeUsers);
                        // TODO: 需要移除處理 countersigners
                        const stepCountersignersAssigneeUsers =
                            this.getCountersignersStepAssigneeUsersByFormValue(
                                taskAssigneeInfo,
                                formValues,
                            );
                        result.stepAssigneeUsers.push(
                            ...stepCountersignersAssigneeUsers,
                        );
                    }
                    break;
                case EnumFormTaskAssigneeType.PrefixFormSpecifyUser:
                    {
                        const formValuesStepSpecifyUsers =
                            this.getStepSpecifyUsersByFormValue(
                                taskAssigneeInfo,
                                formValues,
                            );
                        result.stepSpecifyUsers.push(
                            ...formValuesStepSpecifyUsers,
                        );
                        const stepAssigneeUsers =
                            this.getStepAssigneeUsersByFormValue(
                                taskAssigneeInfo,
                                formValues,
                            );
                        result.stepAssigneeUsers.push(...stepAssigneeUsers);
                        // TODO: 需要移除處理 countersigners
                        const stepCountersignersAssigneeUsers =
                            this.getCountersignersStepAssigneeUsersByFormValue(
                                taskAssigneeInfo,
                                formValues,
                            );
                        result.stepAssigneeUsers.push(
                            ...stepCountersignersAssigneeUsers,
                        );
                    }
                    break;
                case EnumFormTaskAssigneeType.Step:
                    {
                        if (!key) break;
                        result.stepRefSteps.push(key);
                    }
                    break;
                case EnumFormTaskAssigneeType.PrefixFormDirectSupervisor:
                case EnumFormTaskAssigneeType.PrefixFormDepartmentHead:
                    {
                        if (!key) break;
                        const assigneeExtraType = type;
                        result.stepAssigneeExtraTypes.push({
                            type: assigneeExtraType as unknown as EnumExtraAssigneeType,
                            key: key,
                        });
                    }
                    break;
            }
        }
        return result;
    }

    /**
     * 新版 genBaseStepTaskAssigneesMap 使用的 function
     * @param taskAssigneeInfo
     * @param formValues
     * @returns
     */
    private getStepSpecifyUsers(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ): SpecifyUser[] {
        const { type, specifyUser, filterKey } = taskAssigneeInfo;
        if (type != 'specify_user' || !specifyUser) return [];

        if (!filterKey) return this.transformStepSpecifyUsers(specifyUser);
        const formValue = getNestedFormValue(formValues, filterKey);
        if (!formValue) {
            return [];
        }
        const filterValue = formValue as string;
        const users = specifyUser.filter(
            (i) => !i.filterBy || i.filterBy == filterValue,
        );
        return this.transformStepSpecifyUsers(users);
    }

    /**
     * 新版 genBaseStepTaskAssigneesMap 使用的 function
     * @param formSpecifyUsers
     * @returns
     */
    private transformStepSpecifyUsers(formSpecifyUsers: FormTaskSpecifyUser[]) {
        const mapFormSpecifyUser = (i: FormTaskSpecifyUser): SpecifyUser => ({
            userCode: i.user_code ?? i.userCode ?? '',
            deptCode: i.dept_code ?? i.deptCode ?? i.departmentCode ?? '',
        });
        const result: SpecifyUser[] = formSpecifyUsers
            .map(mapFormSpecifyUser)
            .filter((i) => i.userCode && i.deptCode);
        return result;
    }

    /**
     * 新版 genBaseStepTaskAssigneesMap 使用的 function
     * @param taskAssigneeInfo
     * @param formValues
     * @returns
     */
    private getStepSpecifyUsersByFormValue(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ): SpecifyUser[] {
        const { type, key } = taskAssigneeInfo;
        const isValidType = [
            EnumFormTaskAssigneeType.SpecifyUser,
            EnumFormTaskAssigneeType.PrefixFormSpecifyUser,
        ].includes(type);
        if (!isValidType || !key) return [];
        const value = getNestedFormValue(formValues, key);
        if (!value) return [];
        // value
        const data: any[] = [];
        if (!_.isArray(value) && _.isObject(value)) {
            data.push(value);
        }
        if (_.isArray(value)) {
            data.push(...value);
        }
        const result = data.filter((i) => _.isObject(i));
        return this.transformStepSpecifyUsers(result);
    }

    /**
     * 新版 genBaseStepTaskAssigneesMap 使用的 function
     * @param taskAssigneeInfo
     * @param formValues
     * @returns
     */
    private getStepAssigneeUsersByFormValue(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ): AssigneeUser[] {
        const { type, key } = taskAssigneeInfo;
        const isValidType = [
            EnumFormTaskAssigneeType.SpecifyUser,
            EnumFormTaskAssigneeType.PrefixFormSpecifyUser,
        ].includes(type);
        if (!isValidType || !key) return [];
        const value = getNestedFormValue(formValues, key);
        if (!value) return [];
        // value
        const data: string[] = [];
        if (!_.isArray(value) && _.isString(value)) {
            data.push(value);
        }
        if (_.isArray(value)) {
            data.push(...value);
        }
        return data
            .filter((i) => _.isString(i))
            .map((i) => this.genAssigneeUser(i));
    }

    /**
     * 新版 genBaseStepTaskAssigneesMap 使用的 function
     * @param taskAssigneeInfo
     * @param formValues
     * @returns
     */
    private getCountersignersStepAssigneeUsersByFormValue(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ): AssigneeUser[] {
        const { type, key } = taskAssigneeInfo;
        const isValidType = [
            EnumFormTaskAssigneeType.PrefixFormSpecifyUser,
        ].includes(type);
        if (!isValidType || key) return [];
        const value = getNestedFormValue(formValues, 'countersigners');
        if (!value) return [];
        // value
        const data: string[] = [];
        if (!_.isArray(value) && _.isString(value)) {
            data.push(value);
        }
        if (_.isArray(value)) {
            data.push(...value);
        }
        return data
            .filter((i) => _.isString(i))
            .map((i) => this.genAssigneeUser(i));
    }

    /**
     * 新版 genBaseStepTaskAssigneesMap 使用的 function
     * @param userStr
     * @returns
     */
    private genAssigneeUser(userStr: string): AssigneeUser {
        const [companyId, departmentId, userId] = userStr.split(',');
        return {
            companyId: Helpers.Str.toIntOrNull(companyId),
            departmentId: Helpers.Str.toIntOrNull(departmentId),
            id: Helpers.Str.toIntOrNull(userId),
        };
    }
}
