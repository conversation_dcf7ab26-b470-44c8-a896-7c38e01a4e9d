import { Inject, Service } from 'typedi';
import {
    AssigneeUser,
    FormSpecifyUser,
    TaskProperty,
} from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { TaskAssigneeRepository } from '@/modules/camunda/providers/taskAssignee/taskAssignee.repository';
import { DepartmentService } from '@/modules/organization/providers/department.service';
import { Owner } from '@/modules/camunda/providers/taskAssignee/taskAssignee.type';
import * as VariablesHelper from '@/common/helpers/variables.helper';
import { CountersignerVariable } from '@/common/helpers/variables.helper';
import { UserService } from '@/modules/organization/providers/user.service';
import { CompanyService } from '@/modules/organization/providers/company.service';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import _ from 'lodash';
import {
    GetAssigneeUsers,
    EnumFormTaskAssigneeType,
    FormTaskAssigneeInfo,
    GetFormTaskAssigneeProperty,
    FormTaskAssigneeProperty,
    FormTaskAssignees,
    EnumElementVariableName,
} from '@/modules/form/form/types/form.taskSetting.type';
import {
    EnumUserVariableName,
    InstancesValuesData,
} from '@/modules/form/formInstance/types/formInstance.type';
import {
    getNestedFormValue,
    getNestedVariableValue,
} from '@/common/helpers/nested.helper';
import { User } from '@/common/models/clinico/core/user.model';
import { Department } from '@/common/models/clinico/core/department.model';
import { Company } from '@/common/models/clinico/core/company.model';
import { UserPositionService } from '../../../organization/providers/userPosition.service';
import { Assignee } from '@/modules/assignee/types/assignee.type';
import { GetFormTaskAssigneesFn } from '@/modules/form/formConf/types/formConfTaskAssignees.types';
import { TaskAssigneeService } from '@/modules/camunda/providers/taskAssignee/taskAssignee.service';
import { TaskAssigneesFilterDataDTO } from '@/modules/form/formConf/types/taskAssigneesFilterData.dto';

@Service()
export class FormTaskAssigneeUseCase {
    @Inject()
    private taskAssigneeRepo: TaskAssigneeRepository;
    @Inject()
    private departmentSvc: DepartmentService;
    @Inject()
    private userSvc: UserService;
    @Inject()
    private userPositionSvc: UserPositionService;
    @Inject()
    private companySvc: CompanyService;
    @Inject()
    private taskAssigneeSvc: TaskAssigneeService;

    private formTaskAssigneeUsers: GetAssigneeUsers = {
        [EnumFormTaskAssigneeType.SpecifyUser]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
        ): Promise<AssigneeUser[]> =>
            this.getAssigneeUsersBySpecifyUsers(taskAssigneeInfo, formValue),
        [EnumFormTaskAssigneeType.PrefixFormDirectSupervisor]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
        ): Promise<AssigneeUser[]> =>
            this.getAssigneeUsersByPrefixFormDirectSupervisor(
                taskAssigneeInfo,
                formValue,
            ),
        [EnumFormTaskAssigneeType.PrefixFormDepartmentHead]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
        ): Promise<AssigneeUser[]> =>
            this.getAssigneeUsersByPrefixFormDepartmentHead(
                taskAssigneeInfo,
                formValue,
            ),
        [EnumFormTaskAssigneeType.PrefixFormSpecifyUser]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
            formTaskAssignees: FormTaskAssignees,
        ): Promise<AssigneeUser[]> =>
            this.getAssigneeUsersByPrefixFormSpecifyUser(
                taskAssigneeInfo,
                formValue,
            ),
        [EnumFormTaskAssigneeType.Step]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
            formTaskAssignees: FormTaskAssignees,
        ): Promise<AssigneeUser[]> =>
            this.getAssigneeUsersByStep(
                taskAssigneeInfo,
                formValue,
                formTaskAssignees,
            ),
    };

    private getProperty: GetFormTaskAssigneeProperty = {
        [EnumFormTaskAssigneeType.SpecifyUser]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
            variables: Variables,
        ): Promise<FormTaskAssigneeProperty> =>
            this.genPropertyBySpecifyUser(
                taskAssigneeInfo,
                formValue,
                variables,
            ),
        [EnumFormTaskAssigneeType.PrefixFormDirectSupervisor]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
            variables: Variables,
        ): Promise<FormTaskAssigneeProperty> =>
            this.genPropertyByPrefixFormDirectSupervisor(
                taskAssigneeInfo,
                formValue,
                variables,
            ),
        [EnumFormTaskAssigneeType.PrefixFormDepartmentHead]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
            variables: Variables,
        ): Promise<FormTaskAssigneeProperty> =>
            this.genPropertyByPrefixFormDepartmentHead(
                taskAssigneeInfo,
                formValue,
                variables,
            ),
        [EnumFormTaskAssigneeType.PrefixFormSpecifyUser]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
            variables: Variables,
        ): Promise<FormTaskAssigneeProperty> =>
            this.genPropertyByPrefixFormSpecifyUser(
                taskAssigneeInfo,
                formValue,
                variables,
            ),
        [EnumFormTaskAssigneeType.Step]: (
            taskAssigneeInfo: FormTaskAssigneeInfo,
            formValue: InstancesValuesData,
            variables: Variables,
            formTaskAssignees: FormTaskAssignees,
        ): Promise<FormTaskAssigneeProperty> =>
            this.genPropertyStep(
                taskAssigneeInfo,
                formValue,
                variables,
                formTaskAssignees,
            ),
    };

    async getTaskAssignee(
        getFormTaskAssigneesFn: GetFormTaskAssigneesFn,
        processTaskId: string,
        formInstance: FormInstance,
        variables: Variables,
        property: TaskProperty,
    ) {
        const assignee = await this.getTaskAssigneeByFormTaskAssignee(
            getFormTaskAssigneesFn,
            processTaskId,
            formInstance,
            variables,
            property,
        );
        return assignee;
    }

    async getTaskAssigneeByFormTaskAssignee(
        getFormTaskAssigneesFn: GetFormTaskAssigneesFn,
        processTaskId: string,
        formInstance: FormInstance,
        variables: Variables,
        property: TaskProperty,
    ): Promise<Assignee | null> {
        const { assigneeType, specifyUser, assignee } =
            await this.getFormTaskAssigneeProperty(
                getFormTaskAssigneesFn,
                processTaskId,
                formInstance,
                variables,
            );
        if (!assigneeType || !specifyUser) return null;
        // update property information
        property.assigneeType = assigneeType;
        property.specifyUser = specifyUser;
        /**
         * 預設使用新版本的 assignee
         */
        if (assignee) {
            return assignee;
        }
        /**
         * 舊有方式使用 specifyUser
         * 增加 companyId 選填，防止多個 company 有相同的部門代碼
         */
        const filter = {
            departmentCode: specifyUser.deptCode,
            userCode: specifyUser.userCode,
        };
        if (specifyUser.companyId) {
            filter['companyId'] = specifyUser.companyId;
        }
        const specifyAssignee =
            await this.taskAssigneeRepo.findOneSpecifyUser(filter);
        return specifyAssignee;
    }

    async getTaskAssigneeUsersByTaskAssigneeInfo(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formInstance: FormInstance,
        formTaskAssignees: FormTaskAssignees,
    ) {
        const formValue = this.getInstanceFormValue(formInstance);
        const formTaskAssigneeType =
            taskAssigneeInfo.type in this.getProperty
                ? taskAssigneeInfo.type
                : EnumFormTaskAssigneeType.SpecifyUser;
        const assigneeUsers = await this.formTaskAssigneeUsers[
            formTaskAssigneeType
        ](taskAssigneeInfo, formValue, formTaskAssignees);
        return assigneeUsers;
    }

    /**
     * get FormTaskAssigneeProperty
     * @param taskDefinitionKey
     * @param formInstance
     * @param variables
     * @param property
     * @returns
     */
    private async getFormTaskAssigneeProperty(
        getFormTaskAssigneesFn: GetFormTaskAssigneesFn,
        taskDefinitionKey: string,
        formInstance: FormInstance,
        variables: Variables,
        property: FormTaskAssigneeProperty = {},
    ) {
        const formValues = this.getInstanceFormValue(formInstance);
        const filterData = new TaskAssigneesFilterDataDTO(
            formValues,
            formInstance,
        );
        const _formTaskAssignees = getFormTaskAssigneesFn(filterData);
        const _formTaskAssigneeInfo = _formTaskAssignees[taskDefinitionKey];
        if (!_formTaskAssigneeInfo || !_formTaskAssigneeInfo.length) {
            return property;
        }

        const formTaskPropertyList: any[] = [];
        for (const formTaskAssigneeInfoItem of _formTaskAssigneeInfo) {
            const formTaskAssigneeType =
                formTaskAssigneeInfoItem.type in this.getProperty
                    ? formTaskAssigneeInfoItem.type
                    : EnumFormTaskAssigneeType.SpecifyUser;

            const formTaskProperty = await this.getProperty[
                formTaskAssigneeType
            ](
                formTaskAssigneeInfoItem,
                formValues,
                variables,
                _formTaskAssignees,
            );
            formTaskPropertyList.push(formTaskProperty);
        }

        return formTaskPropertyList[0];
    }

    private async getAssigneeUsersBySpecifyUsers(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
    ): Promise<AssigneeUser[]> {
        const specifyUsers = this.taskAssigneeSvc.getFormTaskSpecifyUsers(
            taskAssigneeInfo,
            formValue,
        );
        const result =
            await this._getAssigneeUsersByFormSpecifyUserList(specifyUsers);
        return result;
    }

    // 從表單指定人員
    private async getAssigneeUsersByStep(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        formTaskAssignees: FormTaskAssignees,
    ) {
        const result: AssigneeUser[] = [];
        const key = taskAssigneeInfo.key;
        if (!key) return result;
        const stepTaskAssigneeInfo = formTaskAssignees[key];
        if (!stepTaskAssigneeInfo.length) return result;
        for (const stepTaskAssigneeInfoItem of stepTaskAssigneeInfo) {
            if (!stepTaskAssigneeInfoItem.type) continue;
            const assigneeUsers = await this.formTaskAssigneeUsers[
                stepTaskAssigneeInfoItem.type
            ](stepTaskAssigneeInfoItem, formValue, formTaskAssignees);
            result.push(...assigneeUsers);
        }
        return result;
    }

    private async getAssigneeUsersByPrefixFormSpecifyUser(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
    ) {
        const result: AssigneeUser[] = [];
        const userStrList = this.taskAssigneeSvc.getUserStrList(
            taskAssigneeInfo,
            formValue,
        );
        if (userStrList.length > 0) {
            return this._getAssigneeUsersByUserStrList(userStrList);
        }
        const formSpecifyUserList = this.taskAssigneeSvc.getFormSpecifyUserList(
            taskAssigneeInfo,
            formValue,
        );
        if (formSpecifyUserList.length > 0) {
            return this._getAssigneeUsersByFormSpecifyUserList(
                formSpecifyUserList,
            );
        }
        return result;
    }

    private async _getAssigneeUsersByFormSpecifyUserList(
        specifyUserList: FormSpecifyUser[],
    ) {
        const result: AssigneeUser[] = [];
        for (const specifyUser of specifyUserList) {
            const { user, dept } =
                await this.getUserInfoByVariableSpecifyUser(specifyUser);
            if (user && dept) {
                result.push({
                    id: user.id,
                    departmentId: dept.id,
                    companyId: dept.companyId,
                });
            }
        }
        return result;
    }

    private async _getAssigneeUsersByUserStrList(userStrList: string[]) {
        const result: AssigneeUser[] = [];
        for (const userStr of userStrList) {
            const { user, dept, company } = await this.getUserInfo(userStr);
            if (user && dept && company) {
                const assigneeUser: AssigneeUser = {
                    id: user.id,
                    departmentId: dept.id,
                    companyId: company.id,
                };
                result.push(assigneeUser);
            }
        }
        return result;
    }

    // form 人員的直屬主管
    private async getAssigneeUsersByPrefixFormDirectSupervisor(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
    ) {
        const result: AssigneeUser[] = [];
        const userAssigneeList =
            await this.getAssigneeUsersByPrefixFormSpecifyUser(
                taskAssigneeInfo,
                formValue,
            );
        for (const userAssignee of userAssigneeList) {
            const taskOwner: Owner = {
                companyId: userAssignee.companyId ?? 1,
                departmentId: userAssignee.departmentId ?? 1,
                departmentCode: '',
                userId: userAssignee.id ?? 1,
                userCode: '',
            };
            const assignee =
                await this.taskAssigneeRepo.findOneOwnerDirectSupervisor(
                    taskOwner,
                );
            if (
                assignee &&
                assignee.userId &&
                assignee.departmentId &&
                assignee.companyId
            ) {
                const assigneeUser: AssigneeUser = {
                    id: assignee.userId,
                    departmentId: assignee.departmentId,
                    companyId: assignee.companyId,
                };
                result.push(assigneeUser);
            }
        }
        return result;
    }

    // form 人員的部門主管
    private async getAssigneeUsersByPrefixFormDepartmentHead(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
    ) {
        const result: AssigneeUser[] = [];
        const userAssigneeList =
            await this.getAssigneeUsersByPrefixFormSpecifyUser(
                taskAssigneeInfo,
                formValue,
            );
        for (const userAssignee of userAssigneeList) {
            const assignee =
                await this.taskAssigneeRepo.findOneDepartmentSupervisor({
                    companyId: userAssignee.companyId ?? -1,
                    departmentId: userAssignee.departmentId,
                });
            if (
                assignee &&
                assignee.userId &&
                assignee.departmentId &&
                assignee.companyId
            ) {
                const assigneeUser: AssigneeUser = {
                    id: assignee.userId,
                    departmentId: assignee.departmentId,
                    companyId: assignee.companyId,
                };
                result.push(assigneeUser);
            }
        }
        return result;
    }

    /* property */

    private async _getSpecifyUserByVariableSpecifyUser(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        variables: Variables,
        property: FormTaskAssigneeProperty,
    ) {
        const specifyUser = getNestedVariableValue(
            variables,
            EnumElementVariableName.SpecifyUser,
        );
        if (!specifyUser) return false;

        if (_.isObject(specifyUser)) {
            property.assigneeType = taskAssigneeInfo.type;
            // 指定多人員
            property.specifyUser = {
                deptCode: specifyUser['dept_code'],
                userCode: specifyUser['user_code'],
            };
            return true;
        }
        if (_.isString(specifyUser)) {
            // 關卡多人員(multi-instance產生)，form value user_str[]
            const userStr = specifyUser;
            const { user, dept } = await this.getUserInfo(userStr);
            property.assigneeType = taskAssigneeInfo.type;
            property.specifyUser = {
                deptCode: dept?.code ?? '',
                userCode: user?.code ?? '',
            };
            return true;
        }
        return false;
    }

    private async genPropertyStep(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        variables: Variables,
        formTaskAssignees: FormTaskAssignees,
        property: FormTaskAssigneeProperty = {},
    ) {
        const key = taskAssigneeInfo.key;
        if (!key) return property;
        const stepTaskAssigneeInfo = formTaskAssignees[key];
        if (!stepTaskAssigneeInfo) return property;
        for (const stepTaskAssigneeInfoItem of stepTaskAssigneeInfo) {
            if (!stepTaskAssigneeInfoItem.type) continue;
            property = await this.getProperty[stepTaskAssigneeInfoItem.type](
                stepTaskAssigneeInfoItem,
                formValue,
                variables,
                formTaskAssignees,
            );
        }
        return property;
    }

    private async genPropertyBySpecifyUser(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        variables: Variables,
        property: FormTaskAssigneeProperty = {},
    ) {
        const runSpecifyUser = await this._getSpecifyUserByVariableSpecifyUser(
            taskAssigneeInfo,
            variables,
            property,
        );
        if (runSpecifyUser) return property;
        // 單一人員
        const users = this.taskAssigneeSvc.getFormTaskSpecifyUsers(
            taskAssigneeInfo,
            formValue,
        );
        if (users.length > 0) {
            property.assigneeType = taskAssigneeInfo.type;
            property.specifyUser = {
                deptCode: users[0].dept_code,
                userCode: users[0].user_code,
            };
        }
        return property;
    }

    private async _getSpecifyUserByVariableUser(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        variables: Variables,
        property: FormTaskAssigneeProperty,
    ) {
        const user = getNestedVariableValue(
            variables,
            EnumElementVariableName.User,
        );
        if (!user) return false;
        // 關卡多人員(multi-instance產生)
        const userData = await this.userSvc.findOne(user.id);
        const deptData = await this.departmentSvc.findOne(
            user[EnumUserVariableName.DepartmentId],
        );
        property.assigneeType = taskAssigneeInfo.type;
        property.specifyUser = {
            companyId: deptData?.companyId ?? 1,
            deptCode: deptData?.code ?? '',
            userCode: userData?.code ?? '',
        };
        property.assignee = {
            companyId: deptData?.companyId,
            departmentId: deptData?.id,
            departmentCode: deptData?.code,
            userId: userData?.id,
            userCode: userData?.code,
        };
        return true;
    }

    private async _getSpecifyUserByVariableKey(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        variables: Variables,
        property: FormTaskAssigneeProperty,
    ) {
        const data = await this._getUserDeptByVariableKey(
            taskAssigneeInfo,
            variables,
        );
        if (!data) return false;
        const { user, dept } = data;
        property.assigneeType = taskAssigneeInfo.type;
        property.specifyUser = {
            companyId: dept?.companyId,
            deptCode: dept?.code ?? '',
            userCode: user?.code ?? '',
        };
        property.assignee = {
            companyId: dept?.companyId,
            departmentId: dept?.id,
            departmentCode: dept?.code,
            userId: user?.id,
            userCode: user?.code,
        };
        return true;
    }

    private async _getSpecifyUserByValueKey(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        property: FormTaskAssigneeProperty,
    ) {
        const data = await this._getUserDeptByValueKey(
            taskAssigneeInfo,
            formValue,
        );
        if (!data) return false;
        const { user, dept } = data;
        property.assigneeType = taskAssigneeInfo.type;
        property.specifyUser = {
            companyId: dept?.companyId,
            deptCode: dept?.code ?? '',
            userCode: user?.code ?? '',
        };
        property.assignee = {
            companyId: dept?.companyId,
            departmentId: dept?.id,
            departmentCode: dept?.code,
            userId: user?.id,
            userCode: user?.code,
        };
        return true;
    }

    private async _getUserDeptByValueKey(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValues: InstancesValuesData,
    ) {
        const { key } = taskAssigneeInfo;
        if (!key) return null;
        const dataValue = getNestedFormValue(formValues, key);
        if (!dataValue) return null;
        if (_.isString(dataValue)) {
            const { user, dept } = await this.getUserInfo(dataValue);
            return { user, dept };
        }
        if (_.has(dataValue, 'user_code') || _.has(dataValue, 'userCode')) {
            const user = await this.userSvc.findOneByCode(
                dataValue.user_code ?? dataValue.userCode,
            );
            const dept = await this.departmentSvc.findOneByParams({
                departmentCode: dataValue.dept_code ?? dataValue.deptCode,
            });
            return { user, dept };
        }
        if (_.has(dataValue, 'id')) {
            // 舊有 hardcode 的 form_data_supervisor 等 key ，預先被轉成 object
            const user = await this.userSvc.findOne(dataValue.id);
            const dept = await this.departmentSvc.findOne(
                dataValue[EnumUserVariableName.DepartmentId],
            );
            return { user, dept };
        }
        return null;
    }
    private async _getUserDeptByVariableKey(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        variables: Variables,
    ) {
        const { key } = taskAssigneeInfo;
        if (!key) return null;
        const dataValue = getNestedVariableValue(variables, key);
        if (!dataValue) return null;
        if (_.isString(dataValue)) {
            const { user, dept } = await this.getUserInfo(dataValue);
            return { user, dept };
        }
        if (_.has(dataValue, 'user_code') || _.has(dataValue, 'userCode')) {
            const user = await this.userSvc.findOneByCode(
                dataValue.user_code ?? dataValue.userCode,
            );
            const dept = await this.departmentSvc.findOneByParams({
                departmentCode: dataValue.dept_code ?? dataValue.deptCode,
            });
            return { user, dept };
        }
        if (_.has(dataValue, 'id')) {
            // 舊有 hardcode 的 form_data_supervisor 等 key ，預先被轉成 object
            const user = await this.userSvc.findOne(dataValue.id);
            const dept = await this.departmentSvc.findOne(
                dataValue[EnumUserVariableName.DepartmentId],
            );
            return { user, dept };
        }
        return null;
    }

    // 從表單指定人員
    private async genPropertyByPrefixFormSpecifyUser(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        variables: Variables,
        property: FormTaskAssigneeProperty = {},
    ) {
        const byKey = await this._getSpecifyUserByVariableKey(
            taskAssigneeInfo,
            variables,
            property,
        );
        if (byKey) return property;
        const byValueKey = await this._getSpecifyUserByValueKey(
            taskAssigneeInfo,
            formValue,
            property,
        );
        if (byValueKey) return property;
        const byUser = await this._getSpecifyUserByVariableUser(
            taskAssigneeInfo,
            variables,
            property,
        );
        if (byUser) return property;
        return property;
    }

    // form 人員的直屬主管
    private async genPropertyByPrefixFormDirectSupervisor(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        variables: Variables,
        property: FormTaskAssigneeProperty = {},
    ) {
        let data: {
            user: User | null | undefined;
            dept: Department | null | undefined;
        } | null = null;
        const dataByVariable = await this._getUserDeptByVariableKey(
            taskAssigneeInfo,
            variables,
        );
        if (dataByVariable) data = dataByVariable;
        const dataByValue = await this._getUserDeptByValueKey(
            taskAssigneeInfo,
            formValue,
        );
        if (dataByValue) data = dataByValue;
        if (!data) return property;
        const { user, dept } = data;
        const taskOwner: Owner = {
            companyId: dept?.companyId ?? 1,
            departmentId: dept?.id ?? 1,
            departmentCode: dept?.code ?? '',
            userId: user?.id ?? 1,
            userCode: user?.code ?? '',
        };
        const assignee =
            await this.taskAssigneeRepo.findOneOwnerDirectSupervisor(taskOwner);
        property.assigneeType = taskAssigneeInfo.type;
        property.specifyUser = {
            companyId: assignee?.companyId,
            deptCode: assignee?.departmentCode ?? '',
            userCode: assignee?.userCode ?? '',
        };
        property.assignee = assignee;
        return property;
    }

    // form 人員的部門主管
    private async genPropertyByPrefixFormDepartmentHead(
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        variables: Variables,
        property: FormTaskAssigneeProperty = {},
    ) {
        let data: {
            user: User | null | undefined;
            dept: Department | null | undefined;
        } | null = null;
        const dataByVariable = await this._getUserDeptByVariableKey(
            taskAssigneeInfo,
            variables,
        );
        if (dataByVariable) data = dataByVariable;
        const dataByValue = await this._getUserDeptByValueKey(
            taskAssigneeInfo,
            formValue,
        );
        if (dataByValue) data = dataByValue;
        if (!data) return property;
        const { user, dept } = data;
        const assignee =
            await this.taskAssigneeRepo.findOneDepartmentSupervisor({
                companyId: dept?.companyId ?? -1,
                departmentCode: dept?.code || '-',
            });
        property.assigneeType = taskAssigneeInfo.type;
        property.specifyUser = {
            companyId: assignee?.companyId,
            deptCode: assignee?.departmentCode ?? '',
            userCode: assignee?.userCode ?? '',
        };
        property.assignee = assignee;
        return property;
    }

    private async getUserInfo(value: string) {
        const userStr = await this.getUserInfoByUserStr(value);
        if (userStr.user) return userStr;
        return this.getUserInfoByUserCode(value);
    }

    private async getUserInfoByUserCode(value: string) {
        const userCode = value.split(',')[0];
        const result: {
            user?: User | null;
            dept?: Department | null;
            company?: Company | null;
        } = {
            user: null,
            dept: null,
            company: null,
        };
        if (_.isNumber(userCode)) return result;
        const { rows } = await this.userPositionSvc.search({
            userCode,
            disabled: false,
        });
        if (rows.length == 0) return result;
        result.user = rows[0].user;
        result.dept = rows[0].department;
        result.company = rows[0].department.company;
        return result;
    }

    private async getUserInfoByUserStr(userStr: string) {
        const countersigner = VariablesHelper.genUsers(
            userStr,
        ) as CountersignerVariable;
        const result: {
            user?: User | null;
            dept?: Department | null;
            company?: Company | null;
        } = {
            user: null,
            dept: null,
            company: null,
        };
        const user = await this.userSvc.findOne(countersigner.id);
        if (!user) return result;
        result.user = user;
        const dept = await this.departmentSvc.findOne(
            countersigner.department_id,
        );
        result.dept = dept;
        const company = await this.companySvc.findOne(countersigner.company_id);
        result.company = company;
        return result;
    }

    private async getUserInfoByVariableSpecifyUser(
        formSpecifyUser: FormSpecifyUser,
    ) {
        const data = await this.userPositionSvc.search({
            userCode: formSpecifyUser.user_code,
            disabled: false,
        });
        const dept =
            formSpecifyUser.dept_code == ''
                ? data.rows[0].department
                : await this.departmentSvc.findOneByParams({
                      departmentCode: formSpecifyUser.dept_code,
                  });
        return {
            user: data.rows[0].user,
            dept,
        };
    }

    private getInstanceFormValue(formInstance: FormInstance) {
        const formValue = formInstance.values ?? {};
        return formValue;
    }
}
