import { Inject, Service } from 'typedi';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import {
    FormSpecifyUser,
    TaskProperty,
} from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { Variables } from '../../types/processInstance.type';
import { FormTaskAssigneeUseCase } from './form.taskAssignee.ucase';
import { GetFormTaskAssigneesFn } from '@/modules/form/formConf/types/formConfTaskAssignees.types';
import { FormConfTaskAssigneesUseCase } from '@/modules/form/formConf/providers/formConfTaskAssignees.usecase';
import { StepTaskAssigneeService } from './step.taskAssignee.service';
import { TaskAssigneeService } from './taskAssignee.service';
import { TaskAssigneesFilterDataDTO } from '@/modules/form/formConf/types/taskAssigneesFilterData.dto';

@Service()
export class TaskAssigneeUseCase {
    @Inject()
    private formTaskAssigneeUCase: FormTaskAssigneeUseCase;
    @Inject()
    private formConfTaskAssigneesUCase: FormConfTaskAssigneesUseCase;
    @Inject()
    private taskAssigneeSvc: TaskAssigneeService;
    @Inject()
    private stepTaskAssigneeSvc: StepTaskAssigneeService;

    async getTaskAssignee(
        getFormTaskAssigneesFn: GetFormTaskAssigneesFn,
        processTaskId: string,
        formInstance: FormInstance,
        variables: Variables,
        property: TaskProperty,
    ) {
        const formTaskAssignee =
            await this.formTaskAssigneeUCase.getTaskAssignee(
                getFormTaskAssigneesFn,
                processTaskId,
                formInstance,
                variables,
                property,
            );
        return formTaskAssignee;
    }

    async getPreviewFormInstanceTaskAssignees(formInstance: FormInstance) {
        const formValues = (formInstance.values ?? {}) as JSON;
        const filterData = new TaskAssigneesFilterDataDTO(
            formValues,
            formInstance,
        );
        const getFormTaskAssigneesFn =
            await this.formConfTaskAssigneesUCase.getGetFormTaskAssigneesFn(
                formInstance.formId,
                formInstance.formVersion,
            );
        const formTaskAssignees = getFormTaskAssigneesFn(filterData);

        const formSpecifyUserMap = this.taskAssigneeSvc.genFormSpecifyUserMap(
            formTaskAssignees,
            formValues,
        );

        const formValueSpecifyUserMap =
            this.taskAssigneeSvc.genFormValueSpecifyUsersMap(
                formTaskAssignees,
                formValues,
            );

        const specifyUserMap = Array.from(
            Object.keys(formTaskAssignees).sort(),
        ).reduce((pre, cur) => {
            const formSpecifyUsers = formSpecifyUserMap.get(cur) ?? [];
            const formValueSpecifyUsers =
                formValueSpecifyUserMap.get(cur) ?? [];
            pre.set(cur, [...formSpecifyUsers, ...formValueSpecifyUsers]);
            return pre;
        }, new Map<string, (FormSpecifyUser | string)[]>());

        const baseStepTaskAssignees =
            this.stepTaskAssigneeSvc.genBaseStepTaskAssigneesMap(
                formTaskAssignees,
                formValues,
            );

        return {
            byStepTaskAssignees: {
                base: baseStepTaskAssignees,
            },
            byFormTaskAssignees: {
                full: specifyUserMap,
                formSpecifyUsers: formSpecifyUserMap,
                formValueSpecifyUsers: formValueSpecifyUserMap,
            },
            formTaskAssignees,
            filterData,
        };
    }
}
