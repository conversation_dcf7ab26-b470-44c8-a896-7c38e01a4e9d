import { Department } from '@/common/models/clinico/core/department.model';
import { DepartmentService } from '@/modules/organization/providers/department.service';
import { UserService } from '@/modules/organization/providers/user.service';
import { UserPositionService } from '@/modules/organization/providers/userPosition.service';
import { Inject, Service } from 'typedi';
import {
    Assignee,
    SearchDepartmentSupervisorParams,
} from '@/modules/assignee/types/assignee.type';

import { Owner } from './taskAssignee.type';

@Service()
export class TaskAssigneeRepository {
    @Inject()
    private departmentService: DepartmentService;
    @Inject()
    private userPositionService: UserPositionService;
    @Inject()
    private userService: UserService;

    async findOnePositionUser(params: {
        companyId?: number;
        departmentId?: number;
        userId?: number;
        departmentCode?: string;
        userCode?: string;
        isMain?: boolean;
    }): Promise<Assignee | null> {
        let assignee: Assignee | null = null;
        const result = await this.userPositionService.search({
            ...params,
        });
        const userPosition = result.rows.length ? result.rows[0] : null;
        if (userPosition) {
            assignee = {
                companyId: userPosition.department.companyId,
                departmentId: userPosition.departmentId,
                departmentCode: userPosition.department.code,
                userId: userPosition.userId,
                userCode: userPosition.userCode,
            };
        }
        return assignee;
    }

    async findOneSpecifyUser(params: {
        companyId?: number;
        departmentId?: number;
        userId?: number;
        departmentCode?: string;
        userCode?: string;
    }): Promise<Assignee | null> {
        let assignee: Assignee | null = null;
        const dept = await this.departmentService.findOneByParams({
            companyId: params.companyId,
            departmentId: params.departmentId,
            departmentCode: params.departmentCode,
        });
        const result = await this.userService.search({
            id: params.userId,
            code: params.userCode,
        });
        const user = result.rows.length ? result.rows[0] : null;
        if (user) {
            assignee = {
                companyId: dept?.companyId,
                departmentId: dept?.id,
                departmentCode: dept?.code,
                userId: user.id,
                userCode: user.code,
            };
        }
        return assignee;
    }

    async findOneDepartmentSupervisor(
        params: SearchDepartmentSupervisorParams,
    ): Promise<Assignee | null> {
        const dept = await this.departmentService.findOneByParams({
            ...params,
        });
        let assignee: Assignee | null = null;
        if (dept) {
            assignee = {
                companyId: dept.companyId,
                companyName: dept.company?.name,
                departmentId: dept.id,
                departmentCode: dept.code,
                departmentName: dept.name,
                userId: dept.supervisorId,
                userCode: dept.supervisor?.code,
                userName: dept.supervisor?.name,
                parentDepartmentId: dept.parentId,
            };
        }
        return assignee;
    }

    // 起單人直屬主管
    async findOneOwnerDirectSupervisor(owner: Owner): Promise<Assignee | null> {
        let dept: Department | null = null;
        let companyId: number = owner.companyId;
        let departmentId: number = owner.departmentId;
        do {
            dept = await this.departmentService.findOneByParams({
                companyId,
                departmentId,
            });
            if (dept) {
                companyId = dept.companyId;
                departmentId = dept.parentId;
            }
        } while (dept && dept.supervisorId == owner.userId);

        let assignee: Assignee | null = null;
        if (dept) {
            assignee = {
                companyId: dept.companyId,
                companyName: dept.company?.name,
                departmentId: dept.id,
                departmentCode: dept.code,
                departmentName: dept.name,
                userId: dept.supervisorId,
                userCode: dept.supervisor?.code,
                userName: dept.supervisor?.name,
                parentDepartmentId: dept.parentId,
            };
        }
        return assignee;
    }
}
