import { Inject, Service } from 'typedi';
import {
    EnumTaskWebhookEvent,
    LogSendWebhook,
    TaskWebhookBodyParams,
    TaskWebhookResponseData,
    WebhookTaskProcessResult,
} from '../../types/taskWebhook.type';
import _ from 'lodash';
import { TaskWebhookService } from './taskWebhook.service';
import { FormInstanceRepository } from '@/modules/form/formInstance/providers/formInstance.repository';
import { FormRepository } from '@/modules/form/form/providers/form.repository';
import { InstancesValuesData } from '@/modules/form/formInstance/types/formInstance.type';
import { EnumTaskStatus } from '@/modules/form/formInstanceTask/types/formInstanceTask.type';
import { FormInstanceTaskRepository } from '@/modules/form/formInstanceTask/providers/formInstanceTask.repository';
import { HookService } from '@/common/services/hook.service';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import LoggerUtil from '@/common/utils/logger.util';

@Service()
export class TaskWebhookUseCase {
    @Inject()
    private taskWebhookSvc: TaskWebhookService;
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private formInstanceTaskRepo: FormInstanceTaskRepository;
    @Inject()
    private formRepo: FormRepository;
    @Inject()
    private hookSvc: HookService;

    async webhookTaskProcess(
        taskId: number,
        status: EnumTaskStatus,
        taskEvent: EnumTaskWebhookEvent,
        paramsValues?: InstancesValuesData,
    ): Promise<WebhookTaskProcessResult> {
        const updatedValues: InstancesValuesData = paramsValues ?? {};
        const paramsAppendFormData = updatedValues.appendFormData ?? null;
        const result: WebhookTaskProcessResult = {
            paramsAppendFormData,
            webhookValues: {},
            values: paramsValues ?? {},
        };
        const taskStatus = this.taskWebhookSvc.convertTaskStatus(status);
        if (!taskStatus) return result;
        const current = await this.formInstanceTaskRepo.findOneOrError(taskId);
        const { formInstanceId, processTaskId } = current;
        const formInstance =
            await this.formInstanceRepo.findOneOrError(formInstanceId);
        const { formId, values } = formInstance;
        const form = await this.formRepo.findOneOrError(formId);
        const { formConfTaskWebhooks, processKey } = form;
        if (!formConfTaskWebhooks) return result;
        const hookUrls = this.taskWebhookSvc.getTaskHookUrls(
            processTaskId,
            taskStatus,
            taskEvent,
            formConfTaskWebhooks,
        );
        if (!hookUrls.length) return result;
        const results = await Promise.all(
            hookUrls.map(async (hookUrl) => {
                const bodyParams: TaskWebhookBodyParams = {
                    formInstanceId: Number(formInstanceId),
                    processKey,
                    taskStatus,
                    taskEvent,
                    values: values ?? {},
                    formId: Number(formId),
                    formName: form.name,
                    taskStep: processTaskId,
                    taskName: current.name,
                    TaskId: Number(taskId),
                    appendFormData: paramsAppendFormData,
                };
                return this.sendWebhook(hookUrl, bodyParams);
            }),
        );

        const webhookValues = _.merge({}, ...results);
        const finalValues = _.merge({}, updatedValues, webhookValues, {
            webhookValues,
            paramsAppendFormData,
        });
        console.log('Final values after merging:', finalValues);
        return {
            paramsAppendFormData,
            webhookValues,
            values: finalValues,
        };
    }

    private async sendWebhook(
        hookUrl: string,
        bodyParams: TaskWebhookBodyParams,
    ) {
        const logInfo: LogSendWebhook = {
            hookUrl,
            bodyParams,
            status: 0,
            data: undefined,
        };
        try {
            const response = await this.hookSvc.taskProcess(
                hookUrl,
                bodyParams,
            );
            console.log('Webhook sent successfully:', response.data);
            logInfo.status = response.status;
            logInfo.data = response.data;
            LoggerUtil.info(JSON.stringify(logInfo));
            return response.data as TaskWebhookResponseData;
        } catch (error) {
            console.error('Error sending webhook:', error);
            logInfo.status = error?.response?.status ?? 0;
            const resData = error?.response?.data ?? {};
            const errorMessage = resData.errorMessage ?? 'Task hook error';
            logInfo.error = error;
            logInfo.errorMessage = errorMessage;
            logInfo.data = resData;
            LoggerUtil.warn(JSON.stringify(logInfo));
            if (bodyParams.taskEvent === EnumTaskWebhookEvent.Complete) {
                return {};
            }
            throw new BaseError(errorMessage, httpStatus.BAD_GATEWAY);
        }
    }
}
