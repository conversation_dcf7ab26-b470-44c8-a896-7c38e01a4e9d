import { Service } from 'typedi';
import {
    EnumTaskWebhookEvent,
    EnumTaskWebhookTaskStatusStr,
    FormConfTaskWebhookParams,
    TaskWebhookNestData,
} from '../../types/taskWebhook.type';
import _ from 'lodash';
import { EnumTaskStatus } from '@/modules/form/formInstanceTask/types/formInstanceTask.type';

@Service()
export class TaskWebhookService {
    convertTaskStatus(status: EnumTaskStatus) {
        switch (status) {
            case EnumTaskStatus.Approved:
                return EnumTaskWebhookTaskStatusStr.Approved;
            case EnumTaskStatus.Rejected:
                return EnumTaskWebhookTaskStatusStr.Rejected;
            case EnumTaskStatus.Return:
                return EnumTaskWebhookTaskStatusStr.Return;
            default:
                return null;
        }
    }
    /**
     * data[configStep][taskStatus][event] = [<hookUrl>]
     * @param configs
     * @returns
     */
    genTaskWebhookNestData(
        configs: FormConfTaskWebhookParams[],
    ): TaskWebhookNestData {
        const data = configs.reduce(
            (acc, config) => {
                const { step: configStep, taskStatus, taskEvent } = config;
                if (!acc[configStep]) {
                    acc[configStep] = {};
                }
                if (!acc[configStep][taskStatus]) {
                    acc[configStep][taskStatus] = {};
                }
                if (!acc[configStep][taskStatus][taskEvent]) {
                    acc[configStep][taskStatus][taskEvent] = [];
                }
                acc[configStep][taskStatus][taskEvent].push(config.hookUrl);
                return acc;
            },
            <TaskWebhookNestData>{},
        );
        return data;
    }

    getTaskHookUrls(
        step: string,
        taskStatus: EnumTaskWebhookTaskStatusStr,
        event: EnumTaskWebhookEvent,
        configs: FormConfTaskWebhookParams[],
    ) {
        const data = this.genTaskWebhookNestData(configs);
        const hookUrls = data[step]?.[taskStatus]?.[event];
        if (!hookUrls || !_.isArray(hookUrls)) {
            return [];
        }
        return hookUrls as string[];
    }
}
