import { InstancesValuesData } from '@/modules/form/formInstance/types/formInstance.type';
import { ValuesAppendFormData } from '@/modules/form/formInstance/types/formValues.type';
import {
    IsEnum,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
} from 'class-validator';
import { JSONSchema } from 'class-validator-jsonschema';

export type LogSendWebhook = {
    hookUrl: string;
    bodyParams: TaskWebhookBodyParams;
    status: number;
    data: any;
    error?: string;
    errorMessage?: string;
};
export type WebhookTaskProcessResult = {
    paramsAppendFormData: ValuesAppendFormData;
    webhookValues: any;
    values: InstancesValuesData;
};

export enum EnumTaskWebhookTaskStatusStr {
    Approved = 'approved',
    Rejected = 'rejected',
    Return = 'return', //退回指定task
}

export enum EnumTaskWebhookEvent {
    Process = 'process', // default, 關卡狀態異動的起始，未產生下一關
    Complete = 'complete', // 關卡異動結束，已產生下一關
}

/**
 * acc[configStep][taskStatus][status] = [<hookUrl>]
 */
export type TaskWebhookNestData = {
    [key: string]: TaskWebhookNestData | string[];
};

export type FormConfTaskWebhookParams = {
    formId: number;
    step: string;
    taskEvent: EnumTaskWebhookEvent;
    taskStatus: EnumTaskWebhookTaskStatusStr;
    hookUrl: string;
};

export class TaskWebhookResponseData {
    @IsObject()
    @IsOptional()
    @JSONSchema({
        description:
            '有要更新的 formInstance.values 內容回傳，object 的 key 全部回傳',
        example: getCnSampleAppendFormData(),
    })
    appendFormData?: ValuesAppendFormData;

    @IsOptional()
    @IsString()
    @JSONSchema({
        description: '有要回傳的錯誤訊息',
        example: '請選擇負責人',
    })
    errorMessage?: string;
}

export class TaskWebhookBodyParams {
    @IsObject()
    @JSONSchema({
        description:
            'formInstance.values 內容全部回傳，中國的 values 會全塞進來有點肥，用這個當範例',
    })
    values: InstancesValuesData;

    @IsNumber()
    formId: number;

    @IsString()
    processKey: string;

    @IsString()
    formName: string;

    @IsString()
    taskStep: string;

    @IsString()
    taskName: string;

    @IsNumber()
    formInstanceId: number;

    @IsNumber()
    @IsOptional()
    TaskId: number;

    @IsEnum(EnumTaskWebhookTaskStatusStr)
    taskStatus: EnumTaskWebhookTaskStatusStr;

    @IsEnum(EnumTaskWebhookEvent)
    taskEvent: EnumTaskWebhookEvent;

    @IsObject()
    @IsOptional()
    @JSONSchema({
        description: '前端傳的 appendFormData 回傳',
    })
    appendFormData: ValuesAppendFormData;
}

function getCnSampleAppendFormData() {
    return {
        bpmOptions: {
            initiator: [
                {
                    deptCode: 'A-2-1',
                    userCode: 'C1236',
                },
            ],
            cooManager: [
                {
                    deptCode: 'V',
                    userCode: 'E0001-1',
                },
            ],
            officeManager: [
                {
                    deptCode: 'A-2-1',
                    userCode: 'C0077',
                },
            ],
            generalManager: [
                {
                    deptCode: 'V',
                    userCode: 'E0001-1',
                },
            ],
            businessManager: [
                {
                    deptCode: 'A',
                    userCode: 'L1584-1',
                },
            ],
        },
    };
}
function getCnSampleValue() {
    return {
        _: {
            Activity_Step1: {
                assignees: [
                    {
                        deptCode: 'A-2-1',
                        userCode: 'C1236',
                        companyId: 37,
                    },
                ],
                assignee_1: {
                    deptCode: 'A-2-1',
                    userCode: 'C1236',
                    companyId: 37,
                },
            },
            Activity_Step2: {
                assignees: [
                    {
                        deptCode: 'A-2-1',
                        userCode: 'C0077',
                        companyId: 37,
                    },
                ],
                assignee_1: {
                    deptCode: 'A-2-1',
                    userCode: 'C0077',
                    companyId: 37,
                },
            },
            Activity_Step3: {
                assignees: [
                    {
                        deptCode: 'A',
                        userCode: 'L1584-1',
                        companyId: 37,
                    },
                ],
                assignee_1: {
                    deptCode: 'A',
                    userCode: 'L1584-1',
                    companyId: 37,
                },
            },
            Activity_Step4: {
                assignees: [
                    {
                        deptCode: 'V',
                        userCode: 'E0001-1',
                        companyId: 37,
                    },
                ],
                assignee_1: {
                    deptCode: 'V',
                    userCode: 'E0001-1',
                    companyId: 37,
                },
            },
            Activity_Step5: {
                assignees: [
                    {
                        deptCode: 'V',
                        userCode: 'E0001-1',
                        companyId: 37,
                    },
                ],
                assignee_1: {
                    deptCode: 'V',
                    userCode: 'E0001-1',
                    companyId: 37,
                },
            },
            Activity_Step6: {
                assignees: [
                    {
                        deptCode: 'COO',
                        userCode: 'L1901-1',
                        companyId: 37,
                    },
                ],
                assignee_1: {
                    deptCode: 'COO',
                    userCode: 'L1901-1',
                    companyId: 37,
                },
            },
        },
        data: {
            id: '252',
            city: null,
            code: 'CN-EQO0000219',
            dept: {
                id: '818',
                code: 'A-2-1',
                name: '营运支持部-供应链管理处-进口组',
                company: {
                    id: '32',
                    name: '科林医疗仪器',
                    __typename: 'Company',
                },
                __typename: 'Department',
                costCenter: null,
            },
            user: {
                id: '1526',
                code: 'C1236',
                name: '程苗',
                __typename: 'User',
            },
            bpmUrl: null,
            region: {
                id: '2',
                name: '大陆',
                __typename: 'Region',
                localCurrency: {
                    id: '2',
                    name: '人民幣',
                    __typename: 'Currency',
                },
            },
            status: 'Processing',
            userId: '1526',
            address: '上海市宝山区富联一路98号4号楼仓库',
            taxRate: 0.13,
            currency: {
                id: '2',
                code: 'CNY',
                name: '人民幣',
                __typename: 'Currency',
            },
            customer: {
                id: '20049',
                code: 'A00000',
                name: 'stock-eye',
                __typename: 'Customer',
            },
            district: null,
            province: {
                id: '14',
                name: '上海市',
                cities: [
                    {
                        id: '96',
                        name: '上海市',
                        districts: [
                            {
                                id: '1198',
                                name: '崇明县',
                                __typename: 'District',
                            },
                            {
                                id: '1197',
                                name: '奉贤区',
                                __typename: 'District',
                            },
                            {
                                id: '1196',
                                name: '南汇区',
                                __typename: 'District',
                            },
                            {
                                id: '1195',
                                name: '青浦区',
                                __typename: 'District',
                            },
                            {
                                id: '1194',
                                name: '松江区',
                                __typename: 'District',
                            },
                            {
                                id: '1193',
                                name: '金山区',
                                __typename: 'District',
                            },
                            {
                                id: '1192',
                                name: '浦东新区',
                                __typename: 'District',
                            },
                            {
                                id: '1191',
                                name: '嘉定区',
                                __typename: 'District',
                            },
                            {
                                id: '1190',
                                name: '宝山区',
                                __typename: 'District',
                            },
                            {
                                id: '1189',
                                name: '闵行区',
                                __typename: 'District',
                            },
                            {
                                id: '1188',
                                name: '杨浦区',
                                __typename: 'District',
                            },
                            {
                                id: '1187',
                                name: '虹口区',
                                __typename: 'District',
                            },
                            {
                                id: '1186',
                                name: '闸北区',
                                __typename: 'District',
                            },
                            {
                                id: '1185',
                                name: '普陀区',
                                __typename: 'District',
                            },
                            {
                                id: '1184',
                                name: '静安区',
                                __typename: 'District',
                            },
                            {
                                id: '1183',
                                name: '长宁区',
                                __typename: 'District',
                            },
                            {
                                id: '1182',
                                name: '徐汇区',
                                __typename: 'District',
                            },
                            {
                                id: '1181',
                                name: '卢湾区',
                                __typename: 'District',
                            },
                            {
                                id: '1180',
                                name: '黄浦区',
                                __typename: 'District',
                            },
                            {
                                id: '1179',
                                name: '上海市',
                                __typename: 'District',
                            },
                        ],
                        __typename: 'City',
                    },
                ],
                __typename: 'Province',
            },
            regionId: '2',
            createdAt: '2025-03-19T15:15:16.259Z',
            orderCode: null,
            __typename: 'EyeQuotationOrder',
            businessId: null,
            costCenter: {
                id: '78',
                code: 'M-20-1-1',
                name: '手术设备事业部-销售东区组',
                __typename: 'CostCenter',
            },
            currencyId: '2',
            customerId: '20049',
            realAmount: 8756972,
            createdUser: {
                id: '4',
                code: 'L1500',
                name: '熊竣凱',
                __typename: 'User',
            },
            description: '台湾科明要心灯器械，大陆代为采购，后续带回台北使用',
            grossMargin: 1,
            paymentInfo: null,
            biddingPrice: 0,
            contactPhone: '***********',
            creditPeriod: {
                id: '16',
                name: '0D',
                __typename: 'CreditPeriod',
            },
            discountRate: 1,
            exchangeRate: null,
            bpmInstanceId: null,
            contactPerson: ' 程苗',
            onlyMaterials: false,
            untaxedAmount: 7749532.74,
            commissionRate: 0,
            discountAmount: 0,
            standardAmount: 8756972,
            warrantyMonths: null,
            eyeServiceOrder: null,
            warrantyBuyType: 'NotBuy',
            commissionAmount: 0,
            financialCompany: null,
            realDiscountRate: 30.9,
            expectPaymentDate: '2025-04-02T00:00:00.000Z',
            eyeServiceOrderId: null,
            invoicingCustomer: {
                id: '20049',
                code: 'A00000',
                name: 'stock-eye',
                __typename: 'Customer',
            },
            recommendedAmount: 283429,
            expectDeliveryDate: '2025-04-02T00:00:00.000Z',
            officialSealBpmUrl: null,
            officialSealStatus: null,
            realDiscountAmount: 0,
            extraDiscountAmount: 0,
            invoicingCustomerId: '20049',
            localStandardAmount: 8756972,
            warrantyPeriodTypeId: null,
            eyeQuotationOrderType: {
                id: '7',
                code: 'Requisition',
                name: '国内请购',
                __typename: 'EyeQuotationOrderType',
            },
            eyeWarrantyPeriodType: null,
            eyeQuotationOrderProducts: [],
            eyeQuotationOrderBusinesses: [],
            eyeQuotationOrderPromotions: [],
            discountRateWithoutCommission: 1,
            eyeQuotationOrderProductItems: [
                {
                    id: '1382',
                    qty: 14,
                    material: {
                        type: {
                            id: '104',
                            code: '2',
                            name: '器械',
                            __typename: 'MaterialType',
                        },
                        images: [],
                        costPrice: 3750,
                        __typename: 'Material',
                        costCurrency: {
                            id: '2',
                            code: 'CNY',
                            name: '人民幣',
                            __typename: 'Currency',
                        },
                        optionSalePrice: 11959,
                        minimumOrderQuantity: 1,
                    },
                    unitPrice: 10583.19,
                    __typename: 'EyeQuotationOrderProductItem',
                    materialId: '5147',
                    materialCode: 'LS-XD-BG380T',
                    materialName: '膜瓣镊',
                    materialSpec: '膜瓣镊',
                    materialUnit: '个',
                    unitPriceVat: 11959,
                    materialModel: 'XD-BG380T',
                    warrantyMonths: null,
                    materialCostPrice: 3750,
                    customQuotationPrice: 11959,
                    materialCostCurrency: {
                        id: '2',
                        code: 'CNY',
                        name: '人民幣',
                        __typename: 'Currency',
                    },
                    materialSellingPrice: 11959,
                    warrantyPeriodTypeId: null,
                    eyeWarrantyPeriodType: null,
                    discountedSellingPrice: 0,
                    materialSellingCurrencyId: '2',
                    eyeQuotationOrderProductId: null,
                    eyeQuotationOrderPromotionId: null,
                },
                {
                    id: '1381',
                    qty: 9,
                    material: {
                        type: {
                            id: '104',
                            code: '2',
                            name: '器械',
                            __typename: 'MaterialType',
                        },
                        images: [],
                        costPrice: 1875,
                        __typename: 'Material',
                        costCurrency: {
                            id: '2',
                            code: 'CNY',
                            name: '人民幣',
                            __typename: 'Currency',
                        },
                        optionSalePrice: 5979,
                        minimumOrderQuantity: 1,
                    },
                    unitPrice: 5291.15,
                    __typename: 'EyeQuotationOrderProductItem',
                    materialId: '5149',
                    materialCode: 'LS-XD-BG476T',
                    materialName: '眼内膜铲',
                    materialSpec: '眼内膜铲',
                    materialUnit: '个',
                    unitPriceVat: 5979,
                    materialModel: 'XD-BG476T',
                    warrantyMonths: null,
                    materialCostPrice: 1875,
                    customQuotationPrice: 5979,
                    materialCostCurrency: {
                        id: '2',
                        code: 'CNY',
                        name: '人民幣',
                        __typename: 'Currency',
                    },
                    materialSellingPrice: 5979,
                    warrantyPeriodTypeId: null,
                    eyeWarrantyPeriodType: null,
                    discountedSellingPrice: 0,
                    materialSellingCurrencyId: '2',
                    eyeQuotationOrderProductId: null,
                    eyeQuotationOrderPromotionId: null,
                },
                {
                    id: '1380',
                    qty: 13,
                    material: {
                        type: {
                            id: '104',
                            code: '2',
                            name: '器械',
                            __typename: 'MaterialType',
                        },
                        images: [],
                        costPrice: 1500,
                        __typename: 'Material',
                        costCurrency: {
                            id: '2',
                            code: 'CNY',
                            name: '人民幣',
                            __typename: 'Currency',
                        },
                        optionSalePrice: 4784,
                        minimumOrderQuantity: 1,
                    },
                    unitPrice: 4233.63,
                    __typename: 'EyeQuotationOrderProductItem',
                    materialId: '5148',
                    materialCode: 'LS-XD-BG472T',
                    materialName: '眼内膜铲',
                    materialSpec: '眼内膜铲',
                    materialUnit: '个',
                    unitPriceVat: 4784,
                    materialModel: 'XD-BG472T',
                    warrantyMonths: null,
                    materialCostPrice: 1500,
                    customQuotationPrice: 4784,
                    materialCostCurrency: {
                        id: '2',
                        code: 'CNY',
                        name: '人民幣',
                        __typename: 'Currency',
                    },
                    materialSellingPrice: 4784,
                    warrantyPeriodTypeId: null,
                    eyeWarrantyPeriodType: null,
                    discountedSellingPrice: 0,
                    materialSellingCurrencyId: '2',
                    eyeQuotationOrderProductId: null,
                    eyeQuotationOrderPromotionId: null,
                },
            ],
            eyeQuotationOrderWarrantyItems: [],
            eyeQuotationOrderCommissionType: {
                id: '2',
                name: '专项',
                __typename: 'EyeQuotationOrderCommissionType',
            },
            eyeQuotationOrderCommissionAmounts: [
                {
                    __typename: 'EyeQuotationOrderCommissionAmount',
                    commissionAmount: 0,
                    eyeQuotationOrderCommissionType: {
                        id: '2',
                        name: '专项',
                        __typename: 'EyeQuotationOrderCommissionType',
                    },
                },
            ],
            eyeQuotationOrderProductItemWarrantyPrices: [],
        },
        isCEO: true,
        isCOO: false,
        editor: {
            commissionAmount: 0,
        },
        bpmOptions: {
            initiator: [
                {
                    deptCode: 'A-2-1',
                    userCode: 'C1236',
                },
            ],
            cooManager: [
                {
                    deptCode: 'V',
                    userCode: 'E0001-1',
                },
            ],
            officeManager: [
                {
                    deptCode: 'A-2-1',
                    userCode: 'C0077',
                },
            ],
            generalManager: [
                {
                    deptCode: 'V',
                    userCode: 'E0001-1',
                },
            ],
            businessManager: [
                {
                    deptCode: 'A',
                    userCode: 'L1584-1',
                },
            ],
        },
        uploadFiles: [],
    };
}
