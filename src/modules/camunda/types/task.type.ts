import { Variable, Variables } from './processInstance.type';

export type Task = {
    id: string;
    name: string;
    assignee: string;
    owner: string;
    created: string; // The date the task was created on. Default format* yyyy-MM-dd'T'HH:mm:ss.SSSZ.
    due: string; // The task's due date. Default format* yyyy-MM-dd'T'HH:mm:ss.SSSZ.
    description: string;
    executionId: string;
    parentTaskId: string;
    processDefinitionId: string;
    processInstanceId: string;
    taskDefinitionKey: string;
    suspended: boolean;
};

export type TaskHistory = {
    id: string;
    processDefinitionKey: string;
    processDefinitionId: string;
    processInstanceId: string;
    activityInstanceId: string;
    name: string;
    description: string;
    deleteReason: string;
    owner: string;
    assignee: string;
    startTime: string; // yyyy-MM-dd'T'HH:mm:ss.SSSZ.
    endTime: string;
    duration: number; // The time the task took to finish (in milliseconds).
    taskDefinitionKey: string;
    priority: number;
    parentTaskId: string;
    removalTime: string; // yyyy-MM-dd'T'HH:mm:ss.SSSZ.
    rootProcessInstanceId: string;
};

export type SearchParams = {
    processInstanceId?: string;
    processInstanceIdIn?: string[];
    processInstanceBusinessKey?: string;
    processInstanceBusinessKeyIn?: string[];
    assignee?: string;
    assigneeIn?: string[];
    assigned?: boolean;
    unassigned?: boolean;
    taskVariables?: any[];
    processVariables?: any[];
};

export type SearchHistoryParams = {
    taskId?: string;
    processInstanceId?: string;
    processInstanceBusinessKey?: string;
    processInstanceBusinessKeyIn?: string[];
    processDefinitionId?: string;
    processDefinitionKey?: string;
    taskAssignee?: string;
    taskOwner?: string;
    assigned?: boolean;
    unassigned?: boolean;
    finished?: boolean;
    unfinished?: boolean;
    startedBefore?: Date;
    startedAfter?: Date;
    finishedBefore?: Date;
    finishedAfter?: Date;
    taskVariables?: any[];
    processVariables?: any[];
    sorting?: {
        sortBy:
            | 'taskId'
            | 'processDefinitionId'
            | 'processInstanceId'
            | 'endTime'
            | 'startTime'
            | 'assignee';
        sortOrder: 'asc' | 'desc';
    }[];
};

export type SetAssigneeParams = {
    id: string;
    assignee: string;
};

export type CompleteParams = {
    id: string;
    variables?: Variables;
};

export type UpdateVariableParams = Variable & {
    id: string;
    varName: string;
};
