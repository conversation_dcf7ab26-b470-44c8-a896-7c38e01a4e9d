export type Variable = {
    value: any;
    type?: string;
    valueInfo?: {
        objectTypeName?: string;
        serializationDataFormat?: string;
    };
};

export type Variables = {
    [key: string]: Variable;
};

export type ProcessInstance = {
    id: string;
    definitionId: string;
    businessKey: string;
    ended: boolean;
    suspended: boolean;
    variables?: any; // JSON object
};

export type DeletedResult = {
    processId: string;
    type: string;
    totalJobs: number;
    jobsCreated: number;
    batchJobsPerSeed: number;
    invocationsPerBatchJob: number;
    seedJobDefinitionId: string;
    monitorJobDefinitionId: string;
    batchJobDefinitionId: string;
    suspended: boolean;
    tenantId: string;
    createUserId: string;
};

export type StartParams = {
    key: string;
    businessKey: string;
    variables?: any; // JSON object
};

export type SearchParams = {
    businessKey?: string;
    processInstanceIds?: string[];
    processDefinitionKey?: string;
    processDefinitionKeyIn?: string[];
    variables?: any[];
};

export type DeleteParams = {
    processInstanceIds: string[];
    deleteReason: string;
};

export type UpdateParams = {
    processId: string;
    skipCustomListeners?: boolean;
    skipIoMappings?: boolean;
    instructions?: {
        type:
            | 'startBeforeActivity'
            | 'startAfterActivity'
            | 'startTransition'
            | 'cancel';
        // Can be used with instructions of types startBeforeActivity, startAfterActivity, and cancel. Specifies the activity the instruction targets
        activityId?: string;
        // Can be used with instructions of types startTransition. Specifies the sequence flow to start.
        transitionId?: string;
        // Can be used with instructions of type cancel.
        activityInstanceId?: string;
        variables?: Variables;
    }[];
    annotation?: string; // 備註
};

export type UpdateVariablesParams = {
    processId: string;
    modifications?: Variables;
    deletions?: string[];
};

export type UpdateVariableParams = Variable & {
    processId: string;
    varName: string;
};

export type ProcessActivityInstance = {
    id: string;
    activityId: string;
    activityType: string;
    processInstanceId: string;
    childActivityInstances: ProcessActivityInstance[];
    activityName: string;
    name: string;
};
