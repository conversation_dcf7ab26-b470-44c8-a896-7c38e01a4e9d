import {
    TaskWebhookBodyParams,
    TaskWebhookResponseData,
} from '@/modules/camunda/types/taskWebhook.type';
import { Body, JsonController, Post, Res } from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import { Service } from 'typedi';
import Koa from 'koa';

@JsonController('/sample')
@Service()
export class SampleController {
    @Post('/form/instance/task/webhook')
    @OpenAPI({
        description: '給外部使用的表單任務異動 Webhook 範例',
    })
    @ResponseSchema(TaskWebhookResponseData, {
        description: '成功',
        statusCode: 200,
    })
    async TaskWebhook(
        @Body() params: TaskWebhookBodyParams,
        @Res() res: Koa.Response,
    ): Promise<TaskWebhookResponseData> {
        return {
            appendFormData: {
                preparer: '30,4814,2607,科林,軟體研發處-後端開發組,莊馥蔚',
            },
            errorMessage: '測試 webhook 錯誤',
        };
    }
}
