import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import _ from 'lodash';
import { Service } from 'typedi';
import { GroupingTask, EnumTaskStatus } from '../types/formInstanceTask.type';
import { EnumTaskStatusStr } from '../../form/types/form.settings.type';

@Service()
export class FormInstanceTaskService {
    /**
     * 篩選要寄信通知的任務
     *
     * allowSkipNotifyMailUsers -> allowSkipNotifyMailTaskStatus 反向篩出不寄信
     *
     * - allowSkipNotifyMailUsers 允許跳過寄信的使用者
     * - allowSkipNotifyMailTaskStatus 允許跳過寄信的任務狀態， 預設 EnumTaskStatusStr.Approved
     *
     * ---
     * allowSkipNotifyMailUsers 人員沒有命中的話，篩選出來
     * ---
     * allowSkipNotifyMailUsers 人員有命中的話
     *  - allowSkipNotifyMailTaskStatus 有命中的就跳掉
     *
     * @param tasks
     * @param taskStatus
     * @returns
     */
    filterAllowMailNotifyTasks(
        tasks: FormInstanceTask[],
        taskStatus: EnumTaskStatusStr,
    ) {
        const data = tasks.filter((task) => {
            const allowSkipNotifyMailUsers =
                task.property?.allowSkipNotifyMailUsers ?? [];
            const allowSkipNotifyMailTaskStatus: EnumTaskStatusStr[] = task
                .property?.allowSkipNotifyMailTaskStatus ?? [
                EnumTaskStatusStr.Approved,
            ];
            const matchUser = allowSkipNotifyMailUsers
                .map((i) => i.id)
                .includes(task.userId ?? 0);
            if (!matchUser) return true;
            const matchTaskStatus =
                allowSkipNotifyMailTaskStatus.includes(taskStatus);
            return !matchTaskStatus;
        });
        return data;
    }

    groupingTasks(
        formInstanceTasks: FormInstanceTask[],
        previous?: boolean,
    ): GroupingTask[] {
        let taskGroup: GroupingTask[] = [];
        const tasks = _.orderBy(formInstanceTasks, ['id'], ['asc']);
        tasks.forEach((task) => {
            if (taskGroup.length == 0) {
                taskGroup.push({
                    name: task.name,
                    processTaskId: task.processTaskId,
                    level: taskGroup.length + 1,
                });
            }
            if (
                !taskGroup.some(
                    (data) => data.processTaskId == task.processTaskId,
                )
            ) {
                taskGroup.push({
                    name: task.name,
                    processTaskId: task.processTaskId,
                    level: taskGroup.length + 1,
                });
            }
        });

        const currentTask = tasks.find(
            (data) => data.status == EnumTaskStatus.Waiting,
        );
        if (previous && currentTask) {
            const currentGroup = taskGroup.find(
                (data) => data.processTaskId == currentTask.processTaskId,
            );
            taskGroup = currentGroup
                ? taskGroup.filter((data) => data.level < currentGroup.level)
                : taskGroup;
        }

        return _.orderBy(taskGroup, ['level'], ['desc']);
    }
}
