import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import _ from 'lodash';
import { Inject, Service } from 'typedi';
import { FormConfFieldStatusUseCase } from '@/modules/form/formConf/providers/formConfFieldStatus.usecase';
import {
    CurrentTask,
    SearchFormInstanceResult,
} from '@/modules/form/formInstance/types/forminstance.controller.type';
import { EnumFormFieldStatusField } from '@/modules/form/form/types/form.settings.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { EnumTaskStatus } from '../types/formInstanceTask.type';
import { FormFieldStatusesFn } from '../../formConf/types/formConfFieldStatus.types';
import { UserUseCase } from '@/modules/organization/providers/user.usecase';
import { UserPayload } from '@/modules/organization/types/user.type';

@Service()
export class CurrentTaskUseCase {
    @Inject()
    private formConfFieldStatusUCase: FormConfFieldStatusUseCase;
    @Inject()
    private userUCase: UserUseCase;

    async genAllCurrentTasksByInstanceResult(
        result: SearchFormInstanceResult,
        userPayload: UserPayload,
    ) {
        const formIds = result.rows.map((i) => i.formId);
        const userInfo = await this.userUCase.getFilterUserInfoById(
            userPayload.id,
        );
        const formFieldStatusesFnMap =
            await this.formConfFieldStatusUCase.getFormIdFormFieldStatusesFnMap(
                formIds,
            );
        result.rows.forEach((row) => {
            const formFieldStatusesFn = formFieldStatusesFnMap.get(row.formId);
            const tasks = row.formInstanceTasks;
            const allCurrentTasks = this.getAllCurrentTasks(
                tasks,
                formFieldStatusesFn,
                userInfo,
            );
            row.allCurrentTasks = allCurrentTasks;
        });
        return result;
    }

    genFinishCurrentTask(
        formInstance: FormInstance,
        formFieldStatusesFn: FormFieldStatusesFn,
        filterData: any,
    ) {
        const currentTask: Partial<CurrentTask> = {
            name: '關卡結束',
            formInstanceId: formInstance.id,
        };
        const formFieldStatus = formFieldStatusesFn(
            EnumFormFieldStatusField.Finish,
            filterData,
        );
        currentTask.formFieldStatus = formFieldStatus;
        return currentTask;
    }

    getCurrentTaskByTasks(
        formInstanceTasks: FormInstanceTask[],
        formFieldStatusesFn: FormFieldStatusesFn,
        filterData: any,
        userId: number,
        isInstanceOwner: boolean,
        isSuperUser: boolean,
    ) {
        const tasks = formInstanceTasks.filter((i) => !i.deleted);
        const processTasks = tasks.filter((i) => {
            const isStatus = [
                EnumTaskStatus.Waiting,
                EnumTaskStatus.Reconsidered,
            ].includes(i.status);
            const isUser = isSuperUser || isInstanceOwner || i.userId == userId;
            return isStatus && isUser;
        });
        if (processTasks.length == 0) return null;

        const userTasks = processTasks.filter((i) => i.userId == userId);
        const userTask = userTasks.length > 0 ? userTasks[0] : processTasks[0];

        const currentTask: CurrentTask = this.currentTask(
            userTask,
            formFieldStatusesFn,
            filterData,
            isSuperUser,
            userId,
        );
        return currentTask;
    }

    getAllCurrentTasks(
        tasks: FormInstanceTask[],
        formFieldStatusesFn: FormFieldStatusesFn | undefined,
        filterData: any,
        isSuperUser: boolean = false,
        userId?: number,
    ) {
        const allCurrentTasks = tasks
            .filter((data) =>
                [EnumTaskStatus.Waiting, EnumTaskStatus.Reconsidered].includes(
                    data.status,
                ),
            )
            .map((task) =>
                this.currentTask(
                    task,
                    formFieldStatusesFn,
                    filterData,
                    isSuperUser,
                    userId,
                ),
            );
        return allCurrentTasks;
    }

    currentTask(
        task: FormInstanceTask,
        formFieldStatusesFn: FormFieldStatusesFn | undefined,
        filterData: any,
        isSuperUser: boolean = false,
        userId?: number,
    ): CurrentTask {
        const currentTask: CurrentTask = {
            ...task,
        };
        filterData.currentTask = currentTask;
        const processTaskId = task.processTaskId;
        const formFieldStatus =
            formFieldStatusesFn?.(processTaskId, filterData) ?? [];
        currentTask.formFieldStatus = formFieldStatus;

        if (task.property) {
            const property = task.property;
            Object.assign(
                currentTask,
                _.omit(property, [
                    'user',
                    'additionalDocsUsers',
                    'allowAdditionalDocs',
                    'additionalDocsType',
                ]),
            );
            //只允許核准
            currentTask.onlyApproved = property.onlyApproved;
            //允許加簽(會簽/或簽)
            currentTask.allowAddAssignee =
                <boolean>property.allowAddAssignee ||
                <boolean>property.allowAddCountersign;
            currentTask.appendFormData = {
                keys: property.appendFormData ?? [],
            };
        }
        this.genCurrentTaskAllowAdditionalDocs(
            currentTask,
            task,
            isSuperUser,
            userId,
        );
        this.genCurrentTaskAllowFormEditorUsers(
            currentTask,
            task,
            isSuperUser,
            userId,
        );
        return currentTask;
    }

    private genCurrentTaskAllowFormEditorUsers(
        currentTask: CurrentTask,
        task: FormInstanceTask,
        isSuperUser: boolean = false,
        userId?: number,
    ) {
        if (!task.property) return currentTask;
        if (!task.property.allowFormEditorUsers) return currentTask;
        if (!userId) return currentTask;

        const allowFormEditorUserIds = task.property.allowFormEditorUsers.map(
            (i) => Number(i.id),
        );
        const isAllowFormEditorUser =
            allowFormEditorUserIds.includes(userId) || isSuperUser;
        task.property.allowFormEditor = isAllowFormEditorUser;
        return currentTask;
    }

    /**
     * 補件上傳
     * @param currentTask
     * @param task
     * @param isSuperUser
     * @param userId
     * @returns
     */
    private genCurrentTaskAllowAdditionalDocs(
        currentTask: CurrentTask,
        task: FormInstanceTask,
        isSuperUser: boolean = false,
        userId?: number,
    ) {
        if (!task.property) return currentTask;
        if (
            !task.property.allowAdditionalDocs ||
            !task.property.additionalDocsUsers
        )
            return currentTask;
        if (!userId) return currentTask;

        const additionalDocsUserIds = task.property.additionalDocsUsers.map(
            (i) => Number(i.id),
        );
        const isAllowAdditionalUser =
            additionalDocsUserIds.includes(userId) || isSuperUser;
        currentTask.allowAdditionalDocs = isAllowAdditionalUser;

        return currentTask;
    }
}
