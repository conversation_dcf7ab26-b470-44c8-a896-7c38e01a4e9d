import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import { Inject, Service } from 'typedi';
import { EntityManager } from 'typeorm';
import {
    AddAssigneeParams,
    AdditionalDocsParams,
    ApproveParams,
    CancelParams,
    ConvertUpdateParams,
    CreateParams,
    EnumTaskStatus,
    GroupingTask,
    ReconsiderParams,
    RejectParams,
    ReturnParams,
    UpdateParams,
} from '../types/formInstanceTask.type';
import _ from 'lodash';
import { BaseError } from '@clinico/base-error';
import { v4 as uuidv4 } from 'uuid';
import httpStatus from 'http-status';
import { NotificationService } from '@/modules/notification/providers/notification.service';
import { EnumNotificationEvent } from '@/modules/notification/types/notification.type';
import { DepartmentUserService } from '@/modules/organization/providers/departmentUser.service';
import { EnumTaskStatusStr } from '../../form/types/form.settings.type';
import { FormInstanceRepository } from '../../formInstance/providers/formInstance.repository';
import { FormInstanceTaskRepository } from './formInstanceTask.repository';
import { ProcessInstanceUseCase } from '@/modules/camunda/providers/processInstance.usecase';
import { FormInstanceTaskService } from './formInstanceTask.service';
import { ProcessInstanceTaskUseCase } from '@/modules/camunda/providers/processInstanceTask.ucase';
import { Task } from '@/modules/camunda/types/task.type';
import { FormValuesUseCase } from '../../formInstance/providers/formValues.usecase';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { TaskNotifyUseCase } from '../../../camunda/providers/taskNotify/taskNotify.usecase';
import { ValuesAppendFormData } from '../../formInstance/types/formValues.type';
import { CustomizeUseCase } from '@/customize/customize.usecase';
import { PermissionRepository } from '@/modules/permission/providers/permission.repository';
import { SearchReturnTaskParams } from '../../formInstance/types/forminstance.controller.type';
import { SpecifyUser } from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { InstancesValuesData } from '../../formInstance/types/formInstance.type';
import { TaskWebhookUseCase } from '@/modules/camunda/providers/taskWebhook/taskWebhook.usecase';
import { EnumTaskWebhookEvent } from '@/modules/camunda/types/taskWebhook.type';

@Service()
export class FormInstanceTaskUseCase {
    @Inject()
    private repo: FormInstanceTaskRepository;
    @Inject()
    private service: FormInstanceTaskService;
    @Inject()
    private formValueUCase: FormValuesUseCase;
    @Inject()
    private taskNotifyUCase: TaskNotifyUseCase;
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private notificationService: NotificationService;
    @Inject()
    private departmentUserService: DepartmentUserService;
    @Inject()
    private processInstanceUCase: ProcessInstanceUseCase;
    @Inject()
    private processInstanceTaskUCase: ProcessInstanceTaskUseCase;
    @Inject()
    private customizeUCase: CustomizeUseCase;
    @Inject()
    private permissionRepo: PermissionRepository;
    @Inject()
    private taskWebhookUCase: TaskWebhookUseCase;

    /**
     * 取得該任務的退回定義
     * - 該關卡有配置退回設定 task.property?.allowReturn
     * - 使用者在超級使用者模式有 isSuperUser 權限
     * @param userId 使用者 ID (決定是否有權限)
     * @param formInstanceId
     * @param formInstanceTaskId
     * @param isSuperUserMode 前端是否有開啟超級使用者模式
     * @param params
     * @returns
     */
    async findReturnDefinitionTasks(
        userId: number,
        formInstanceId: number,
        formInstanceTaskId: number,
        isSuperUserMode: boolean,
        params: SearchReturnTaskParams,
    ): Promise<GroupingTask[]> {
        const [task, userPermission] = await Promise.all([
            this.repo.findOneOrError(formInstanceTaskId),
            this.permissionRepo.findByUserId(userId),
        ]);
        let allowReturn = false;
        const isSuperUser: boolean =
            userPermission?.permission?.['isSuperUser'] ?? false;
        if (isSuperUserMode && isSuperUser) {
            allowReturn = true;
        } else {
            allowReturn = task.property?.allowReturn ?? false;
        }
        if (!allowReturn) {
            return [];
        }
        const tasks = await this.repo.search({
            ...params,
            formInstanceId,
        });
        const result = this.service.groupingTasks(tasks.rows, true);
        return result;
    }

    async createInstanceTask(params: CreateParams): Promise<FormInstanceTask> {
        if (!params.name) {
            throw new BaseError('請設定任務名稱', httpStatus.BAD_REQUEST);
        }
        const { formInstance } = params;
        const camundaTask = await this.processInstanceTaskUCase.getTaskById(
            params.camundaTask.id,
        );
        let formInstanceTask =
            await this.repo.initNewFormInstanceTaskByCamundaTask(
                params,
                camundaTask,
            );
        const { variables, property, activity, activityType, assignee } =
            await this.processInstanceTaskUCase.getTaskInfo(
                camundaTask,
                formInstance,
            );

        formInstanceTask = await this.repo.saveByCamundaTaskInfo(
            formInstanceTask,
            activityType,
            property,
            assignee,
        );

        // set camunda task assignee
        await this.processInstanceTaskUCase.setCamundaTaskAssignee(
            camundaTask,
            assignee,
        );
        await this.validate(formInstanceTask);
        await this.notifiTaskPublish(formInstanceTask);

        return formInstanceTask;
    }

    private async getInfoByUpdateParams(params: ConvertUpdateParams) {
        const { attachmentS3Keys, id } = params;
        const formInstanceTask = await this.repo.findOne(id);
        if (!formInstanceTask) {
            throw new BaseError('任務不存在', httpStatus.NOT_FOUND);
        }
        const camundaTask = await this.processInstanceTaskUCase.getTaskById(
            formInstanceTask.processInstanceTaskId,
        );
        const { values } = await this.taskWebhookUCase.webhookTaskProcess(
            params.id,
            params.status,
            EnumTaskWebhookEvent.Process,
            params.values,
        );
        params.values = values as JSON;
        const formData: JSON = params.values ?? ({} as JSON);
        formInstanceTask.status = params.status;
        formInstanceTask.memo = params.memo;
        formInstanceTask.values = formData;
        formInstanceTask.updatedUserId = params.updatedUserId;
        const { formInstance } = formInstanceTask;
        await this.validate(formInstanceTask);

        // 移到 process complete 完成後
        // await this.save(formInstanceTask, manager);

        /** 是否為會簽 */
        const isMultiInstance =
            await this.processInstanceTaskUCase.checkMultiInstance(
                formInstanceTask.processInstanceTaskId,
            );
        return {
            formInstance,
            formInstanceTask,
            camundaTask,
            isMultiInstance,
            formData,
            attachmentS3Keys: attachmentS3Keys ?? [],
        };
    }

    private async runCompleteCamundaProcess(
        status: EnumTaskStatus,
        formInstanceTask: FormInstanceTask,
        formInstance: FormInstance,
        camundaTask: Task,
        formData: JSON,
        attachmentS3Keys: string[],
    ) {
        const approved = status == EnumTaskStatus.Approved;
        const rejected = status == EnumTaskStatus.Rejected;
        const result = {
            tasks: [],
            nextFormInstanceTasks: [],
            isContinueCountersign: false,
            nextIsMultiInstance: false,
        };
        await this.updateSystemTaskAssigneeFormValueByTaskStatus(
            formInstanceTask,
            status,
            formInstance,
        );
        if (!approved && !rejected) return result;
        const tasks = await this.processInstanceTaskUCase.runCompleteProcess(
            status,
            formInstanceTask,
            formInstance,
            camundaTask,
            formData,
            attachmentS3Keys,
        );
        const {
            nextFormInstanceTasks,
            isContinueCountersign,
            nextIsMultiInstance,
        } = await this.runNextFormInstanceTasks(
            formInstance,
            formInstanceTask,
            tasks,
            status,
        );
        await this.notifiTaskPublish(formInstanceTask);
        return {
            tasks,
            nextFormInstanceTasks,
            isContinueCountersign,
            nextIsMultiInstance,
        };
    }

    private async runNextFormInstanceTasks(
        formInstance: FormInstance,
        formInstanceTask: FormInstanceTask,
        tasks: Task[],
        status: EnumTaskStatus,
    ) {
        const nextFormInstanceTasks: FormInstanceTask[] = [];
        const result = {
            nextFormInstanceTasks,
            nextIsMultiInstance: false,
            isContinueCountersign: false,
        };
        if (tasks.length != 0 && tasks[0].id) {
            const nextIsMultiInstance =
                await this.processInstanceTaskUCase.checkMultiInstance(
                    tasks[0].id,
                );
            result.nextIsMultiInstance = nextIsMultiInstance;
        }
        /** 是否繼續會簽 */
        const isContinueCountersign = await this.repo.checkContinueCountersign(
            formInstanceTask,
            tasks.map((data) => data.taskDefinitionKey),
        );
        result.isContinueCountersign = isContinueCountersign;

        // 繼續會簽 | 沒有下個任務 不新增 instanceTask
        if (isContinueCountersign || tasks.length == 0) {
            return result;
        }

        const groupCode = uuidv4();
        for (const task of tasks) {
            const nextFormInstanceTask = await this.createInstanceTask({
                camundaTask: task,
                formInstance,
                name: task.name,
                groupCode,
            });
            await this.customizeUCase.autoGenFormInstanceValues(
                formInstance,
                task,
                status,
            );
            nextFormInstanceTasks.push(nextFormInstanceTask);
        }
        result.nextFormInstanceTasks = nextFormInstanceTasks;
        return result;
    }

    private async runAfterCompleteCamundaProcess(
        params: UpdateParams,
        formInstanceTask: FormInstanceTask,
        camundaTask: Task,
        isMultiInstance: boolean,
        isContinueCountersign: boolean,
        nextIsMultiInstance: boolean,
        manager?: EntityManager,
    ) {
        await this.updateAfterCompletedStatusApproveReject(
            camundaTask,
            formInstanceTask,
            isMultiInstance,
            isContinueCountersign,
            nextIsMultiInstance,
            params.status,
        );
        await this.updateAfterCompletedStatusReturn(
            camundaTask,
            formInstanceTask,
            isMultiInstance,
            params.status,
        );
        await this.save(formInstanceTask, manager);
    }

    private async updateAfterCompletedStatusApproveReject(
        camundaTask: Task,
        formInstanceTask: FormInstanceTask,
        isMultiInstance: boolean,
        isContinueCountersign: boolean,
        nextIsMultiInstance: boolean,
        status: EnumTaskStatus,
    ) {
        if (
            status != EnumTaskStatus.Approved &&
            status != EnumTaskStatus.Rejected
        ) {
            return;
        }
        // 會簽結束時，將其他未處理的task狀態改為完成
        if (!isContinueCountersign && isMultiInstance) {
            await this.repo.updateCompletedStatusByGroup(formInstanceTask);
        }
        if (nextIsMultiInstance) {
            await this.processInstanceUCase.resetCountingAddAssignee(
                formInstanceTask.formInstance.processInstanceId!,
            );
        }
    }

    private async updateAfterCompletedStatusReturn(
        camundaTask: Task,
        formInstanceTask: FormInstanceTask,
        isMultiInstance: boolean,
        status: EnumTaskStatus,
    ) {
        if (status != EnumTaskStatus.Return) {
            return;
        }
        // 會簽結束時，將其他未處理的task狀態改為完成
        if (isMultiInstance) {
            await this.repo.updateCompletedStatusByGroup(formInstanceTask);
            await this.processInstanceUCase.resetCountingStatus(
                camundaTask.processInstanceId,
            );
            await this.processInstanceUCase.resetCountingAddAssignee(
                camundaTask.processInstanceId,
            );
        }
    }

    private convertUpdateParams(params: UpdateParams): ConvertUpdateParams {
        const convertParams: ConvertUpdateParams = {
            ...params,
            values: undefined,
        };
        convertParams.values =
            params.values && _.isString(params.values)
                ? JSON.parse(params.values)
                : params.values;
        return convertParams;
    }

    /**
     * task process 更新 formInstanceTask 資訊
     * TODO: params.values 這邊用 convertUpdateParams 轉換為 JSON Object，
     * 等前端推送改為 object 後，再變更 params.values type 為 JSON Object。
     * @param params
     * @param manager
     * @returns
     */
    private async updateInstanceTask(
        params: UpdateParams,
        manager?: EntityManager,
    ): Promise<{
        current: FormInstanceTask;
        next: FormInstanceTask[];
    }> {
        const convertParams = this.convertUpdateParams(params);
        const {
            formInstanceTask,
            camundaTask,
            formInstance,
            isMultiInstance,
            formData,
            attachmentS3Keys,
        } = await this.getInfoByUpdateParams(convertParams);

        const {
            nextFormInstanceTasks,
            isContinueCountersign,
            nextIsMultiInstance,
        } = await this.runCompleteCamundaProcess(
            params.status,
            formInstanceTask,
            formInstance,
            camundaTask,
            formData,
            attachmentS3Keys,
        );

        await this.runAfterCompleteCamundaProcess(
            params,
            formInstanceTask,
            camundaTask,
            isMultiInstance,
            isContinueCountersign,
            nextIsMultiInstance,
            manager,
        );
        await this.taskWebhookUCase.webhookTaskProcess(
            params.id,
            params.status,
            EnumTaskWebhookEvent.Complete,
            params.values,
        );
        return { current: formInstanceTask, next: nextFormInstanceTasks };
    }

    async addAssignee(params: AddAssigneeParams) {
        const departmentUser = await this.departmentUserService.findOneOrError({
            departmentId: params.departmentId,
            userId: params.userId,
        });
        const formInstanceTask = await this.repo.findOneOrError(params.id);
        const formInstance = await this.formInstanceRepo.findOneOrError(
            formInstanceTask.formInstanceId,
        );
        const { differenceTask } =
            await this.processInstanceTaskUCase.runAddAssignee(
                departmentUser,
                formInstanceTask,
                formInstance,
            );

        const newFormInstanceTask = await this.createInstanceTask({
            camundaTask: differenceTask,
            formInstance,
            name: formInstanceTask.name,
            groupCode: formInstanceTask.groupCode,
            memo: '加簽',
        });

        //信件通知
        await this.taskNotifyUCase.notifyTaskProcess(
            formInstanceTask,
            [newFormInstanceTask],
            EnumTaskStatusStr.AddAssignee,
        );
    }

    async runAutoApprove(next: FormInstanceTask[], updatedUserId: number) {
        const skipTasks: boolean[] = [];
        for (const task of next) {
            const property = task.property;

            const isSkipTaskByUserCodes =
                property?.allowSkipUserCodes?.includes(task.userCode ?? '') ??
                false;
            /**
             * filterAllowSkipUsers
             * 過濾掉不允許跳過的人員
             */
            const filterAllowSkipUsers = property?.filterAllowSkipUsers ?? [];
            const filterAllowSkipUsersIds = filterAllowSkipUsers.map(
                (i) => i.id,
            );
            const isSkipTaskByUsers =
                property?.allowSkipUsers
                    ?.map((i) => i.id)
                    .filter((i) => !filterAllowSkipUsersIds.includes(i))
                    .includes(task.userId ?? 0) ?? false;

            if (isSkipTaskByUserCodes || isSkipTaskByUsers) {
                skipTasks.push(true);
                const isSkip = task.userCode?.includes('L0528') ?? false; //David
                await this.approve({
                    id: task.id,
                    values: isSkip ? { isSkip: true } : undefined,
                    updatedUserId,
                });
            }
        }
        return skipTasks.length == next.length;
    }

    private async autoApprovedActivityFinished(
        data: {
            current: FormInstanceTask;
            next: FormInstanceTask[];
        },
        status: EnumTaskStatus,
    ) {
        const task =
            data.next.filter(
                (i) => i.processTaskId == 'Activity_Finished',
            )[0] ?? null;
        if (!task) {
            return data;
        }
        data.next = data.next.filter(
            (i) => i.processTaskId != 'Activity_Finished',
        );
        const instance = await this.formInstanceRepo.findOneOrError(
            task.formInstanceId,
        );
        const camundaTask = await this.processInstanceTaskUCase.getTaskById(
            task.processInstanceTaskId,
        );
        const approved = [
            EnumTaskStatus.Approved,
            EnumTaskStatus.Completed,
        ].includes(status);
        await this.processInstanceTaskUCase.camundaTaskComplete(
            camundaTask,
            instance,
            {} as JSON,
            approved,
        );

        // task.deleted = true; // 決定前端要不要顯示 row
        /* task.userId = null;
    task.userCode = null;
    task.departmentId = null;
    task.departmentCode = null; */
        task.status = approved ? EnumTaskStatus.Completed : status;

        const updateTask = await this.repo.saveFormInstanceTask(task);

        await this.notifiTaskPublish(task);

        return data;
    }

    async approve(params: ApproveParams) {
        const result = await this.updateInstanceTask({
            ...params,
            status: EnumTaskStatus.Approved,
        });

        await this.autoApprovedActivityFinished(
            result,
            EnumTaskStatus.Approved,
        );

        const isSkipTask = await this.runAutoApprove(
            result.next,
            params.updatedUserId,
        );

        const allowSkipNotifyMailUsers =
            result.next[0]?.property?.allowSkipNotifyMailUsers;

        /**
         * 之後在把舊有的運行方式，改成新的邏輯拆分，這邊先保留
         *
         * 之後改用新的 拆分自動簽核及跳過信件通知的邏輯
         * - allowSkipNotifyMailUsers
         *
         * ---
         * 移除舊有的運行方式
         * - allowSkipUserCodes: 要移除
         */
        //信件通知
        if (!isSkipTask || !_.isUndefined(allowSkipNotifyMailUsers)) {
            await this.taskNotifyUCase.notifyTaskProcess(
                result.current,
                result.next,
                EnumTaskStatusStr.Approved,
            );
        }

        return result;
    }

    /**
     * 人員附件上傳
     *
     * 將暫存區的檔案傳到processInstanceId底下
     * @param params AdditionalDocsParams
     * @returns
     */
    async additionalDocs(params: AdditionalDocsParams) {
        // convert params values
        const formData: JSON =
            params.values && _.isString(params.values)
                ? JSON.parse(params.values)
                : params.values;
        const { id, attachmentS3Keys } = params;
        const formInstanceTask = await this.repo.findOneOrError(id);
        const formInstance = await this.formInstanceRepo.findOneOrError(
            formInstanceTask.formInstanceId,
        );
        const nextFormInstanceTasks: FormInstanceTask[] = [];
        if (!(formInstance.processInstanceId && formData)) {
            return { current: formInstanceTask, next: nextFormInstanceTasks };
        }

        const mergeUploadFilesValues = (
            _taskValues: ValuesAppendFormData,
            _formData: JSON,
        ) => {
            const taskValuesJson = _taskValues;
            const valuesJson = _formData;
            const taskUploadFiles: any[] = taskValuesJson['uploadFiles'] ?? [];
            const valueUploadFiles: any[] = valuesJson['uploadFiles'] ?? [];
            const mergeValues = _.assign({}, taskValuesJson, {
                uploadFiles: [...taskUploadFiles, ...valueUploadFiles],
            });
            return mergeValues;
        };

        formInstanceTask.values = mergeUploadFilesValues(
            formInstanceTask.values ?? {},
            formData,
        );

        await this.formValueUCase.instanceTaskUpdateFormData(
            formInstance.processInstanceId,
            formInstance,
            formInstanceTask,
            formData,
            attachmentS3Keys ?? [],
            params.updatedUserId,
        );

        // 信件通知
        await this.taskNotifyUCase.notifyTaskProcess(
            formInstanceTask,
            [],
            EnumTaskStatusStr.AdditionalDocs,
        );
        return { current: formInstanceTask, next: nextFormInstanceTasks };
    }

    async reject(params: RejectParams) {
        const result = await this.updateInstanceTask({
            ...params,
            status: EnumTaskStatus.Rejected,
        });
        await this.autoApprovedActivityFinished(
            result,
            EnumTaskStatus.Rejected,
        );
        //信件通知
        await this.taskNotifyUCase.notifyTaskProcess(
            result.current,
            result.next,
            EnumTaskStatusStr.Rejected,
        );
        return result;
    }

    async cancel(params: CancelParams) {
        const tasks = await this.repo.search({
            formInstanceId: params.instanceId,
            status: EnumTaskStatus.Waiting,
        });
        for (const task of tasks.rows) {
            await this.updateInstanceTask({
                ...params,
                id: task.id,
                status: EnumTaskStatus.Canceled,
            });
        }
    }

    async reconsider(params: ReconsiderParams, manager?: EntityManager) {
        const result = await this.updateInstanceTask(
            {
                ...params,
                status: EnumTaskStatus.Reconsidered,
            },
            manager,
        );
        // notify 在 formInstance 發布
        return result;
    }

    async return(params: ReturnParams) {
        const currentTask = await this.repo.findOneOrError(params.id);
        const formInstance = await this.formInstanceRepo.findOneOrError(
            currentTask.formInstanceId,
        );
        const returnTaskActivityId = await this.repo.getActivityId({
            formInstanceId: currentTask.formInstanceId,
            processTaskId: params.processTaskId,
        });

        await this.updateInstanceTask({
            ...params,
            status: EnumTaskStatus.Return,
        });

        const { currentCamundaTasks } =
            await this.processInstanceTaskUCase.runReturnProcess(
                currentTask,
                returnTaskActivityId,
            );

        const nextFormInstanceTasks: FormInstanceTask[] = [];
        const groupCode = uuidv4();
        for (const camundaTask of currentCamundaTasks) {
            const nextFormInstanceTask = await this.createInstanceTask({
                camundaTask: camundaTask,
                formInstance,
                name: camundaTask.name,
                groupCode: groupCode,
                memo: '退回',
            });
            nextFormInstanceTasks.push(nextFormInstanceTask);
        }

        //信件通知
        const completedCurrentTask = await this.repo.findOneOrError(params.id);
        await this.taskNotifyUCase.notifyTaskProcess(
            completedCurrentTask,
            nextFormInstanceTasks,
            EnumTaskStatusStr.Return,
        );

        return { current: currentTask, next: nextFormInstanceTasks };
    }

    /**
     * formInstance value 增加紀錄關卡簽核人員
     * @param task
     * @param status
     * @param formInstance
     */
    private async updateSystemTaskAssigneeFormValueByTaskStatus(
        task: FormInstanceTask,
        status: EnumTaskStatus,
        formInstance?: FormInstance,
    ) {
        formInstance =
            formInstance ??
            (await this.formInstanceRepo.findOneOrError(task.formInstanceId));
        const value = this.genSystemTaskAssigneeFormValue(
            formInstance.values ?? {},
            task,
            status,
        );
        formInstance.values = value;
        await this.formInstanceRepo.saveFormInstance(formInstance);
    }

    private genSystemTaskAssigneeFormValue(
        formValue: InstancesValuesData,
        task: FormInstanceTask,
        status: EnumTaskStatus,
    ) {
        switch (status) {
            case EnumTaskStatus.Approved:
                return this.appendSystemTaskAssigneeFormValue(formValue, task);
            case EnumTaskStatus.Rejected:
            case EnumTaskStatus.Return:
                return this.initAllSystemTaskAssigneeFormValue(formValue);
        }
        return formValue;
    }

    private initAllSystemTaskAssigneeFormValue(formValue: InstancesValuesData) {
        const systemKey = '_';
        formValue[systemKey] = null;
        return formValue;
    }

    private initSystemTaskAssigneeFormValue(
        formValue: InstancesValuesData,
        task: FormInstanceTask,
    ) {
        const step = task.processTaskId;
        const systemKey = '_';
        if (_.isEmpty(formValue[systemKey])) {
            formValue[systemKey] = {};
        }
        formValue[systemKey][step] = null;
        return formValue;
    }

    appendSystemTaskAssigneeFormValue(
        formValue: InstancesValuesData,
        task: FormInstanceTask,
    ) {
        const step = task.processTaskId;
        const systemKey = '_';
        if (_.isEmpty(formValue[systemKey])) {
            formValue[systemKey] = {};
        }
        if (_.isEmpty(formValue[systemKey][step])) {
            formValue[systemKey][step] = {};
        }
        const stepFormValue = formValue[systemKey][step];
        const specifyUser: SpecifyUser = {
            companyId: task.companyId,
            deptCode: task.departmentCode ?? '',
            userCode: task.userCode ?? '',
        };
        const stepAssignees = stepFormValue['assignees'] ?? [];
        stepAssignees.push(specifyUser);
        const setStepAssignees = Array.from(new Set(stepAssignees));
        formValue[systemKey][step]['assignees'] = setStepAssignees;
        formValue[systemKey][step][`assignee_${setStepAssignees.length}`] =
            specifyUser;
        return formValue;
    }

    protected async validate(task: FormInstanceTask): Promise<void> {
        // TODO
    }

    private async notifiTaskPublish(
        formInstanceTask: FormInstanceTask,
        keys: EnumNotificationEvent[] = [EnumNotificationEvent.Pending],
    ) {
        if (formInstanceTask.userId) {
            await this.notificationService.publish({
                userId: formInstanceTask.userId,
                keys,
            });
        }
        return;
    }

    async save(entity: any, manager?: EntityManager) {
        if (manager) {
            return await manager.save(entity);
        }
        return await this.repo.saveFormInstanceTask(entity);
    }
}
