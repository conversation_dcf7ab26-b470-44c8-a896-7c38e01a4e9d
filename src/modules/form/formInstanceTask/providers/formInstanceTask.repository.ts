import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import _ from 'lodash';
import { Inject, Service } from 'typedi';
import { FindOptionsRelations, FindOptionsWhere, In } from 'typeorm';
import {
    CreateParams,
    EnumTaskStatus,
    SearchParams,
} from '../types/formInstanceTask.type';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { Assignee } from '@/modules/assignee/types/assignee.type';
import { Task } from '@/modules/camunda/types/task.type';
import { TaskProperty } from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { FormConfService } from '@/modules/form/formConf/providers/formConf.service';

@Service()
export class FormInstanceTaskRepository extends CommonService<FormInstanceTask> {
    repository = ClinicoDataSource.getRepository(FormInstanceTask);
    @Inject()
    private formConfSvc: FormConfService;

    convertInstanceJsonb(instance: FormInstance) {
        instance.values =
            instance.values && _.isString(instance.values)
                ? JSON.parse(instance.values)
                : instance.values;
        return instance;
    }

    convertJsonb(task: FormInstanceTask) {
        task.property =
            task.property && _.isString(task.property)
                ? JSON.parse(task.property)
                : task.property;
        task.values =
            task.values && _.isString(task.values)
                ? JSON.parse(task.values)
                : task.values;
        task.formInstance = task.formInstance
            ? this.convertInstanceJsonb(task.formInstance)
            : task.formInstance;
        if (task.formInstance?.form) {
            task.formInstance.form = this.formConfSvc.genFormAllConf(
                task.formInstance.form,
            );
        }
        return task;
    }

    async saveFormInstanceTask(formInstanceTask: FormInstanceTask) {
        this.convertJsonb(formInstanceTask);
        formInstanceTask = await this.repository.save(formInstanceTask);
        return formInstanceTask;
    }

    async initNewFormInstanceTaskByCamundaTask(
        params: CreateParams,
        camundaTask: Task,
        status: EnumTaskStatus = EnumTaskStatus.Waiting,
    ) {
        const create = this.repository.create({
            processInstanceTaskId: camundaTask.id,
            processTaskId: camundaTask.taskDefinitionKey,
            formInstanceId: params.formInstance.id,
            name: params.name,
            status,
            groupCode: params.groupCode,
        });
        return this.saveFormInstanceTask(create);
    }

    async saveByCamundaTaskInfo(
        formInstanceTask: FormInstanceTask,
        activityType: string,
        property: TaskProperty,
        assignee?: Assignee | null,
        status: EnumTaskStatus = EnumTaskStatus.Waiting,
    ) {
        formInstanceTask.companyId = assignee?.companyId;
        formInstanceTask.departmentId = assignee?.departmentId;
        formInstanceTask.departmentCode = assignee?.departmentCode;
        formInstanceTask.userId = assignee?.userId;
        formInstanceTask.userCode = assignee?.userCode;
        formInstanceTask.status = status;
        formInstanceTask.property = property;
        formInstanceTask.processTaskActivityType = activityType;
        formInstanceTask = await this.saveFormInstanceTask(formInstanceTask);
        return formInstanceTask;
    }
    createNewFormInstanceTask(
        params: CreateParams,
        camundaTask: Task,
        activityType: string,
        property: TaskProperty,
        assignee?: Assignee | null,
        status: EnumTaskStatus = EnumTaskStatus.Waiting,
    ) {
        return this.repository.create({
            processInstanceTaskId: camundaTask.id,
            processTaskId: camundaTask.taskDefinitionKey,
            formInstanceId: params.formInstance.id,
            name: params.name,
            companyId: assignee?.companyId,
            departmentId: assignee?.departmentId,
            departmentCode: assignee?.departmentCode,
            userId: assignee?.userId,
            userCode: assignee?.userCode,
            status,
            property,
            groupCode: params.groupCode,
            processTaskActivityType: activityType,
        });
    }

    /** 將其他未處理的task狀態改為完成(或簽時) */
    async updateCompletedStatusByGroup(task: FormInstanceTask): Promise<void> {
        if (!task.groupCode) {
            return;
        }
        const tasks = await this.search({
            formInstanceId: task.formInstanceId,
            groupCode: task.groupCode,
            inStatus: [EnumTaskStatus.Waiting, EnumTaskStatus.Reconsidered],
        });
        const rows = tasks.rows.filter((data) => data.id != task.id);
        const updateFormInstanceTask = rows.map(
            (data) =>
                <FormInstanceTask>{
                    ...data,
                    status: EnumTaskStatus.Completed,
                },
        );
        await this.repository.save(updateFormInstanceTask);
    }

    async checkContinueCountersign(
        task: FormInstanceTask,
        taskIdIgnores: string[],
    ): Promise<boolean> {
        let isCountersign = false;
        const tasks = await this.search({
            formInstanceId: task.formInstanceId,
            groupCode: task.groupCode,
        });
        tasks.rows.forEach((data) => {
            if (taskIdIgnores.includes(data.processTaskId)) {
                isCountersign = true;
            }
        });
        return isCountersign;
    }

    async getActivityId(params: {
        formInstanceId: number;
        processTaskId: string;
    }): Promise<string> {
        let activityId = params.processTaskId;
        const specifyTasks = await this.search({
            formInstanceId: params.formInstanceId,
            processTaskId: params.processTaskId,
        });
        if (specifyTasks.count == 0) {
            throw new BaseError(
                'processTaskId of task not found',
                httpStatus.BAD_REQUEST,
            );
        }
        if (!specifyTasks.rows[0].processTaskActivityType) {
            throw new BaseError(
                'instance version not supported',
                httpStatus.BAD_REQUEST,
            );
        }

        if (
            specifyTasks.rows[0].processTaskActivityType == 'multiInstanceBody'
        ) {
            activityId += '#multiInstanceBody';
        }
        return activityId;
    }

    async search(
        params: SearchParams,
        relations?: FindOptionsRelations<FormInstanceTask>,
    ): Promise<CommonSearchResult<FormInstanceTask>> {
        const filters: FindOptionsWhere<FormInstanceTask> = { deleted: false };
        if (params.id) {
            filters.id = params.id;
        }
        if (params.processInstanceTaskId) {
            filters.processInstanceTaskId = params.processInstanceTaskId;
        }
        if (params.formInstanceId) {
            filters.formInstanceId = params.formInstanceId;
        }
        if (params.companyId) {
            filters.companyId = params.companyId;
        }
        if (params.departmentId) {
            filters.departmentId = params.departmentId;
        }
        if (params.departmentCode) {
            filters.departmentCode = params.departmentCode;
        }
        if (params.userId) {
            filters.userId = params.userId;
        }
        if (params.userCode) {
            filters.userCode = params.userCode;
        }
        if (!_.isUndefined(params.status) && !_.isNull(params.status)) {
            filters.status = params.status;
        }
        if (params.inStatus) {
            filters.status = In(params.inStatus);
        }
        if (params.groupCode) {
            filters.groupCode = params.groupCode;
        }
        if (params.processTaskId) {
            filters.processTaskId = params.processTaskId;
        }

        const defaultRelations: FindOptionsRelations<FormInstanceTask> = {
            formInstance: {
                ownerUser: true,
                form: {},
            },
            company: true,
            department: true,
            user: true,
            reminders: true,
        };

        /**
         * default 優先於 relations
         */
        const findRelations: FindOptionsRelations<FormInstanceTask> = _.merge(
            relations,
            defaultRelations,
        );

        const data = await this.repository.findAndCount({
            relations: findRelations,
            where: filters,
            take: params.limit,
            skip: params.offset,
            order: {
                id: 'DESC',
            },
        });

        const result: CommonSearchResult<FormInstanceTask> = {
            rows: data[0].map((i) => this.convertJsonb(i)),
            count: data[1],
        };
        return result;
    }
}
