import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { CommonSearchParams } from '@/common/types/common.type';
import { Task } from '@/modules/camunda/types/task.type';
import { InstancesValuesData } from '../../formInstance/types/formInstance.type';

export enum EnumTaskStatus {
    Waiting = 0,
    Approved = 1,
    Rejected = 2,
    Completed = 3, //或簽完成時，其他task改為完成
    Canceled = 4,
    Reconsidered = 5, //再議
    Return = 6, //退回指定task
}

export type SearchParams = CommonSearchParams & {
    processInstanceTaskId?: string;
    formInstanceId?: number;
    companyId?: number;
    departmentId?: number;
    departmentCode?: string;
    userId?: number;
    userCode?: string;
    status?: EnumTaskStatus;
    inStatus?: EnumTaskStatus[];
    groupCode?: string;
    processTaskId?: string;
};

export type CreateParams = {
    camundaTask: Task;
    // formInstanceId: number;
    formInstance: FormInstance;
    name: string;
    memo?: string;
    groupCode?: string;
};

export type ConvertUpdateParams = {
    id: number;
    status: EnumTaskStatus;
    updatedUserId: number;
    values?: JSON;
    memo?: string;
    attachmentS3Keys?: string[];
};

export type UpdateParams = {
    id: number;
    status: EnumTaskStatus;
    updatedUserId: number;
    values?: JSON | InstancesValuesData;
    memo?: string;
    attachmentS3Keys?: string[];
};

export type AddAssigneeParams = {
    id: number;
    departmentId: number;
    userId: number;
    updatedUserId: number;
};

export type EditParams = {
    id: number;
    updatedUserId: number;
    values?: JSON | InstancesValuesData;
    memo?: string;
    attachmentS3Keys?: string[];
    message?: string | string[];
};

export type ApproveParams = {
    id: number;
    updatedUserId: number;
    values?: JSON | InstancesValuesData;
    memo?: string;
    attachmentS3Keys?: string[];
};

export type AdditionalDocsParams = {
    id: number;
    updatedUserId: number;
    values?: JSON | InstancesValuesData;
    attachmentS3Keys?: string[];
};

export type RejectParams = {
    id: number;
    updatedUserId: number;
    values?: JSON | InstancesValuesData;
    memo?: string;
};

export type CancelParams = {
    instanceId: number;
    updatedUserId: number;
    values?: JSON | InstancesValuesData;
    memo?: string;
};

export type ReconsiderParams = RejectParams;

export type ReturnParams = RejectParams & {
    processTaskId: string;
};

export type GroupingTask = {
    name: string;
    processTaskId: string;
    level: number;
};
