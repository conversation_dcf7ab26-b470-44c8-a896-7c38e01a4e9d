import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { FormInstanceMessage } from '@/common/models/clinico/bpm/formInstanceMessage.model';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { Service } from 'typedi';
import { EntityManager, FindOptionsWhere } from 'typeorm';
import {
    CreateParams,
    SearchParams,
    UpdateParams,
} from '../types/formInstanceMessage.type';

@Service()
export class FormInstanceMessageService extends CommonService<FormInstanceMessage> {
    protected repository = ClinicoDataSource.getRepository(FormInstanceMessage);

    async save(entity: FormInstanceMessage, manager?: EntityManager) {
        if (manager) {
            return await manager.save(entity);
        }
        return await this.repository.save(entity);
    }

    async search(
        params: SearchParams,
    ): Promise<CommonSearchResult<FormInstanceMessage>> {
        const filters: FindOptionsWhere<FormInstanceMessage> = {
            deleted: false,
        };
        if (params.id) {
            filters.id = params.id;
        }

        const [rows, count] = await this.repository.findAndCount({
            relations: {
                formInstance: { ownerUser: true, form: true },
                formInstanceTask: true,
                createUser: true,
            },
            where: filters,
            take: params.limit,
            skip: params.offset,
            order: { id: 'ASC' },
        });

        const result: CommonSearchResult<FormInstanceMessage> = {
            rows,
            count,
        };
        return result;
    }

    async create(
        params: CreateParams,
        manager?: EntityManager,
    ): Promise<FormInstanceMessage> {
        const formInstanceMessage = this.repository.create({
            ...params,
        });

        return await this.save(formInstanceMessage, manager);
    }

    async update(params: UpdateParams): Promise<FormInstanceMessage> {
        const formInstanceMessage = await this.findOneOrError(params.id);
        if (params.updatedUserId != formInstanceMessage.createdUserId) {
            throw new BaseError('不允許非留言人變更', httpStatus.BAD_REQUEST);
        }
        formInstanceMessage.message = params.message;
        return await this.save(formInstanceMessage);
    }
}
