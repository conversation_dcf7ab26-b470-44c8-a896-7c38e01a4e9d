import { ValuesAppendFormData } from '@/modules/form/formInstance/types/formValues.type';
import {
    IsBoolean,
    IsInt,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
} from 'class-validator';
import {
    InternalCancelFormInstanceParams,
    InternalCancelFormInstanceResult,
} from './internalForminstance.type';

export class ReqCancelFormInstanceBody
    implements InternalCancelFormInstanceParams
{
    @IsInt()
    @IsNotEmpty()
    formInstanceId: number;

    @IsString()
    @IsNotEmpty()
    updatedUserCode: string;
}

export class ResCancelFormInstance implements InternalCancelFormInstanceResult {
    @IsBoolean()
    isCanceled: boolean;
}

export class CreateParams {
    @IsString()
    @IsNotEmpty()
    formCode: string;

    @IsObject()
    @IsOptional()
    values?: ValuesAppendFormData;

    @IsString()
    @IsOptional()
    companyCode?: string;

    @IsString()
    @IsOptional()
    departmentCode?: string;

    @IsString()
    @IsNotEmpty()
    userCode: string;

    @IsString({ each: true })
    @IsOptional()
    attachmentS3Keys?: string[];

    @IsString()
    @IsNotEmpty()
    createdUserCode: string;
}
