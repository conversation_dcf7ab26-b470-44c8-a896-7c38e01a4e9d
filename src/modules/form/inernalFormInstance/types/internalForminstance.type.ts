import { GRPCFileUpload } from '@/common/types/common.grpc.type';
import { ValuesAppendFormData } from '../../formInstance/types/formValues.type';

export type InternalUserParams = {
    companyCode?: string;
    departmentCode?: string;
    userCode: string;
};

export type InternalCreateFormInstanceParams = InternalUserParams & {
    formCode: string;
    values?: ValuesAppendFormData;
    createdUserCode: string;
    files?: GRPCFileUpload[];
};

export type InternalCancelFormInstanceParams = {
    formInstanceId: number;
    updatedUserCode: string;
};

export type InternalCancelFormInstanceResult = {
    isCanceled: boolean;
};
