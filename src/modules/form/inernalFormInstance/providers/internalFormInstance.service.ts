import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { UserPosition } from '@/common/models/clinico/core/userPosition.model';
import { AttachmentService } from '@/modules/attachment/providers/attachment.service';
import { UserService } from '@/modules/organization/providers/user.service';
import { UserPositionService } from '@/modules/organization/providers/userPosition.service';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { Inject, Service } from 'typedi';
import {
    InternalCancelFormInstanceParams,
    InternalCreateFormInstanceParams,
    InternalUserParams,
} from '../types/internalForminstance.type';
import { FormRepository } from '../../form/providers/form.repository';
import { FormInstanceUseCase } from '../../formInstance/providers/formInstance.usecase';
import { FormInstanceRepository } from '../../formInstance/providers/formInstance.repository';
import { EnumInstanceStatus } from '../../formInstance/types/formInstance.type';

@Service()
export class InternalFormInstanceService {
    @Inject()
    private formRepo: FormRepository;
    @Inject()
    private userPositionService: UserPositionService;
    @Inject()
    private userService: UserService;
    @Inject()
    private formInstanceUCase: FormInstanceUseCase;
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private attachmentService: AttachmentService;

    async cancelFormInstance(params: InternalCancelFormInstanceParams) {
        const { formInstanceId, updatedUserCode } = params;
        const [updatedUser, formInstance] = await Promise.all([
            this.userService.findOneOrErrorByCode(updatedUserCode),
            this.formInstanceRepo.findOneOrError(formInstanceId),
        ]);
        const canceledFormInstance =
            await this.formInstanceUCase.cancelFormInstance({
                id: formInstance.id,
                updateUserId: updatedUser.id,
            });
        return {
            isCanceled:
                canceledFormInstance.status == EnumInstanceStatus.Canceled,
            formInstance: canceledFormInstance,
        };
    }

    async create(
        params: InternalCreateFormInstanceParams,
    ): Promise<FormInstance> {
        const from = await this.formRepo.findOneByCodeOrErrpr(params.formCode);
        const ownerUser = await this.getUserByParams(params);

        if (!ownerUser) {
            throw new BaseError('User not found', httpStatus.NOT_FOUND);
        }
        const createdUser = await this.userService.findOneOrErrorByCode(
            params.createdUserCode,
        );

        const attachments: {
            name: string;
            s3Key: string;
        }[] = [];
        if (params.files) {
            console.log('=== InternalFormInstanceService.create - Processing files ===');
            console.log('Files count:', params.files.length);

            for (let i = 0; i < params.files.length; i++) {
                const file = params.files[i];
                console.log(`Processing file ${i + 1}/${params.files.length}:`, {
                    filename: file.filename,
                    mimetype: file.mimetype,
                    encoding: file.encoding,
                    contentType: typeof file.content,
                    contentLength: file.content instanceof Buffer ? file.content.length : 'unknown',
                    contentIsBuffer: Buffer.isBuffer(file.content),
                    contentConstructor: file.content?.constructor?.name
                });

                try {
                    const s3Key = await this.attachmentService.upload({
                        fileName: file.filename,
                        encoding: file.encoding,
                        contentType: file.mimetype,
                        body: file.content,
                    });
                    console.log(`File ${i + 1} uploaded successfully with S3 key:`, s3Key);

                    attachments.push({
                        name: file.filename,
                        s3Key,
                    });
                } catch (error) {
                    console.error(`Error uploading file ${i + 1}:`, {
                        filename: file.filename,
                        error: error.message,
                        stack: error.stack
                    });
                    throw error;
                }
            }
            console.log('=== All files processed successfully ===');
        }
        if (params.values && attachments.length > 0) {
            params.values.attachments = attachments;
        }

        const instance = await this.formInstanceUCase.createFormInstance({
            ...params,
            values: params.values,
            formId: from.id,
            ownerCompanyId: ownerUser.department.companyId,
            ownerDepartmentId: ownerUser.departmentId,
            ownerUserId: ownerUser.userId,
            createdUserId: createdUser.id,
            attachmentS3Keys: attachments.map((data) => data.s3Key),
        });

        return instance;
    }

    private async getUserByParams(params: InternalUserParams) {
        let user: UserPosition | null = null;
        if (params.companyCode || params.departmentCode) {
            user = await this.userPositionService.findOneByParamsOrError({
                companyCode: params.companyCode,
                departmentCode: params.departmentCode,
                userCode: params.userCode,
            });
        } else {
            user = await this.userPositionService.findOneByParamsOrError({
                userCode: params.userCode,
                isMain: true,
            });
        }
        return user;
    }
}
