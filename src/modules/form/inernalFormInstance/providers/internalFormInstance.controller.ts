import { InternalAuthKoaInterceptor } from '@/common/interceptors/serviceAuth.interceptor';
import { OpenAPIParams } from '@/common/types/common.openapi.type';
import { Helpers } from '@clinico/clinico-node-framework';
import { Body, JsonController, Post, UseBefore } from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import {
    CreateParams,
    ReqCancelFormInstanceBody,
    ResCancelFormInstance,
} from '../types/internalForminstance.controller.type';
import { InternalFormInstanceService } from './internalFormInstance.service';

@JsonController('/internal/form/instances')
@UseBefore(InternalAuthKoaInterceptor)
@Service()
export class InternalFormInstanceController {
    @Inject()
    private internalFormInstanceService: InternalFormInstanceService;

    @OpenAPI(OpenAPIParams.internalCreateInstance)
    @Post('/')
    async create(@Body() params: CreateParams) {
        const instance = await this.internalFormInstanceService.create(params);
        return Helpers.Json.success(instance);
    }

    @Post('/cancel')
    @ResponseSchema(ResCancelFormInstance)
    async cancelTask(@Body() params: ReqCancelFormInstanceBody) {
        await this.internalFormInstanceService.cancelFormInstance(params);
        return Helpers.Json.success();
    }
}
