import { CommonSearchParams } from '@/common/types/common.type';
import {
    FormTaskAssigneeInfo,
    FormTaskAssignees,
} from '../../form/types/form.taskSetting.type';

export type SearchFormConfTaskAssigneesParams = CommonSearchParams & {
    formId?: number;
    formIds?: number[];
    formVersion?: number;
    step?: string;
};

export type FormTaskAssigneeInfoListFn = <T>(
    activityId: string,
    filterData: T,
) => FormTaskAssigneeInfo[];

export type GetFormTaskAssigneesFn = <T>(filterData: T) => FormTaskAssignees;
