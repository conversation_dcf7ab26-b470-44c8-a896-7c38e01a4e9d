import { CommonSearchParams } from '@/common/types/common.type';
import { FormFieldStatusItem } from '../../form/types/form.settings.type';

export type SearchFormConfFieldStatusParams = CommonSearchParams & {
    formId?: number;
    formIds?: number[];
    step?: string;
    formKey?: string;
};

export type FormFieldStatusesFn = <T>(
    activityId: string,
    filterData: T,
) => FormFieldStatusItem[];

export enum EnumCurrentTaskStatus {
    Processing = 'Processing',
    Finish = 'Finish',
}
