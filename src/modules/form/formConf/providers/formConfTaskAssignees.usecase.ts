import { Inject, Service } from 'typedi';
import { FormConfTaskAssigneesRepository } from './formConfTaskAssignees.repository';
import { FormConfTaskAssigneesService } from './formConfTaskAssignees.service';
import _ from 'lodash';
import {
    FormTaskAssigneeInfoListFn,
    GetFormTaskAssigneesFn,
} from '../types/formConfTaskAssignees.types';

@Service()
export class FormConfTaskAssigneesUseCase {
    @Inject()
    private formConfTaskAssigneesRepos: FormConfTaskAssigneesRepository;
    @Inject()
    private formConfTaskAssigneesSvc: FormConfTaskAssigneesService;

    /**
     * This function retrieves the GetFormTaskAssignees function for a specific form ID.
     * If no function is found, it returns an empty function that returns an empty object.
     * @param formId The ID of the form for which to retrieve the GetFormTaskAssignees function.
     * @returns A promise that resolves to the GetFormTaskAssignees function for the specified form ID.
     */
    async getGetFormTaskAssigneesFn(
        formId: number,
        formVersion: number = 1,
    ): Promise<GetFormTaskAssigneesFn> {
        const { rows } = await this.formConfTaskAssigneesRepos.search({
            formId,
            formVersion,
        });
        const getFormTaskAssigneesFn =
            this.formConfTaskAssigneesSvc.genGetFormTaskAssigneesFn(
                rows.sort((a, b) => a.step.localeCompare(b.step)),
            );
        return getFormTaskAssigneesFn;
    }

    /**
     * @deprecated
     * This function retrieves a map of form IDs to their corresponding FormTaskAssigneeInfo list functions.
     * @param formIds
     * @returns
     */
    private async getFormIdFormTaskAssigneeInfoListFnMap(
        formIds: number[],
    ): Promise<Map<number, FormTaskAssigneeInfoListFn>> {
        const formConfTaskAssigneesList =
            (await this.formConfTaskAssigneesRepos.findByFormIds(formIds)) ??
            [];
        const groupByFormIdTaskAssignees = _.groupBy(
            formConfTaskAssigneesList,
            (item) => item.formId,
        );

        return Object.entries(groupByFormIdTaskAssignees).reduce(
            (acc, [formId, taskAssignees]) => {
                const fn =
                    this.formConfTaskAssigneesSvc.genFormTaskAssigneeInfoListFn(
                        taskAssignees,
                    );
                acc.set(Number(formId), fn);
                return acc;
            },
            new Map<number, FormTaskAssigneeInfoListFn>(),
        );
    }

    /**
     * @deprecated
     * This function retrieves the FormTaskAssigneeInfoListFn for a specific form ID.
     * @param formId
     * @returns
     */
    private async getFormTaskAssigneeInfoListFn(
        formId: number,
    ): Promise<FormTaskAssigneeInfoListFn | null> {
        const formIdTaskAssigneeInfoListFnMap =
            await this.getFormIdFormTaskAssigneeInfoListFnMap([formId]);
        const formTaskAssigneeInfoListFn =
            formIdTaskAssigneeInfoListFnMap.get(formId);
        if (formTaskAssigneeInfoListFn) return formTaskAssigneeInfoListFn;
        return null;
    }
}
