import { FormConfFieldStatus } from '@/common/models/clinico/bpm/formConfFieldStatus.model';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { Service } from 'typedi';
import { SearchFormConfFieldStatusParams } from '../types/formConfFieldStatus.types';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { In } from 'typeorm';

@Service()
export class FormConfFieldStatusRepository extends CommonService<FormConfFieldStatus> {
    protected repo = ClinicoDataSource.getRepository(FormConfFieldStatus);

    async findByFormId(formId: number): Promise<FormConfFieldStatus[] | null> {
        const { rows } = await this.search({
            formId,
        });
        return rows || null;
    }
    async findByFormIds(
        formIds: number[],
    ): Promise<FormConfFieldStatus[] | null> {
        const { rows } = await this.search({
            formIds,
        });
        return rows || null;
    }

    async search(
        params: SearchFormConfFieldStatusParams,
    ): Promise<CommonSearchResult<FormConfFieldStatus>> {
        const { formId, step, formKey, ...searchParams } = params;
        const where: any = {
            deleted: false,
        };
        if (params.id) {
            where.id = params.id;
        }
        if (formId) {
            where.formId = formId;
        }
        if (step) {
            where.step = step;
        }
        if (formKey) {
            where.formKey = formKey;
        }
        if (params.formIds && params.formIds.length > 0) {
            where.formId = In(params.formIds);
        }

        const result = await this.repo.findAndCount({
            where,
            order: { updatedAt: 'DESC' },
            ...searchParams,
        });

        return {
            rows: result[0],
            count: result[1],
        };
    }
}
