import { Form } from '@/common/models/clinico/bpm/form.model';
import { Service } from 'typedi';
import _ from 'lodash';
import {
    FormTaskConfig,
    FormTaskMailToItem,
} from '../../form/types/form.taskSetting.type';
import { filterMappingMatchQueryData } from '@/common/helpers/filterMappingMatchQueryData';
import {
    EnumFormFieldStatus,
    EnumFormFieldStatusField,
    FormFieldStatusItem,
} from '../../form/types/form.settings.type';
import { FormConfFilterMetadata } from '@/common/models/clinico/bpm/formConfFilterMetadata.model';
import { FormConfFieldStatus } from '@/common/models/clinico/bpm/formConfFieldStatus.model';

@Service()
export class FormConfService {
    /**
     * form conf 資料處理
     *
     * 給 repo 使用
     * 依據原本在 form 中的 conf 設定，將其合併到 form 中，並且依照更新時間排序
     * @param form
     * @returns
     */
    genFormAllConf(form: Form) {
        this.genTaskSettingMailTo(form);
        this.genTaskSettingProperties(form);
        this.genTaskWebhook(form);
        return form;
    }

    genTaskWebhook(form: Form) {
        form.formConfTaskWebhooks =
            form.formConfTaskWebhooks?.filter((i) => !i.deleted) ?? [];
    }

    /**
     * 取得最終表單設定的表單狀態
     */
    getFormFieldStatusByActivityId(
        formFieldStatusesMap: Map<string, FormFieldStatusItem[]>,
        activityId: string,
    ): FormFieldStatusItem[] {
        const overrideFormFieldStatus =
            formFieldStatusesMap.get(EnumFormFieldStatusField.Override) ?? [];
        if (overrideFormFieldStatus.length > 0) {
            return overrideFormFieldStatus;
        }
        const formFieldStatus = formFieldStatusesMap.get(activityId) ?? [];
        return formFieldStatus;
    }

    genFormFieldStatusesMap<T>(data: FormConfFieldStatus[], filterData: T) {
        const stepFormKeyStatusesMap = data.reduce((pre, cur) => {
            const { step, formKey, formStatus, configFilter } = cur;
            const key = `${step}&${formKey}`;
            const stepValue = pre.get(key) || [];
            if (configFilter) {
                const isMatch =
                    filterMappingMatchQueryData(
                        [filterData],
                        [{ filter: configFilter }],
                    ).length > 0;
                if (!isMatch) return pre;
            }
            stepValue.push(formStatus);
            pre.set(key, stepValue);
            return pre;
        }, new Map<string, EnumFormFieldStatus[]>());
        return Array.from(stepFormKeyStatusesMap).reduce((pre, cur) => {
            const [keys, statuses] = cur;
            const [step, formKey] = keys.split('&');
            const stepValue = pre.get(step) || [];
            stepValue.push({
                key: formKey,
                statuses,
            });
            pre.set(step, stepValue);
            return pre;
        }, new Map<string, FormFieldStatusItem[]>());
    }

    getMatchFormConfFilterMetadatasByUserData(
        userData: any,
        formConfFilterMetadatas: FormConfFilterMetadata[],
        exData = {},
    ) {
        return formConfFilterMetadatas.filter(
            (i) =>
                filterMappingMatchQueryData(
                    [userData],
                    [{ filter: i.formFilter }],
                    exData,
                ).length > 0,
        );
    }

    genTaskSettingMailTo(form: Form) {
        const { formConfTaskMailToList } = form;
        if (!formConfTaskMailToList?.length) return form;
        const data = _.orderBy(
            formConfTaskMailToList.filter((i) => !i.deleted),
            ['updatedAt'],
            ['asc'],
        );
        if (!data.length) return form;
        const result = data.reduce((pre, cur) => {
            const { step, status, assignees, taskStatus, skipAssignees } = cur;
            const stepValue = pre.get(step) || [];
            const config: FormTaskMailToItem = {
                status,
                assignees,
                skipAssignees,
                taskStatus: taskStatus ?? [],
            };
            stepValue.push(config);
            pre.set(step, stepValue);
            return pre;
        }, new Map<string, FormTaskMailToItem[]>());
        if (!form.taskSettings) {
            form.taskSettings = {} as JSON;
        }
        const obj = Object.fromEntries(result);
        form.taskSettings['mailTo'] = obj;
    }

    genTaskSettingProperties(form: Form) {
        const { formConfTaskProperties } = form;
        if (!formConfTaskProperties?.length) return form;
        const data = _.orderBy(
            formConfTaskProperties.filter((i) => !i.deleted),
            ['updatedAt'],
            ['asc'],
        );
        if (!data.length) return form;
        const result = data.reduce((pre, cur) => {
            const { step, propertyKey, config } = cur;
            const stepValue = pre.get(step) || {};
            stepValue[propertyKey] = config;
            pre.set(step, stepValue);
            return pre;
        }, new Map<string, FormTaskConfig>());
        // 先延續使用原本的 taskSettings 位置，替換設定，後續有重構再調整
        if (!form.taskSettings) {
            form.taskSettings = {} as JSON;
        }
        // type FormTaskConfigs
        const obj = Object.fromEntries(result);
        form.taskSettings['configs'] = obj;
    }
}
