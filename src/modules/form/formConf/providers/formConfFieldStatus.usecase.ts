import { Inject, Service } from 'typedi';
import { FormConfFieldStatusRepository } from './formConfFieldStatus.repository';
import { FormConfService } from './formConf.service';
import _ from 'lodash';
import { FormFieldStatusItem } from '@/modules/form/form/types/form.settings.type';
import { FormFieldStatusesFn } from '../types/formConfFieldStatus.types';
import { FormConfFieldStatus } from '@/common/models/clinico/bpm/formConfFieldStatus.model';

@Service()
export class FormConfFieldStatusUseCase {
    @Inject()
    private formConfFieldStatusRepo: FormConfFieldStatusRepository;
    @Inject()
    private formConfSvc: FormConfService;

    /**
     * 取得表單 id 與 formFieldStatusesFn 的 map
     * @param formIds
     * @returns
     */
    async getFormIdFormFieldStatusesFnMap(formIds: number[]) {
        const formConfFieldStatusList =
            (await this.formConfFieldStatusRepo.findByFormIds(formIds)) ?? [];
        const groupByFormIdFieldStatus = _.groupBy(
            formConfFieldStatusList,
            (item) => item.formId,
        );
        const formIdFormFieldStatusesFnMap = new Map<
            number,
            FormFieldStatusesFn
        >();
        Object.entries(groupByFormIdFieldStatus).forEach(
            ([formId, fieldStatuses]) => {
                const fn = this.genFormFieldStatusesFn(fieldStatuses);
                formIdFormFieldStatusesFnMap.set(Number(formId), fn);
            },
        );
        return formIdFormFieldStatusesFnMap;
    }

    /**
     * 取得表單 id 與 formFieldStatusesFn 的 map
     * @param formId
     * @returns
     */
    async getFormFieldStatusesFnMap(formId: number) {
        const formIdFormFieldStatusesFnMap =
            await this.getFormIdFormFieldStatusesFnMap([formId]);
        const formFieldStatusesFn =
            formIdFormFieldStatusesFnMap.get(formId) ??
            ((activityId: string, filterData: any) => []);
        return formFieldStatusesFn;
    }

    /**
     * 取得表單 id 的 formFieldStatuses
     * @param formId
     * @param activityId
     * @param filterData
     * @returns
     */
    async getFormFieldStatusesByFormId(
        formId: number,
        activityId: string,
        filterData: any,
    ): Promise<FormFieldStatusItem[]> {
        const formFieldStatusesFn =
            await this.getFormFieldStatusesFnMap(formId);
        return formFieldStatusesFn(activityId, filterData);
    }

    /**
     * 回傳 formFieldStatusesFn 供外部即時配置 filterData 條件
     * @param formFieldStatuses
     * @returns
     */
    private genFormFieldStatusesFn(
        formFieldStatuses: FormConfFieldStatus[],
    ): FormFieldStatusesFn {
        return <T>(activityId: string, filterData: T) => {
            const formFieldStatusesMap =
                this.formConfSvc.genFormFieldStatusesMap(
                    formFieldStatuses,
                    filterData,
                );
            return this.formConfSvc.getFormFieldStatusByActivityId(
                formFieldStatusesMap,
                activityId,
            );
        };
    }
}
