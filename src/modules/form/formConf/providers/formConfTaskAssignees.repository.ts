import { FormConfTaskAssignee } from '@/common/models/clinico/bpm/formConfTaskAssignee.model';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { Service } from 'typedi';
import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { In } from 'typeorm';
import { SearchFormConfTaskAssigneesParams } from '../types/formConfTaskAssignees.types';
import _ from 'lodash';

@Service()
export class FormConfTaskAssigneesRepository extends CommonService<FormConfTaskAssignee> {
    protected repo = ClinicoDataSource.getRepository(FormConfTaskAssignee);

    async findByFormId(formId: number): Promise<FormConfTaskAssignee[] | null> {
        const { rows } = await this.search({
            formId,
        });
        return rows || null;
    }
    async findByFormIds(
        formIds: number[],
    ): Promise<FormConfTaskAssignee[] | null> {
        const { rows } = await this.search({
            formIds,
        });
        return rows || null;
    }

    async search(
        params: SearchFormConfTaskAssigneesParams,
    ): Promise<CommonSearchResult<FormConfTaskAssignee>> {
        const { formId, step, ...searchParams } = params;
        // Default form version 0 取得跨版本 formTaskAssignees
        const formVersions = [0];
        const where: any = {
            deleted: false,
        };
        if (params.id) {
            where.id = params.id;
        }
        if (formId) {
            where.formId = formId;
        }
        if (step) {
            where.step = step;
        }
        if (params.formIds && params.formIds.length > 0) {
            where.formId = In(params.formIds);
        }
        if (!_.isUndefined(params.formVersion)) {
            formVersions.push(params.formVersion);
        }
        where.formVersion = In(formVersions);

        const result = await this.repo.findAndCount({
            where,
            order: { updatedAt: 'DESC' },
            ...searchParams,
        });

        return {
            rows: result[0],
            count: result[1],
        };
    }
}
