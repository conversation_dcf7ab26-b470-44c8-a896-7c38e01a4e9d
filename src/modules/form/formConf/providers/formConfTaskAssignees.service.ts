import { FormConfTaskAssignee } from '@/common/models/clinico/bpm/formConfTaskAssignee.model';
import { Service } from 'typedi';
import {
    FormTaskAssigneeInfo,
    FormTaskAssignees,
} from '../../form/types/form.taskSetting.type';
import { filterMappingMatchQueryData } from '@/common/helpers/filterMappingMatchQueryData';
import {
    FormTaskAssigneeInfoListFn,
    GetFormTaskAssigneesFn,
} from '../types/formConfTaskAssignees.types';

@Service()
export class FormConfTaskAssigneesService {
    /**
     * Generates a function that retrieves FormTaskAssignees based on the provided formConfTaskAssignees.
     * @param formConfTaskAssignees An array of FormConfTaskAssignee objects.
     * @returns A function that takes filterData and returns FormTaskAssignees.
     */
    genGetFormTaskAssigneesFn(
        formConfTaskAssignees: FormConfTaskAssignee[],
    ): GetFormTaskAssigneesFn {
        return (filterData: any) => {
            const dataMap = this.genTaskAssigneesMap(
                formConfTaskAssignees,
                filterData,
            );
            const result: FormTaskAssignees = Array.from(
                dataMap.entries(),
            ).reduce((acc, [activityId, configs]) => {
                acc[activityId] = configs;
                return acc;
            }, {});
            return result;
        };
    }

    /**
     * @deprecated
     * Generates a function that retrieves a list of FormTaskAssigneeInfo based on the provided formConfTaskAssignees.
     * @param formConfTaskAssignees
     * @returns
     */
    genFormTaskAssigneeInfoListFn(
        formConfTaskAssignees: FormConfTaskAssignee[],
    ): FormTaskAssigneeInfoListFn {
        return (activityId: string, filterData: any) => {
            const dataMap = this.genTaskAssigneesMap(
                formConfTaskAssignees,
                filterData,
            );
            const configs = dataMap.get(activityId) ?? [];
            return configs;
        };
    }

    private genTaskAssigneesMap<T>(
        data: FormConfTaskAssignee[],
        filterData: T,
    ) {
        return data.reduce((pre, cur) => {
            const { step, config, configFilter } = cur;
            let isMatch = true;
            if (configFilter) {
                isMatch =
                    filterMappingMatchQueryData(
                        [filterData],
                        [{ filter: configFilter }],
                    ).length > 0;
            }
            const configs = pre.get(step) ?? [];
            if (isMatch) configs.push(config);
            pre.set(step, configs);
            return pre;
        }, new Map<string, FormTaskAssigneeInfo[]>());
    }
}
