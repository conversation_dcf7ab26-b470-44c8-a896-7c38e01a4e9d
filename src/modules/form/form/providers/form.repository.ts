import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import { Inject, Service } from 'typedi';
import { FindOptionsRelations, FindOptionsWhere, Like } from 'typeorm';
import { SearchParams } from '../types/form.type';
import { BaseError } from '@clinico/base-error';
import httpStatus from 'http-status';
import { FormExternalAssigneeValue } from '../types/form.assignees.type';
import { FormField } from '../types/form.settings.type';
import { FormConfService } from '@/modules/form/formConf/providers/formConf.service';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';

@Service()
export class FormRepository extends CommonService<Form> {
    protected repo = ClinicoDataSource.getRepository(Form);
    @Inject()
    private formConfSvc: FormConfService;

    async findOneByProcessKey(processKey: string): Promise<Form | null> {
        const result = await this.search({ processKey: processKey });
        const form = result.rows.length ? result.rows[0] : null;
        return form;
    }

    async findOneByCodeOrErrpr(code: string): Promise<Form> {
        const result = await this.search({ code });
        const allMatchCodeForms = result.rows.filter((i) => i.code == code);
        // 完全比對
        if (allMatchCodeForms.length > 0) {
            return allMatchCodeForms[0];
        }
        const form = result.rows.length ? result.rows[0] : null;
        if (!form) {
            throw new BaseError('From not found', httpStatus.NOT_FOUND);
        }
        return form;
    }

    async findAllByRegionId(regionId: number): Promise<Form[]> {
        const result = await this.search({ regionId: regionId });
        return result.rows;
    }

    async findOneFormExternalAssigneesByKeyId(
        id: number,
        key: string,
    ): Promise<FormExternalAssigneeValue[]> {
        const filters: FindOptionsWhere<Form> = {
            id,
        };
        const sql = `
        SELECT 
            externalAssignees.values AS "formAssignees"
        FROM
            "bpm"."forms" "f"
            , jsonb_to_recordset(f.form_assignees -> 'externalAssignees') as externalAssignees(key text, values jsonb)
        WHERE
            "f"."id" = $1
            AND
            externalAssignees.key = $2;
        `;
        const data = await this.repo.query(sql, [id, key]);
        if (!data || !data[0] || !data[0].formAssignees) {
            return [];
        }

        const externalAssignees: FormExternalAssigneeValue[] =
            data[0].formAssignees;
        return externalAssignees;
    }

    async findOneFormFieldsById(id: number): Promise<FormField[]> {
        const form = await this.findOneOrError(id);
        if (form && form.formSettings) {
            const formFields: FormField[] = form.formSettings['formFields'];
            if (!formFields) return [];
            return formFields;
        }
        return [];
    }

    /**
     * 給查詢使用的 form 清單，無視權限
     * 取得有簽核過的關聯表單
     * @param userId
     * @returns
     */
    async findByInstanceTaskUserId(userId: number): Promise<Form[]> {
        const formInstanceTaskFilter: FindOptionsWhere<FormInstanceTask> = {
            userId,
        };
        const data = await this.repo.find({
            where: {
                formInstances: {
                    formInstanceTasks: formInstanceTaskFilter,
                },
            },
        });
        return data;
    }

    async search(
        params: SearchParams,
        relations: FindOptionsRelations<Form> = {
            formConfFilterMetadatas: true,
            formConfTaskMailToList: true,
            formConfTaskWebhooks: true,
            formConfTaskProperties: true,
            // formConfTaskAssignees: true,
        },
    ): Promise<CommonSearchResult<Form>> {
        const filters: FindOptionsWhere<Form> = {
            deleted: false,
        };
        if (params.id) {
            filters.id = params.id;
        }
        if (params.regionId) {
            filters.regionId = params.regionId;
        }
        if (params.name) {
            filters.name = Like(`%${params.name}%`);
        }
        if (params.code) {
            filters.code = Like(`%${params.code}%`);
        }
        if (params.processKey) {
            filters.processKey = params.processKey;
        }

        const data = await this.repo.findAndCount({
            relations,
            where: filters,
            take: params.limit,
            skip: params.offset,
            order: {
                id: 'DESC',
            },
        });

        const result: CommonSearchResult<Form> = {
            rows: data[0].map((i) => this.formConfSvc.genFormAllConf(i)),
            count: data[1],
        };
        return result;
    }
}
