import { Inject, Service } from 'typedi';
import { FormRepository } from './form.repository';
import { UserService } from '@/modules/organization/providers/user.service';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { ProcessInstanceUseCase } from '@/modules/camunda/providers/processInstance.usecase';
import { FormService } from './form.service';
import { UserPayload } from '../../../organization/types/user.type';
import { UserUseCase } from '@/modules/organization/providers/user.usecase';
import { FormConfService } from '@/modules/form/formConf/providers/formConf.service';
import { TaskAssigneeService } from '@/modules/camunda/providers/taskAssignee/taskAssignee.service';

@Service()
export class FormUseCase {
    @Inject()
    private svc: FormService;
    @Inject()
    protected repo: FormRepository;
    @Inject()
    protected processInstanceUCase: ProcessInstanceUseCase;
    @Inject()
    private userService: UserService;
    @Inject()
    private userUCase: UserUseCase;
    @Inject()
    private formConfSvc: FormConfService;
    @Inject()
    private taskAssigneeSvc: TaskAssigneeService;

    /**
     * 根據使用者取得篩選的表單
     *
     * 使用 filterMappingMatchQueryData 來過濾表單
     * 基礎能篩選資訊有
     *  - 使用者資料
     *  - 是否為超級使用者
     *  - 使用者權限表
     *  - 使用者部門 id 清單
     *  - 使用者部門 code 清單
     *  - 使用者公司 id 清單
     *  - 使用者公司 code 清單
     *  - 使用者區域 id 清單
     *
     * 外部 config 使用 data.${dataKey} 來取得使用者篩選資料來作為條件
     *
     * @param userPayload 使用者資料
     * @returns
     */
    async getAllFormByUser(userPayload: UserPayload) {
        const forms = await this.repo.findAll({
            formConfFilterMetadatas: true,
        });
        const userData = await this.userUCase.getFilterUserInfoById(
            userPayload.id,
        );

        const filterForms = forms.filter((form) => {
            const { formConfFilterMetadatas } = form;
            const activeFilterMetadata = formConfFilterMetadatas?.filter(
                (i) => !i.deleted,
            );
            if (!activeFilterMetadata?.length) return true;
            const matchs =
                this.formConfSvc.getMatchFormConfFilterMetadatasByUserData(
                    userData,
                    activeFilterMetadata,
                );
            return matchs.length > 0;
        });
        return filterForms;
    }

    /**
     * 取得符合個人的表單篩選設定
     * 提供給 assignees 使用
     * @param id
     * @param userId
     * @returns
     */
    async getMatchUserFormConfFilterMetadatas(id: number, userId: number) {
        const userData = await this.userUCase.getFilterUserInfoById(userId);
        const { formConfFilterMetadatas } = await this.repo.findOneOrError(id, {
            formConfFilterMetadatas: true,
        });
        const activeFilterMetadata = formConfFilterMetadatas?.filter(
            (i) => !i.deleted,
        );
        if (!activeFilterMetadata?.length) return null;
        const matchs =
            this.formConfSvc.getMatchFormConfFilterMetadatasByUserData(
                userData,
                activeFilterMetadata,
            );
        return matchs;
    }

    async findCamundaFormByForm(form: Form) {
        return this.processInstanceUCase.getCamundaFormByProcessKey(
            form.processKey,
        );
    }

    async findOneDefinitionXMLById(id: number): Promise<string | null> {
        const form = await this.repo.findOneOrError(id);
        return this.processInstanceUCase.findOneDefinitionXML({ form });
    }

    async allowReadByFormTaskMailTo(formId: number, userId: number) {
        const allowRead = false;
        const form = await this.repo.findOneOrError(formId);
        const taskMailTo = this.svc.getTaskMailTo(form);
        if (!taskMailTo) return allowRead;
        const userCodes = Object.values(taskMailTo).reduce(
            (pre, cur) => {
                for (const formTaskMailToItem of cur) {
                    const { assignees } = formTaskMailToItem;
                    if (!assignees) continue;
                    for (const formTaskAssigneeInfo of assignees) {
                        const { type, specifyUser } = formTaskAssigneeInfo;
                        if (!specifyUser) continue;
                        const formSpecifyUsers =
                            this.taskAssigneeSvc.transformFormSpecifyUsers(
                                specifyUser,
                            );
                        const formUserCodes = formSpecifyUsers.map(
                            (i) => i.user_code,
                        );
                        pre.push(...formUserCodes);
                    }
                }
                return pre;
            },
            <string[]>[],
        );
        if (userCodes.length == 0) return false;
        const user = await this.userService.findOneOrError(userId);
        return userCodes.includes(user.code);
    }
}
