import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'mali';
import Container from 'typedi';
import { GRPCForm, SearchFormParams } from '../types/form.grpc.type';
import { FormRepository } from './form.repository';

export const Form = {
    search: async (ctx: Context<any>) => {
        const params = <SearchFormParams>ctx.req;
        const formRepo = Container.get(FormRepository);
        const forms = await formRepo.search(params);
        const data = forms.rows.map(
            (data) =>
                <GRPCForm>{
                    id: data.id,
                    name: data.name,
                    code: data.code,
                    processKey: data.processKey,
                },
        );
        const res = Helpers.Json.success(data, forms.count);
        ctx.res = res;
    },
};
