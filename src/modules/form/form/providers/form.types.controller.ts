import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { Helpers } from '@clinico/clinico-node-framework';
import { Get, JsonController, UseBefore } from 'routing-controllers';
import { Service } from 'typedi';
import {
    EnumSealCategory,
    EnumSealType,
} from '../types/form.applyDocumentSeal.type';
import { EnumApprovalCategory } from '../types/form.approval.type';

@JsonController('/forms/types')
@UseBefore(UserAuthKoaInterceptor)
@Service()
export class FormApplyDocumentSealController {
    @Get('/approval/categories')
    async approvalCategories() {
        const categories: {
            key: string;
            value: string;
        }[] = [];
        for (const key of Object.keys(EnumApprovalCategory))
            categories.push({
                key,
                value: EnumApprovalCategory[key],
            });

        return Helpers.Json.success(categories);
    }

    @Get('/seal/categories')
    async applyDocumentSealCategories() {
        const categories: {
            key: string;
            value: string;
        }[] = [];
        for (const key of Object.keys(EnumSealCategory))
            categories.push({
                key,
                value: EnumSealCategory[key],
            });

        return Helpers.Json.success(categories);
    }

    @Get('/seal/types')
    async applyDocumentSealTypes() {
        const categories: {
            key: string;
            value: string;
        }[] = [];
        for (const key of Object.keys(EnumSealType))
            categories.push({
                key,
                value: EnumSealType[key],
            });

        return Helpers.Json.success(categories);
    }
}
