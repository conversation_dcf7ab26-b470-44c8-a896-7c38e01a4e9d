import { Service } from 'typedi';
import {
    FormTaskConfig,
    FormTaskConfigs,
    FormTaskMailTo,
    FormTaskMailToItem,
    FormTaskMailToTask,
} from '../types/form.taskSetting.type';
import { Form } from '@/common/models/clinico/bpm/form.model';

@Service()
export class FormService {
    getTaskConfigsByActivityId(
        form: Form,
        activityId: string,
    ): FormTaskConfig | null {
        const configs = this.getTaskConfigs(form);
        if (!configs) return null;
        if (!(activityId in configs)) return null;
        return configs[activityId];
    }

    getTaskConfigs(form: Form): FormTaskConfigs | null {
        if (form && form.taskSettings) {
            const configs: FormTaskConfigs = form.taskSettings['configs'];
            if (!configs) return null;
            return configs;
        }
        return null;
    }

    /* mailTo */
    getTaskMailTo(form: Form): FormTaskMailTo | null {
        if (form && form.taskSettings) {
            const result: FormTaskMailTo = form.taskSettings['mailTo'];
            if (!result) return null;
            return result;
        }
        return null;
    }

    getTaskMailToByActivityId(
        form: Form,
        activityId: FormTaskMailToTask,
    ): FormTaskMailToItem[] | null {
        const data = this.getTaskMailTo(form);
        if (!data) return null;
        if (!(activityId in data)) return null;
        return data[activityId];
    }
}
