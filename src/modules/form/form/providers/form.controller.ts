import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { Helpers } from '@clinico/clinico-node-framework';
import {
    Ctx,
    Get,
    JsonController,
    Param,
    UseBefore,
} from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { FormUseCase } from './form.usecase';
import { FormRepository } from './form.repository';
import { Context } from 'koa';
import { UserPayload } from '@/modules/organization/types/user.type';
import { FormConfFieldStatusUseCase } from '@/modules/form/formConf/providers/formConfFieldStatus.usecase';
import { UserUseCase } from '@/modules/organization/providers/user.usecase';
import { EnumFormFieldStatusField } from '../types/form.settings.type';

@JsonController('/forms')
@UseBefore(UserAuthKoaInterceptor)
@Service()
export class FormController {
    @Inject()
    private usecase: FormUseCase;
    @Inject()
    private repo: FormRepository;
    @Inject()
    private formConfFieldStatusUCase: FormConfFieldStatusUseCase;
    @Inject()
    private userUCase: UserUseCase;

    @Get('/')
    async findAll(@Ctx() ctx: Context) {
        const userPayload: UserPayload = ctx.req['user'];
        const forms = await this.usecase.getAllFormByUser(userPayload);
        return Helpers.Json.success(forms);
    }

    @Get('/:id')
    async findOne(@Param('id') id: number) {
        const form = await this.repo.findOne(id);
        return Helpers.Json.success(form);
    }

    @Get('/:id/xml')
    async findOneDefinitionXMLById(@Param('id') id: number) {
        const xml = await this.usecase.findOneDefinitionXMLById(id);
        return Helpers.Json.success(xml);
    }

    @Get('/:id/fieldStatus')
    async findOneFormFieldStatusById(
        @Param('id') id: number,
        @Ctx() ctx: Context,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        const userInfo = await this.userUCase.getFilterUserInfoById(
            userPayload.id,
        );
        const filterData = {
            userInfo,
            currentTaskStatus: null,
        };
        const result =
            await this.formConfFieldStatusUCase.getFormFieldStatusesByFormId(
                id,
                EnumFormFieldStatusField.Initiator,
                filterData,
            );
        return Helpers.Json.success(result);
    }

    @Get('/:id/fields')
    async findOneFormFieldsById(@Param('id') id: number) {
        const result = await this.repo.findOneFormFieldsById(id);
        return Helpers.Json.success(result);
    }
}
