export type FormExternalAssignees = {
    key: string;
    values: FormExternalAssigneeValue[];
};

export type FormExternalAssigneeValue = {
    nodes: FormExternalNode[];
    assignees: FormExternalAssignee[];
};

export type FormExternalNode = {
    key: string;
    name?: string;
    value: string;
};

export type FormExternalAssignee = {
    name: string;
    values: FormExternalAssigneeUser[];
};

export type FormExternalAssigneeUser = {
    prefix?: string;
    suffix?: string;
    userCode?: string;
    [key: string]: any;
};
