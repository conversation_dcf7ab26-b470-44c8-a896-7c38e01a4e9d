import {
    AssigneeUser,
    SpecifyUser,
} from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { Variables } from '@/modules/camunda/types/processInstance.type';
import { EnumMailToStatus, EnumTaskStatusStr } from './form.settings.type';
import { InstancesValuesData } from '../../formInstance/types/formInstance.type';
import { Assignee } from '@/modules/assignee/types/assignee.type';

export enum EnumExtraAssigneeType {
    PrefixFormDirectSupervisor = 'prefix_form_direct_supervisor',
    PrefixFormDepartmentHead = 'prefix_form_department_head',
}

export enum EnumFormTaskAssigneeType {
    /**
     * table form.task_settings 預先指定人員
     */
    SpecifyUser = 'specify_user',
    /**
     * 指定人員的簽核 直屬主管
     */
    PrefixFormDirectSupervisor = 'prefix_form_direct_supervisor',
    /**
     * 指定人員的簽核 部門主管
     */
    PrefixFormDepartmentHead = 'prefix_form_department_head',
    /**
     * 表單指定簽核人員
     * user_str / user_str[]
     */
    PrefixFormSpecifyUser = 'prefix_form_specify_user',
    /**
     * 指定某關關卡人員
     * {
     *    type: "step"
     *    key: "Activity_Step1";
     * }
     */
    Step = 'step',
}

/**
 * camunda element variable name
 * 這些變數會在 camunda 的 task 中產生
 */
export enum EnumElementVariableName {
    /**
     * 會簽關卡人員(multi-instance產生)
     */
    User = 'user',
    /**
     * table form.task_settings 預先指定人員
     */
    SpecifyUser = 'specify_user',
}

/**
 * 新舊 userCode,user_code 要相容...
 */
export type FormTaskSpecifyUser = {
    filterBy?: string;
    dept_code?: string;
    user_code?: string;
    deptCode?: string;
    departmentCode?: string;
    userCode?: string;
};

/**
 * 用原本的 assignee type enum
 */
// export type FormTaskAssigneeType = `${EnumFormTaskAssigneeType}`;

/* taskSettings assignee */

export type FormTaskAssigneeInfo = {
    key?: string;
    filterKey?: string;
    type: EnumFormTaskAssigneeType;
    specifyUser?: FormTaskSpecifyUser[];
};

export type FormTaskAssignees = {
    [activityId: string]: FormTaskAssigneeInfo[];
};

/* taskSettings configs */

/**
 * 原先是想說用 string type, 去動態生成
 */
export type FormTaskConfigKey = string;

export type FormTaskConfigInfoValue =
    | string
    | string[]
    | number
    | number[]
    | boolean
    | boolean[];

export type FormTaskConfigInfoType =
    | 'value'
    | 'assignee'
    | 'taskAssigneeFilter';

export type FormTaskConfigInfo = {
    type: FormTaskConfigInfoType;
    value?: FormTaskConfigInfoValue | FormTaskAssigneeInfo[];
    /**
     * 過濾簽核人員的 key
     * 用於過濾指定人員的簽核人員
     * 當 type 為 filterTaskAssignee 時使用
     */
    taskAssigneeFilterKey?: string;
};

export type FormTaskConfig = {
    [key in FormTaskConfigKey]: FormTaskConfigInfo;
};

export type FormTaskConfigs = {
    [activityId: string]: FormTaskConfig;
};

/* mail to */
export type FormTaskMailToItem = {
    taskStatus?: EnumTaskStatusStr[];
    status?: EnumMailToStatus;
    assignees?: FormTaskAssigneeInfo[];
    skipAssignees?: FormTaskAssigneeInfo[];
};

export type FormTaskMailToTask = 'End' | string;

export type FormTaskMailTo = {
    [activity_id: FormTaskMailToTask]: FormTaskMailToItem[];
};

/* function object */

export type FormTaskAssigneeProperty = {
    assigneeType?: EnumFormTaskAssigneeType; // 簽核對像類型
    specifyUser?: SpecifyUser;
    assignee?: Assignee | null;
};

export type GetFormTaskAssigneeProperty = {
    [key in EnumFormTaskAssigneeType]: (
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        variables: Variables,
        formTaskAssignees: FormTaskAssignees,
    ) => Promise<FormTaskAssigneeProperty>;
};

export type GetAssigneeUsers = {
    [key in EnumFormTaskAssigneeType]: (
        taskAssigneeInfo: FormTaskAssigneeInfo,
        formValue: InstancesValuesData,
        formTaskAssignees: FormTaskAssignees,
    ) => Promise<AssigneeUser[]>;
};
