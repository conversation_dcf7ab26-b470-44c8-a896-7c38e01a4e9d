export enum EnumSealCategory {
    /* 印鑑 */
    Seal = 'seal',
    /* 便章 */
    Simple = 'simple',
    /* 專用章 */
    Special = 'special',
    /* 戳記 */
    Stamp = 'stamp',
    /* 電子簽章 */
    ElectronicSignature = 'electronicSignature',
}

export enum EnumSealType {
    /** 大章 */
    OfficialSeal = 'officialSeal',
    /** 小章 */
    PersonalSeal = 'personalSeal',
    /** 騎縫章 */
    PagingSeal = 'pagingSeal',
    /** 戳記 */
    StampSeal = 'stampSeal',
}
