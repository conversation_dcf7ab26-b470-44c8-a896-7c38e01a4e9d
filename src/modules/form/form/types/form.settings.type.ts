export type FormField = {
    key: string;
    name: string;
    fieldType: FormFieldType;
    dataType: FormFieldDataType;
    value: string | number | FormFieldValue[];
    output: FormFieldDataType;
    require: boolean;
    filterBy?: FormFieldFilterBy | null;
};

export type FormFieldValue = {
    value: string | number;
    name: string;
    filterBy?: FormFieldFilterBy;
};

export enum FormFieldType {
    Input = 'input',
    Select = 'select',
    Radio = 'radio',
    AssigneeInitiator = 'assignee_initiator',
    AssigneeSupervisors = 'assignee_supervisors',
    AssigneeUsers = 'assignee_users',
    AssigneeDirectSupervisors = 'assignee_directSupervisors',
}

export enum FormFieldDataType {
    String = 'String',
    Number = 'Number',
    Array = 'Array',
    File = 'File',
}

export type FormFieldFilterBy = {
    key: string;
    values: string[] | number[];
};

export enum EnumTaskStatusStr {
    Approved = 'approved',
    Rejected = 'rejected',
    Completed = 'completed', // 結束
    AdditionalDocs = 'additionalDocs',
    AddAssignee = 'addAssignee',
    Reconsidered = 'reconsidered', //再議
    Return = 'return', //退回指定task
    // Completed = 'completed', //或簽完成時，其他task改為完成
}

export enum EnumMailToTaskType {
    /**
     * default
     */
    Current = 'current',
    Next = 'next',
}

export enum EnumMailToStatus {
    Send = 'send',
    Reject = 'reject',
    Complete = 'complete', // 結束
    AdditionalDocs = 'additionalDocs',
    AddAssignee = 'addAssignee',
    /**
     * default
     */
    Save = 'save',
}

export enum EnumFormFieldStatus {
    EDITOR = 'editor',
    DISABLED = 'disabled',
    OPTIONAL = 'optional',
}

export enum EnumFormFieldStatusField {
    // form field 狀態設定，為影響所有欄位
    Override = 'override', // 複蓋全部欄位

    Finish = 'finish', // 完成

    Initiator = 'initiator', // 發起人
}

export type FormFieldStatusItem = {
    key: string;
    statuses: EnumFormFieldStatus[];
};

export type FormFieldStatus = {
    [activity_id: string]: FormFieldStatusItem[];
};
