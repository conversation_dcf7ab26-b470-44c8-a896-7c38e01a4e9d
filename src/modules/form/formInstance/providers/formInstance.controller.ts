import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';
import { CommonSearchPageParams } from '@/common/types/common.controller.type';
import { UserPayload } from '@/modules/organization/types/user.type';
import { Helpers } from '@clinico/clinico-node-framework';
import { Context } from 'koa';
import {
    Body,
    BodyParam,
    Ctx,
    Delete,
    Get,
    JsonController,
    Param,
    Post,
    Put,
    QueryParams,
    UseBefore,
} from 'routing-controllers';
import { OpenAPI } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import {
    SearchParams,
    CreateParams,
    ApproveTaskParams,
    RejectTaskParams,
    CancelTaskParams,
    SearchFormInstanceResult,
    MessageParams,
    UpdateMessageParams,
    ReturnTaskParams,
    SearchTaskParams,
    AdditionalDocsTaskParams,
    SearchBody,
    SearchReturnTaskParams,
    EditTaskParams,
} from '../types/forminstance.controller.type';
import { OpenAPIParams } from '../../../../common/types/common.openapi.type';
import { FormInstanceUseCase } from './formInstance.usecase';
import {
    ApproveParams,
    EnumTaskStatus,
} from '../../formInstanceTask/types/formInstanceTask.type';
import { FormInstanceRepository } from './formInstance.repository';
import { FormInstanceTaskUseCase } from '../../formInstanceTask/providers/formInstanceTask.usecase';
import { FormInstanceTaskRepository } from '../../formInstanceTask/providers/formInstanceTask.repository';
import { FormInstanceTaskService } from '../../formInstanceTask/providers/formInstanceTask.service';
import { FormInstanceService } from './formInstance.service';
import _ from 'lodash';
import { PermissionUseCase } from '@/modules/permission/providers/permission.ucase';
import { EnumInstanceStatus } from '../types/formInstance.type';
import { CurrentTaskUseCase } from '../../formInstanceTask/providers/currentTask.usecase';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';

@JsonController('/form/instances')
@UseBefore(UserAuthKoaInterceptor)
@Service()
export class FormInstanceController {
    @Inject()
    private usecase: FormInstanceUseCase;
    @Inject()
    private svc: FormInstanceService;
    @Inject()
    private repo: FormInstanceRepository;
    @Inject()
    private formInstanceTaskUCase: FormInstanceTaskUseCase;
    @Inject()
    private formInstanceTaskRepo: FormInstanceTaskRepository;
    @Inject()
    private permissionUCase: PermissionUseCase;
    @Inject()
    private formInstanceTaskSvc: FormInstanceTaskService;
    @Inject()
    private currentTaskUCase: CurrentTaskUseCase;

    @Get('/:id')
    async findOne(@Ctx() ctx: Context, @Param('id') id: number) {
        const userPayload: UserPayload = ctx.req['user'];
        const result = await this.usecase.getFormInstanceResultById(
            id,
            userPayload,
        );
        return Helpers.Json.success(result);
    }

    @OpenAPI(OpenAPIParams.findAllTasks)
    @Get('/:id/tasks')
    async findAllTasks(
        @Param('id') id: number,
        @QueryParams() params: CommonSearchPageParams,
    ) {
        const tasks = await this.formInstanceTaskRepo.search({
            ...params,
            formInstanceId: id,
        });

        return Helpers.Json.success(tasks);
    }

    @OpenAPI(OpenAPIParams.findAllTasks)
    @Get('/:id/definition/tasks')
    async findAllDefinitionTasks(
        @Param('id') id: number,
        @QueryParams() params: SearchTaskParams,
    ) {
        const tasks = await this.formInstanceTaskRepo.search({
            ...params,
            formInstanceId: id,
        });
        const result = this.formInstanceTaskSvc.groupingTasks(
            tasks.rows,
            params.previous,
        );
        return Helpers.Json.success(result);
    }

    @Get('/:id/preview/tasks-assignees')
    async findAllPreviewTasks(@Param('id') id: number) {
        const data = await this.usecase.previewTasksAssigneesById(id);
        return Helpers.Json.success(data);
    }

    @Get('/:id/return-definition/tasks/:taskId')
    @UseBefore(UserAuthKoaInterceptor)
    async findReturnDefinitionTasks(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Param('taskId') taskId: number,
        @QueryParams() params: SearchReturnTaskParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        const result =
            await this.formInstanceTaskUCase.findReturnDefinitionTasks(
                userPayload.id,
                id,
                taskId,
                params.isSuperUser ?? false,
                params,
            );
        return Helpers.Json.success(result);
    }

    @Post('/:id/tasks/:taskId/addAssignee')
    async addAssignee(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Param('taskId') taskId: number,
        @BodyParam('departmentId') departmentId: number,
        @BodyParam('userId') userId: number,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        const task = await this.formInstanceTaskUCase.addAssignee({
            id: taskId,
            departmentId,
            userId,
            updatedUserId: userPayload.id,
        });

        return Helpers.Json.success(task);
    }

    @OpenAPI(OpenAPIParams.approveTask)
    @Post('/:id/tasks/:taskId/approve')
    async approveTask(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Param('taskId') taskId: number,
        @Body() params: ApproveTaskParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        this.convertParam(params);
        const task = await this.formInstanceTaskUCase.approve({
            id: taskId,
            values: params.values,
            memo: params.memo,
            updatedUserId: userPayload.id,
            attachmentS3Keys: params.attachmentS3Keys,
        });

        return Helpers.Json.success(task);
    }

    @Post('/:id/tasks/:taskId/edit')
    async editTask(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Param('taskId') taskId: number,
        @Body() params: EditTaskParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        this.convertParam(params);

        await this.usecase.message({
            id,
            message: params.message ?? '表單異動',
            taskId: taskId,
            values: params.values,
            memo: params.memo,
            attachmentS3Keys: params.attachmentS3Keys,
            updateUserId: userPayload.id,
        });
        const current = await this.formInstanceTaskRepo.findOneOrError(taskId);
        const task: {
            current: FormInstanceTask;
            next: FormInstanceTask[];
        } = {
            current,
            next: [],
        };
        return Helpers.Json.success(task);
    }

    @OpenAPI(OpenAPIParams.rejectTask)
    @Post('/:id/tasks/:taskId/reject')
    async rejectTask(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Param('taskId') taskId: number,
        @Body() params: RejectTaskParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        this.convertParam(params);
        const task = await this.formInstanceTaskUCase.reject({
            id: taskId,
            values: params.values,
            memo: params.memo,
            updatedUserId: userPayload.id,
        });

        return Helpers.Json.success(task);
    }

    /**
     * 關卡附件上傳
     */
    @OpenAPI(OpenAPIParams.additionalDocsTask)
    @Post('/:id/tasks/:taskId/additionalDocs')
    async additionalDocsTask(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Param('taskId') taskId: number,
        @Body() params: AdditionalDocsTaskParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        this.convertParam(params);
        const task = await this.formInstanceTaskUCase.additionalDocs({
            id: taskId,
            values: params.values,
            attachmentS3Keys: params.attachmentS3Keys,
            updatedUserId: userPayload.id,
        });
        return Helpers.Json.success(task);
    }

    @Post('/:id/tasks/:taskId/return')
    async returnTask(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Param('taskId') taskId: number,
        @Body() params: ReturnTaskParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        this.convertParam(params);
        const task = await this.formInstanceTaskUCase.return({
            id: taskId,
            processTaskId: params.processTaskId,
            values: params.values,
            memo: params.memo,
            updatedUserId: userPayload.id,
        });

        return Helpers.Json.success(task);
    }

    @Post('/:id/message')
    async message(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Body() params: MessageParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        this.convertParam(params);
        const formInstance = await this.usecase.message({
            id,
            message: params.message,
            taskId: params.taskId,
            values: params.values,
            memo: params.memo,
            attachmentS3Keys: params.attachmentS3Keys,
            updateUserId: userPayload.id,
        });
        return Helpers.Json.success(formInstance);
    }

    @Put('/:id/message/:messageId')
    async updateMessage(
        @Ctx() ctx: Context,
        @Param('id') id: number,
        @Param('messageId') messageId: number,
        @Body() params: UpdateMessageParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        const formInstanceMessage = await this.usecase.updateMessage({
            id,
            messageId,
            message: params.message,
            updateUserId: userPayload.id,
        });
        return Helpers.Json.success(formInstanceMessage);
    }

    @Post('/search')
    async searchMany(@Body() body: SearchBody, @Ctx() ctx: Context) {
        const userPayload: UserPayload = ctx.req['user'];
        const result = await this.usecase.searchByUserPayload(
            body,
            userPayload,
        );
        return Helpers.Json.success(result);
    }

    @OpenAPI(OpenAPIParams.searchInstance)
    @Get('/')
    async search(@QueryParams() params: SearchParams, @Ctx() ctx: Context) {
        const userPayload: UserPayload = ctx.req['user'];
        let result: SearchFormInstanceResult | null = null;
        result = await this.repo.search({
            ...params,
            taskUserid: userPayload.id,
        });
        result = await this.currentTaskUCase.genAllCurrentTasksByInstanceResult(
            result,
            userPayload,
        );
        return Helpers.Json.success(result);
    }

    /**
     * 待簽核清單
     */
    @OpenAPI(OpenAPIParams.currentAssigned)
    @Get('/current/assigned')
    async currentAssigned(
        @QueryParams() params: CommonSearchPageParams,
        @Ctx() ctx: Context,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        const result: SearchFormInstanceResult = await this.repo.search({
            ...params,
            status: EnumInstanceStatus.Waiting,
            currentAssignedUserid: userPayload.id,
        });
        result.rows = result.rows.map((row) => {
            row.allCurrentTasks = row.formInstanceTasks.filter(
                (data) =>
                    data.status == EnumTaskStatus.Waiting ||
                    data.status == EnumTaskStatus.Reconsidered,
            );
            const userTaskStatus = row.allCurrentTasks.filter(
                (i) => i.userId == userPayload.id,
            )[0];
            row['taskStatus'] =
                userTaskStatus.status ??
                row.allCurrentTasks[0].status ??
                row.status;
            return row;
        });
        return Helpers.Json.success(result);
    }

    @Post('/current/assigned/search')
    async searchCurrentAssigned(@Body() body: SearchBody, @Ctx() ctx: Context) {
        const userPayload: UserPayload = ctx.req['user'];
        const formInstanceParams = body.formInstanceParams || {};
        formInstanceParams.status = EnumInstanceStatus.Waiting;
        formInstanceParams.currentAssignedUserid = userPayload.id;
        const result = await this.usecase.searchByUserPayload(
            {
                ...body,
                formInstanceParams,
            },
            userPayload,
        );
        return Helpers.Json.success(result);
    }

    @OpenAPI(OpenAPIParams.createInstance)
    @Post('/')
    async create(@Body() params: CreateParams, @Ctx() ctx: Context) {
        const userPayload: UserPayload = ctx.req['user'];
        this.convertParam(params);
        const instance = await this.usecase.createFormInstance({
            ...params,
            ownerUserId: userPayload.id,
            createdUserId: userPayload.id,
        });
        return Helpers.Json.success(instance);
    }

    @Delete('/:id')
    async delete(@Param('id') id: number, @Ctx() ctx: Context) {
        const userPayload: UserPayload = ctx.req['user'];
        const instance = await this.usecase.deleteFormInstance({
            id: id,
            updateUserId: userPayload.id,
        });
        return Helpers.Json.success(instance);
    }

    @Post('/cancel/:id')
    async cancel(
        @Param('id') id: number,
        @Ctx() ctx: Context,
        @Body() params: CancelTaskParams,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        this.convertParam(params);
        const instance = await this.usecase.cancelFormInstance({
            id: id,
            values: params.values,
            memo: params.memo,
            updateUserId: userPayload.id,
        });
        return Helpers.Json.success(instance);
    }

    @Get('/:id/xml')
    async findOneDefinitionXMLById(@Param('id') id: number) {
        const xml = await this.usecase.findOneDefinitionXMLById(id);
        return Helpers.Json.success(xml);
    }

    private convertParam(params: ApproveParams | ApproveTaskParams) {
        if (params.values && _.isString(params.values)) {
            params.values = JSON.parse(params.values);
        }
    }
}
