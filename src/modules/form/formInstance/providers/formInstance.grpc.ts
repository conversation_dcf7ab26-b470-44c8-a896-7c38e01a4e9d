import { Context } from 'mali';
import {
    GRPCCreateFormInstanceParams,
    GRPCFormInstance,
} from '../types/formInstance.grpc.type';
import moment from 'moment';
import { InternalFormInstanceService } from '../../inernalFormInstance/providers/internalFormInstance.service';
import Container from 'typedi';

export const FormInstance = {
    create: async (ctx: Context<any>) => {
        const params = <GRPCCreateFormInstanceParams>ctx.req;
        const internalFormInstanceService = Container.get(
            InternalFormInstanceService,
        );
        const instance = await internalFormInstanceService.create(params);
        const data = <GRPCFormInstance>{
            ...instance,
            startDate: instance.startDate
                ? { seconds: moment(instance.startDate).unix() }
                : undefined,
            finishDate: instance.finishDate
                ? { seconds: moment(instance.finishDate).unix() }
                : undefined,
            values: instance.values
                ? JSON.stringify(instance.values)
                : undefined,
            createdAt: { seconds: moment(instance.createdAt).unix() },
            updatedAt: instance.updatedAt
                ? { seconds: moment(instance.updatedAt).unix() }
                : undefined,
            ownerCompanyCode: instance.ownerCompany?.code ?? '',
            createdUserCode: instance.createUser?.code ?? '',
        };
        ctx.res = data;
    },
};
