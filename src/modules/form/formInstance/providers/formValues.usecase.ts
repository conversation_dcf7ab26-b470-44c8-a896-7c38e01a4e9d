import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { AttachmentService } from '@/modules/attachment/providers/attachment.service';
import { Inject, Service } from 'typedi';
import { FormInstanceRepository } from './formInstance.repository';
import { FormInstanceService } from './formInstance.service';
import { FormValuesService } from './formValues.service';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import {
    EnumValueUploadFileSource,
    ValuesAppendFormData,
    ValuesUploadFile,
} from '../types/formValues.type';
import { FormInstanceTaskRepository } from '../../formInstanceTask/providers/formInstanceTask.repository';
import { InstancesValuesData } from '../types/formInstance.type';
import { FormInstanceMessage } from '@/common/models/clinico/bpm/formInstanceMessage.model';

@Service()
export class FormValuesUseCase {
    @Inject()
    private svc: FormValuesService;
    @Inject()
    private formInstanceRepo: FormInstanceRepository;
    @Inject()
    private formInstanceTaskRepo: FormInstanceTaskRepository;
    @Inject()
    private formInstanceSvc: FormInstanceService;
    @Inject()
    private attachmentSvc: AttachmentService;

    async instanceTaskUpdateFormData(
        processInstanceId: string,
        formInstance: FormInstance,
        formInstanceTask: FormInstanceTask,
        formData: JSON,
        attachmentS3Keys: string[],
        updateUserId: number,
        source: EnumValueUploadFileSource = EnumValueUploadFileSource.TASK,
        formInstanceMessage?: FormInstanceMessage,
    ) {
        const appendFormData: ValuesAppendFormData =
            formData['appendFormData'] ?? {};
        const uploadFiles: ValuesUploadFile[] = formData['uploadFiles'] ?? [];
        await this.genFormInstanceTaskS3Files(
            processInstanceId,
            formInstance,
            formInstanceTask,
            uploadFiles,
            attachmentS3Keys,
            source,
            updateUserId,
            formInstanceMessage,
        );

        this.svc.instanceAppendFormData(formInstance, appendFormData);

        // update formInstance data to db after camunda complete
        const currentInstance = await this.formInstanceRepo.findOneOrError(
            formInstance.id,
        );
        formInstance.status = currentInstance.status;
        formInstance.finishDate = currentInstance.finishDate;

        formInstance =
            await this.formInstanceRepo.saveFormInstance(formInstance);
        formInstanceTask =
            await this.formInstanceTaskRepo.saveFormInstanceTask(
                formInstanceTask,
            );
        return {
            formInstance,
            formInstanceTask,
        };
    }

    private async genFormInstanceTaskS3Files(
        processInstanceId: string,
        formInstance: FormInstance,
        formInstanceTask: FormInstanceTask,
        uploadFiles: ValuesUploadFile[],
        attachmentS3Keys: string[],
        source: EnumValueUploadFileSource,
        uploadUserId: number,
        formInstanceMessage?: FormInstanceMessage,
    ) {
        const taskValue = formInstanceTask.values ?? {};
        const s3FileInfo = await this.genS3FileInfo(
            processInstanceId,
            taskValue,
            attachmentS3Keys,
            uploadFiles,
        );
        const values = s3FileInfo.values;
        attachmentS3Keys = s3FileInfo.attachmentS3Keys;
        uploadFiles = s3FileInfo.uploadFiles;

        formInstanceTask.values = values;
        await this.updateAttachmentFiles(
            formInstance.id,
            values,
            source,
            uploadUserId,
            formInstanceTask.id,
            formInstanceMessage?.id,
        );

        // formInstance 用 appened 方式
        formInstance = this.svc.genAppendUploadFilesToFormInstance(
            formInstance,
            uploadFiles,
            attachmentS3Keys,
        );
        return {
            formInstance,
            formInstanceTask,
        };
    }

    private async genFormInstanceS3Files(
        processInstanceId: string,
        formInstance: FormInstance,
        attachmentS3Keys: string[],
        uploadUserId: number,
    ) {
        const s3FileInfo = await this.genS3FileInfo(
            processInstanceId,
            formInstance.values ?? {},
            attachmentS3Keys,
        );
        attachmentS3Keys = s3FileInfo.attachmentS3Keys;
        formInstance.values = s3FileInfo.values;
        await this.updateAttachmentFiles(
            formInstance.id,
            s3FileInfo.values,
            EnumValueUploadFileSource.INSTANCE,
            uploadUserId,
        );
        return formInstance;
    }

    async saveFormInstanceS3Files(
        processInstanceId: string,
        formInstance: FormInstance,
        attachmentS3Keys: string[],
    ) {
        await this.genFormInstanceS3Files(
            processInstanceId,
            formInstance,
            attachmentS3Keys,
            formInstance.ownerUserId,
        );
        formInstance =
            await this.formInstanceRepo.saveFormInstance(formInstance);
        return formInstance;
    }

    private async genS3FileInfo(
        processInstanceId: string,
        values: ValuesAppendFormData | InstancesValuesData,
        attachmentS3Keys: string[],
        uploadFiles: ValuesUploadFile[] = [],
    ) {
        attachmentS3Keys =
            await this.attachmentSvc.validateAttachment(attachmentS3Keys);
        // Attachment move from `draft/..` to `FORM_ID/...`
        const moveResult =
            await this.attachmentSvc.bulkMoveDraftToCurrentFolder({
                processInstanceId: processInstanceId,
                sourceKeys: attachmentS3Keys,
            });
        // replace s3Key of values
        for (const result of moveResult) {
            values = this.svc.replaceJsonS3Key({
                values,
                s3Key: result.key,
                newS3Key: result.newKey,
            });
        }
        // append uploadFiles replace file name
        uploadFiles = uploadFiles
            .map((i) => {
                const find = moveResult.find((i2) => i2.key == i.s3Key);
                i.s3Key = find?.newKey ?? '';
                return i;
            })
            .filter((i) => i.s3Key != '');
        attachmentS3Keys = moveResult.map((i) => i.newKey);
        return {
            values,
            uploadFiles,
            attachmentS3Keys,
        };
    }

    private async updateAttachmentFiles(
        formInstanceId: number,
        formValues: InstancesValuesData | ValuesAppendFormData,
        source: EnumValueUploadFileSource,
        uploadUserId: number,
        formInstanceTaskId?: number,
        formInstanceMessageId?: number,
    ) {
        // 由BPM新增instance的附件
        if (formValues && 'uploadFiles' in formValues) {
            const values = formValues as any;
            await this.attachmentSvc.updateOrCreate({
                formInstanceId,
                formInstanceTaskId,
                uploadUserId,
                source,
                formInstanceMessageId,
                attachmentFiles: values.uploadFiles.map((data) => ({
                    s3Key: data.s3Key,
                    fileName: data.name,
                    groupBy: data.groupBy,
                })),
            });
        }
        // 由外部上傳的附件
        if (formValues && 'attachments' in formValues) {
            const values = formValues as any;
            await this.attachmentSvc.updateOrCreate({
                formInstanceId,
                formInstanceTaskId,
                uploadUserId,
                source,
                formInstanceMessageId,
                attachmentFiles: values.attachments.map((data) => ({
                    s3Key: data.s3Key,
                    fileName: data.name,
                    groupBy: data.groupBy,
                })),
            });
        }
    }
}
