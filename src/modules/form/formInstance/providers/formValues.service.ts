import _ from 'lodash';
import { Service } from 'typedi';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import {
    ValuesAppendFormData,
    ValuesUploadFile,
} from '../types/formValues.type';
import { InstancesValuesData } from '../types/formInstance.type';

@Service()
export class FormValuesService {
    instanceAppendFormData(
        formInstance: FormInstance,
        appendFormData: ValuesAppendFormData,
    ) {
        const values = formInstance.values ?? {};
        this.appendFormData(values, appendFormData);
        formInstance.values = values;
        return formInstance;
    }

    private appendFormData(
        values: InstancesValuesData,
        appendFormData: ValuesAppendFormData,
    ) {
        if (appendFormData && !_.isEmpty(appendFormData)) {
            values = this.genObjectByFormData(appendFormData, values);
        }
        return values;
    }

    // formInstance 用 appened 方式 uploadFiles
    genAppendUploadFilesToFormInstance(
        formInstance: FormInstance,
        uploadFiles: ValuesUploadFile[],
        attachmentS3Keys: string[],
    ) {
        const instanceValues: any = formInstance.values ?? {};
        instanceValues.uploadFiles = instanceValues.uploadFiles ?? [];
        // 用 appened 方式
        for (const attachmentS3Key of attachmentS3Keys) {
            const fileName =
                uploadFiles.find(
                    (uploadFile) => uploadFile.s3Key == attachmentS3Key,
                )?.name ?? '';

            instanceValues.uploadFiles.push({
                name: fileName,
                s3Key: attachmentS3Key,
            });
        }
        formInstance.values = instanceValues;
        return formInstance;
    }

    replaceS3Key(params: { values: string; s3Key: string; newS3Key: string }) {
        return params.values.replace(params.s3Key, params.newS3Key);
    }

    replaceJsonS3Key(params: {
        values: InstancesValuesData | ValuesAppendFormData;
        s3Key: string;
        newS3Key: string;
    }) {
        const values = JSON.stringify(params.values).replace(
            params.s3Key,
            params.newS3Key,
        );
        return JSON.parse(values);
    }

    genObjectByFormData(appendFormData: any, target: any = {}) {
        target = _.merge(target, appendFormData);
        return target;
    }
}
