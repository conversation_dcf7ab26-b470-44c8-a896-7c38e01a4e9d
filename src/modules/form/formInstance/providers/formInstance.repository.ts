import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import { CommonService } from '@/common/services/common.service';
import { CommonSearchResult } from '@/common/types/common.type';
import _ from 'lodash';
import { Inject, Service } from 'typedi';
import {
    Between,
    Equal,
    FindOptionsRelations,
    FindOptionsWhere,
    In,
    IsNull,
    LessThanOrEqual,
    Like,
    MoreThanOrEqual,
    Not,
} from 'typeorm';
import { EnumTaskStatus } from '../../formInstanceTask/types/formInstanceTask.type';
import {
    ConvertCreateParams,
    EnumInstanceStatus,
    SearchParams,
    UpdateParams,
} from '../types/formInstance.type';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { genFilterQueryMetadata } from '@/common/helpers/jsonbQuery/genFilterQueryMetadata';
import { FilterQueryMetadata } from '@/common/helpers/jsonbQuery/type';
import { SearchBody } from '../types/forminstance.controller.type';
import { Process } from '@/modules/camunda/types/process.type';
import { User } from '@/common/models/clinico/core/user.model';
import { Department } from '@/common/models/clinico/core/department.model';
import { Helpers } from '@clinico/clinico-node-framework';
import moment from 'moment';
import { FormConfService } from '@/modules/form/formConf/providers/formConf.service';

@Service()
export class FormInstanceRepository extends CommonService<FormInstance> {
    repository = ClinicoDataSource.getRepository(FormInstance);
    @Inject()
    private formConfSvc: FormConfService;

    convertTaskJsonb(task: FormInstanceTask) {
        task.property =
            task.property && _.isString(task.property)
                ? JSON.parse(task.property)
                : task.property;
        task.values =
            task.values && _.isString(task.values)
                ? JSON.parse(task.values)
                : task.values;
        return task;
    }
    convertJsonb(instance: FormInstance) {
        instance.values =
            instance.values && _.isString(instance.values)
                ? JSON.parse(instance.values)
                : instance.values;
        return instance;
    }

    createNewFormInstance(
        form: Form,
        params: ConvertCreateParams,
        camundaForm: Process,
        ownerUser: User,
        ownerDepartment: Department,
        code: string,
        status: EnumInstanceStatus = EnumInstanceStatus.Waiting,
    ) {
        return this.repository.create({
            formId: params.formId,
            processId: camundaForm.id,
            version: camundaForm.version,
            formVersion: form.version,
            values: params.values,
            startDate: Helpers.Date.now().toDate(),
            ownerCompanyId: params.ownerCompanyId,
            ownerDepartmentId: ownerDepartment.id,
            ownerDepartmentCode: ownerDepartment.code,
            ownerUserId: ownerUser.id,
            ownerUserCode: ownerUser.code,
            createdUserId: params.createdUserId,
            status,
            referenceFormInstanceId: params.referenceFormInstanceId,
            code,
        });
    }

    async saveFormInstance(formInstance: FormInstance) {
        const omitRefFormInstance = _.omit(formInstance, [
            'form',
            'ownerCompany',
            'ownerDepartment',
            'ownerUser',
            'createUser',
            'formInstanceTasks',
            'formInstanceMessages',
        ]);
        return this.repository.save(omitRefFormInstance);
    }

    async updateParamsById(params: UpdateParams) {
        const omitUpdate = _.omit(params, ['id', 'updateUserId']);
        await this.repository.update(
            {
                id: params.id,
            },
            omitUpdate,
        );
        return this.findOneOrError(params.id);
    }

    async countFormInstanceByToday(formId: number) {
        return this.repository.count({
            where: {
                formId,
                createdAt: Between(
                    moment().startOf('day').toDate(),
                    moment().endOf('day').toDate(),
                ),
            },
        });
    }

    async searchMany(params: SearchBody) {
        const { offset, limit, formInstanceParams, formValueFilter } = params;
        const searchParams: SearchParams = {
            ...formInstanceParams,
            offset,
            limit,
        };
        if (formValueFilter) {
            const formInstanceIds =
                await this.getFormInstanceIdsByFormValueFilter(formValueFilter);
            if (formInstanceIds.length == 0) {
                return {
                    rows: [],
                    count: 0,
                };
            }
            searchParams.ids = formInstanceIds;
        }
        return this.search(searchParams);
    }

    async getFormInstanceIdsByFormValueFilter(
        formValueFilter: FilterQueryMetadata,
    ) {
        const formValueSql =
            this.genGetIdsByFormValueMetadataFilterSql(formValueFilter);
        const findIds: [{ id: number }] =
            await this.repository.query(formValueSql);
        const formInstanceIds = findIds.map((i) => Number(i.id));
        return formInstanceIds;
    }

    private genGetIdsByFormValueMetadataFilterSql(
        filterQuery: FilterQueryMetadata,
    ) {
        const jsonb = 'values';
        const { where, from } = genFilterQueryMetadata(jsonb, filterQuery);
        if (where.length == 0) {
            return '';
        }
        const sql = `
        SELECT
            distinct fi.id as id
        FROM
            "bpm"."form_instances" "fi"
            ${from.length > 0 ? `, ${from.join(',')}` : ''}
        WHERE
            ${where}
        `;
        return sql;
    }

    async search(
        params: SearchParams,
        relations?: FindOptionsRelations<FormInstance>,
    ): Promise<CommonSearchResult<FormInstance>> {
        const filters: FindOptionsWhere<FormInstance> = { deleted: false };
        const taskFilters: FindOptionsWhere<FormInstanceTask> = {};
        const formFilters: FindOptionsWhere<Form> = {};
        if (params.id) {
            filters.id = params.id;
        }
        if (params.ids && params.ids.length > 0) {
            filters.id = In(params.ids);
        }
        if (params.formId) {
            filters.formId = params.formId;
        }
        if (params.formCode) {
            formFilters.code = params.formCode;
        }
        if (params.processInstanceId) {
            filters.processInstanceId = params.processInstanceId;
        }
        if (params.startDate) {
            filters.startDate = params.startDate;
        }
        if (params.finishDate) {
            filters.finishDate = params.finishDate;
        }
        if (params.startDateRange1 && params.startDateRange2) {
            filters.startDate = Between(
                params.startDateRange1,
                params.startDateRange2,
            );
        } else if (params.startDateRange1) {
            filters.startDate = MoreThanOrEqual(params.startDateRange1);
        } else if (params.startDateRange2) {
            filters.startDate = LessThanOrEqual(params.startDateRange2);
        }
        if (params.finishDateRange1 && params.finishDateRange2) {
            filters.finishDate = Between(
                params.finishDateRange1,
                params.finishDateRange2,
            );
        } else if (params.finishDateRange1) {
            filters.finishDate = MoreThanOrEqual(params.finishDateRange1);
        } else if (params.finishDateRange2) {
            filters.finishDate = LessThanOrEqual(params.finishDateRange2);
        }
        if (params.ownerCompanyId) {
            filters.ownerCompanyId = params.ownerCompanyId;
        }
        if (params.ownerDepartmentId) {
            filters.ownerDepartmentId = params.ownerDepartmentId;
        }
        if (params.ownerDepartmentCode) {
            filters.ownerDepartmentCode = params.ownerDepartmentCode;
        }
        if (params.ownerUserId) {
            filters.ownerUserId = params.ownerUserId;
        }
        if (params.ownerUserCode) {
            filters.ownerUserCode = params.ownerUserCode;
        }
        if (!_.isUndefined(params.status) && !_.isNull(params.status)) {
            filters.status = params.status;
        }
        if (params.createdUserId) {
            filters.createdUserId = params.createdUserId;
        }
        if (params.currentAssignedUserid) {
            taskFilters.userId = params.currentAssignedUserid;
            taskFilters.status = In([
                EnumTaskStatus.Waiting,
                EnumTaskStatus.Reconsidered,
            ]);
            let taskFiltersIds = await this.searchByTaskFilters(taskFilters);
            if (params.ids && params.ids.length > 0) {
                taskFiltersIds = taskFiltersIds.filter(
                    (i) => params.ids?.includes(i),
                );
            }
            filters.id = In(taskFiltersIds);
        }
        if (params.taskUserid) {
            taskFilters.userId = params.taskUserid;
            let taskFiltersIds = await this.searchByTaskFilters(taskFilters);
            if (params.ids && params.ids.length > 0) {
                taskFiltersIds = taskFiltersIds.filter(
                    (i) => params.ids?.includes(i),
                );
            }
            filters.id = In(taskFiltersIds);
        }
        if (params.code) {
            filters.code = Like(`%${params.code}%`);
        }
        if (params.beReferencedId) {
            filters.beReferencedId = params.beReferencedId;
        }
        if (_.isNull(params.beReferencedStatus)) {
            filters.beReferencedStatus = IsNull();
        } else {
            filters.beReferencedStatus = params.beReferencedStatus;
        }

        const andFilter = { ...filters, form: formFilters };

        const orFilter: any[] = [];
        /**
         * not BeReferencedStatus, or is null 這部分用 orm 真的很難處理，只能先用 push 進去再用 or 來處理
         * 彈性比較小，但目前先這樣處理
         *
         * 要複雜查詢還是得用 /form/instance/search jsonb query 來處理
         * 哎，這部分真的很難處理
         * 哎
         * searchMany
         * _beReferencedStatus
         * _beReferencedId
         *
         * "formValueFilter": {
         *     "logic": "and",
         *     "filters": [
         *         {
         *             "logic": "or",
         *             "filters": [
         *                 {
         *                     "field": "_beReferencedStatus",
         *                     "dataType": "numeric",
         *                     "operator": "isnull",
         *                     "value": null
         *                 },
         *                 {
         *                     "field": "_beReferencedStatus",
         *                     "dataType": "numeric",
         *                     "operator": "neq",
         *                     "value": 1
         *                 }
         *             ]
         *         }
         *     ]
         * }
         */
        // or operator
        if (
            !_.isUndefined(params.notBeReferencedStatus) &&
            !_.isNull(params.notBeReferencedStatus)
        ) {
            orFilter.push({
                ...andFilter,
                beReferencedStatus: Not(Equal(params.notBeReferencedStatus)),
            });
            orFilter.push({
                ...andFilter,
                beReferencedStatus: IsNull(),
            });
        }
        const where = orFilter.length > 0 ? orFilter : andFilter;

        const defaultRelations: FindOptionsRelations<FormInstance> = {
            ownerCompany: true,
            ownerDepartment: true,
            ownerUser: true,
            createUser: true,
            formInstanceTasks: {
                company: true,
                department: true,
                user: true,
                reminders: true,
            },
            form: {},
        };

        /**
         * default 優先於 relations
         */
        const findRelations = _.merge(relations, defaultRelations);

        const [rows, count] = await this.repository.findAndCount({
            relations: findRelations,
            where,
            take: params.limit,
            skip: params.offset,
            order: {
                id: 'DESC',
                // TODO：關聯表排序會導致take數量異常
                // formInstanceTasks: { id: 'DESC' },
                // formInstanceMessages: { id: 'ASC' },
            },
        });

        const result: CommonSearchResult<FormInstance> = {
            rows: this.sortRelation(rows),
            count,
        };
        return result;
    }

    private sortRelation(rows: FormInstance[]) {
        rows.forEach((row) => {
            row = this.convertJsonb(row);
            if (row.form) {
                row.form = this.formConfSvc.genFormAllConf(row.form);
            }
            row.formInstanceTasks?.forEach((i) => (i.id = Number(i.id)));
            row.formInstanceMessages?.forEach((i) => (i.id = Number(i.id)));
            row.formInstanceTasks = _.orderBy(
                row.formInstanceTasks,
                ['id'],
                ['desc'],
            ).map((i) => this.convertTaskJsonb(i));
            row.formInstanceMessages = _.orderBy(
                row.formInstanceMessages,
                ['id'],
                ['asc'],
            );
        });
        return rows;
    }

    private async searchByTaskFilters(
        taskFilters: FindOptionsWhere<FormInstanceTask>,
    ): Promise<number[]> {
        const rows = await this.repository.find({
            where: { formInstanceTasks: taskFilters },
            select: {
                id: true,
            },
        });
        return rows.map((row) => Number(row.id));
    }
}
