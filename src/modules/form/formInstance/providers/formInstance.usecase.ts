import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import {
    CancelParams,
    DeleteParams,
    EnumInstanceStatus,
    CreateParams,
    UpdateParams,
    MessageParams,
    UpdateMessageParams,
    ConvertCreateParams,
} from '../types/formInstance.type';
import { Inject, Service } from 'typedi';
import _ from 'lodash';
import { BaseError } from '@clinico/base-error';
import { DepartmentService } from '@/modules/organization/providers/department.service';
import { UserService } from '@/modules/organization/providers/user.service';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import httpStatus from 'http-status';
import { Form } from '@/common/models/clinico/bpm/form.model';
import { HookService } from '@/common/services/hook.service';
import { EnumHookEvent } from '@/common/types/hook.type';
import {
    CurrentTask,
    FormInstanceResult,
    SearchBody,
    SearchFormInstanceResult,
} from '../types/forminstance.controller.type';
import { FormInstanceMessage } from '@/common/models/clinico/bpm/formInstanceMessage.model';
import { v4 as uuidv4 } from 'uuid';
import { Department } from '@/common/models/clinico/core/department.model';
import { FormInstanceRepository } from './formInstance.repository';
import { Process } from '@/modules/camunda/types/process.type';
import { User } from '@/common/models/clinico/core/user.model';
import { FormInstanceService } from './formInstance.service';
import { FormValuesUseCase } from './formValues.usecase';
import { ProcessInstanceUseCase } from '@/modules/camunda/providers/processInstance.usecase';
import { TaskNotifyUseCase } from '@/modules/camunda/providers/taskNotify/taskNotify.usecase';
import { FormRepository } from '../../form/providers/form.repository';
import { FormUseCase } from '../../form/providers/form.usecase';
import { FormInstanceMessageService } from '../../formInstanceMessage/providers/formInstanceMessage.service';
import { FormInstanceTaskUseCase } from '../../formInstanceTask/providers/formInstanceTask.usecase';
import { EnumTaskStatus } from '../../formInstanceTask/types/formInstanceTask.type';
import { PermissionRepository } from '@/modules/permission/providers/permission.repository';
import {
    EnumValueUploadFileSource,
    ValueUploadFileInfo,
    ValuesUploadFile,
} from '../types/formValues.type';
import { AttachmentService } from '@/modules/attachment/providers/attachment.service';
import { Attachment } from '@/common/models/clinico/bpm/attachment.model';
import { UserPayload } from '@/modules/organization/types/user.type';
import { PermissionUseCase } from '@/modules/permission/providers/permission.ucase';
import { UserPositionService } from '@/modules/organization/providers/userPosition.service';
import { UserPermission } from '@/common/models/clinico/bpm/userPermission.model';
import { CustomizeUseCase } from '@/customize/customize.usecase';
import { FormConfFieldStatusUseCase } from '../../formConf/providers/formConfFieldStatus.usecase';
import { CurrentTaskUseCase } from '../../formInstanceTask/providers/currentTask.usecase';
import { UserUseCase } from '@/modules/organization/providers/user.usecase';
import { EnumCurrentTaskStatus } from '../../formConf/types/formConfFieldStatus.types';
import { TaskAssigneeUseCase } from '@/modules/camunda/providers/taskAssignee/taskAssignee.usecase';

@Service()
export class FormInstanceUseCase {
    @Inject()
    private formRepo: FormRepository;
    @Inject()
    private repo: FormInstanceRepository;
    @Inject()
    private service: FormInstanceService;
    @Inject()
    private permissionUCase: PermissionUseCase;
    @Inject()
    private formValuesUCase: FormValuesUseCase;
    @Inject()
    private formInstanceTaskUCase: FormInstanceTaskUseCase;
    @Inject()
    private processInstanceUCase: ProcessInstanceUseCase;
    @Inject()
    private departmentService: DepartmentService;
    @Inject()
    private userService: UserService;
    @Inject()
    private userPositionService: UserPositionService;
    @Inject()
    private taskNotifyUCase: TaskNotifyUseCase;
    @Inject()
    private hookService: HookService;
    @Inject()
    private formInstanceMessageService: FormInstanceMessageService;
    @Inject()
    private formUCase: FormUseCase;
    @Inject()
    private permissionRepo: PermissionRepository;
    @Inject()
    private attachmentSvc: AttachmentService;
    @Inject()
    private formValueUCase: FormValuesUseCase;
    @Inject()
    private customizeUCase: CustomizeUseCase;
    @Inject()
    private formConfFieldStatusUCase: FormConfFieldStatusUseCase;
    @Inject()
    private currentTaskUCase: CurrentTaskUseCase;
    @Inject()
    private userUCase: UserUseCase;
    @Inject()
    private taskAssigneeUCase: TaskAssigneeUseCase;

    /**
     * 預覽簽核相關的任務受理人
     * @param id
     * @returns
     */
    async previewTasksAssigneesById(id: number) {
        const instance = await this.repo.findOneOrError(id);
        const data =
            await this.taskAssigneeUCase.getPreviewFormInstanceTaskAssignees(
                instance,
            );
        return data;
    }

    async searchByUserPayload(params: SearchBody, userPayload: UserPayload) {
        if (!params.formInstanceParams) {
            params.formInstanceParams = {};
        }
        params.formInstanceParams.taskUserid = userPayload.id;
        const result: SearchFormInstanceResult =
            await this.repo.searchMany(params);
        return this.currentTaskUCase.genAllCurrentTasksByInstanceResult(
            result,
            userPayload,
        );
    }

    async findOneDefinitionXMLById(id: number): Promise<string | null> {
        const formInstance = await this.repo.findOneOrError(id);
        return this.processInstanceUCase.findOneDefinitionXML({
            formInstance,
        });
    }

    async createFormInstance(params: CreateParams): Promise<FormInstance> {
        const convertParams = this.convertCreateParams(params);
        const {
            form,
            camundaForm,
            ownerDepartment,
            ownerUser,
            formData,
            attachmentS3Keys,
        } = await this.getInfoByCreateParams(convertParams);

        // form instance
        const formInstance = await this.genNewFormInstance(
            convertParams,
            camundaForm,
            form,
            ownerUser,
            ownerDepartment,
        );

        // camunda process instance.
        const { formInstanceTasks } =
            await this.createInstanceTaskByStartProcess(
                formInstance,
                form,
                formData,
                attachmentS3Keys,
            );

        await this.autoUpdateBeReferencedFormInstance(
            formInstance,
            formInstance.status,
        );

        if (formInstanceTasks.length) {
            const notifyTasksFnList = formInstanceTasks.map((task) =>
                this.taskNotifyUCase.notifyNewFormInstance(task),
            );
            await Promise.all([
                this.formInstanceTaskUCase.runAutoApprove(
                    formInstanceTasks,
                    ownerUser.id,
                ),
                ...notifyTasksFnList,
            ]);
        }

        return formInstance;
    }

    private async autoUpdateBeReferencedFormInstance(
        formInstance: FormInstance,
        status?: EnumInstanceStatus,
    ) {
        const { referenceFormInstanceId } = formInstance;
        if (
            !referenceFormInstanceId ||
            _.isUndefined(status) ||
            _.isNull(status)
        ) {
            return null;
        }
        const beReferencedFormInstance = await this.repo.findOne(
            referenceFormInstanceId,
        );
        const beRefValues = beReferencedFormInstance?.values;
        if (!beRefValues) {
            return null;
        }

        beRefValues['_beReferencedId'] = Number(formInstance.id);
        beRefValues['_beReferencedStatus'] = status;
        beReferencedFormInstance.values = beRefValues;
        beReferencedFormInstance.beReferencedId = formInstance.id;
        beReferencedFormInstance.beReferencedStatus = status;
        return this.repo.saveFormInstance(beReferencedFormInstance);
    }

    async updateFormInstance(params: UpdateParams): Promise<FormInstance> {
        const instance = await this.repo.findOne(params.id);
        if (!instance) {
            throw new BaseError('表單實例不存在', httpStatus.NOT_FOUND);
        }
        if (
            instance.createdUserId != params.updateUserId &&
            instance.ownerUserId != params.updateUserId
        ) {
            throw new BaseError('不允許非表單擁用人變更', httpStatus.FORBIDDEN);
        }

        if (params.finishDate) {
            instance.finishDate = params.finishDate;
        }
        if (params.values) {
            instance.values = params.values;
        }
        if (!_.isUndefined(params.status) && !_.isNull(params.status)) {
            instance.status = params.status;
        }
        if (!_.isUndefined(params.deleted) && !_.isNull(params.deleted)) {
            instance.deleted = params.deleted;
        }

        const [updateInstance, beReferencedFormInstance] = await Promise.all([
            this.repo.updateParamsById(params),
            this.autoUpdateBeReferencedFormInstance(instance, params.status),
        ]);

        await this.hookService.hook({
            event: EnumHookEvent.Instance,
            formInstance: updateInstance,
        });

        return updateInstance;
    }

    async deleteFormInstance(params: DeleteParams): Promise<FormInstance> {
        const instance = await this.updateFormInstance({
            id: params.id,
            updateUserId: params.updateUserId,
            deleted: true,
        });
        await this.processInstanceUCase.deleteProcessByFromInstance(instance);

        return instance;
    }

    async cancelFormInstance(params: CancelParams): Promise<FormInstance> {
        const instance = await this.updateFormInstance({
            id: params.id,
            updateUserId: params.updateUserId,
            status: EnumInstanceStatus.Canceled,
        });
        await this.formInstanceTaskUCase.cancel({
            instanceId: instance.id,
            updatedUserId: params.updateUserId,
        });
        await this.processInstanceUCase.deleteProcessByFromInstance(instance);
        return instance;
    }

    async message(params: MessageParams): Promise<FormInstance> {
        let formInstance = await this.repo.findOneOrError(params.id);
        let currentTask: FormInstanceTask | null = null;

        const message = await ClinicoDataSource.transaction(async (manager) => {
            let formInstanceTaskId =
                params.taskId ??
                _.maxBy(formInstance.formInstanceTasks, 'id')?.id;
            if (!formInstanceTaskId) {
                const currentTasks = formInstance.formInstanceTasks.filter(
                    (data) => data.status == EnumTaskStatus.Waiting,
                );
                formInstanceTaskId = currentTasks[0].id;
            }
            const message = await this.formInstanceMessageService.create(
                {
                    message: params.message,
                    formInstanceId: formInstance.id,
                    formInstanceTaskId,
                    createdUserId: params.updateUserId,
                },
                manager,
            );

            if (params.taskId) {
                const result = await this.formInstanceTaskUCase.reconsider(
                    {
                        id: params.taskId,
                        values: params.values,
                        memo: params.memo,
                        updatedUserId: params.updateUserId,
                    },
                    manager,
                );
                currentTask = result.current;
            }

            return message;
        });

        formInstance = await this.repo.findOneOrError(params.id);

        if (
            params.updateUserId &&
            params.values &&
            currentTask &&
            // params.attachmentS3Keys &&
            // params.attachmentS3Keys.length > 0 &&
            formInstance.processInstanceId
        ) {
            const data = await this.formValueUCase.instanceTaskUpdateFormData(
                formInstance.processInstanceId,
                formInstance,
                currentTask,
                params.values as JSON,
                params.attachmentS3Keys ?? [],
                params.updateUserId,
                EnumValueUploadFileSource.MESSAGE,
                message,
            );
            await this.repo.saveFormInstance(data.formInstance);
        }

        await this.taskNotifyUCase.notifyReconsidered(
            formInstance,
            message.id,
            params.updateUserId,
            currentTask,
        );

        return await this.repo.findOneOrError(params.id);
    }

    async updateMessage(
        params: UpdateMessageParams,
    ): Promise<FormInstanceMessage> {
        const formInstance = await this.repo.findOneOrError(params.id);
        if (
            !formInstance.formInstanceMessages.some(
                (data) => data.id == params.messageId,
            )
        ) {
            throw new BaseError('Message not found', httpStatus.NOT_FOUND);
        }
        return await this.formInstanceMessageService.update({
            id: params.messageId,
            message: params.message,
            updatedUserId: params.updateUserId,
        });
    }

    private async createInstanceTaskByStartProcess(
        formInstance: FormInstance,
        form: Form,
        formData: JSON,
        attachmentS3Keys: string[],
    ) {
        const { tasks, processInstance } =
            await this.processInstanceUCase.startProcess(
                formInstance,
                form,
                formData,
            );
        formInstance.processInstanceId = processInstance.id;

        const groupCode = uuidv4();
        const formInstanceTasks: FormInstanceTask[] = [];
        /**
         * 無法使用 Promise.all 來同時執行，因為 camunda task 會報錯
         */
        for (const task of tasks) {
            const formInstanceTask =
                await this.formInstanceTaskUCase.createInstanceTask({
                    camundaTask: task,
                    formInstance,
                    name: task.name,
                    groupCode,
                });
            formInstanceTasks.push(formInstanceTask);
        }

        // Attachment move from `draft/..` to `FORM_ID/...`
        await this.formValuesUCase.saveFormInstanceS3Files(
            processInstance.id,
            formInstance,
            attachmentS3Keys,
        );

        return {
            formInstance,
            formInstanceTasks,
            processInstance,
        };
    }

    private convertCreateParams(params: CreateParams): ConvertCreateParams {
        const convertParams: ConvertCreateParams = {
            ...params,
            values: undefined,
        };
        convertParams.values =
            params.values && _.isString(params.values)
                ? JSON.parse(params.values)
                : params.values;
        return convertParams;
    }

    private async genNewFormInstance(
        params: ConvertCreateParams,
        camundaForm: Process,
        form: Form,
        ownerUser: User,
        ownerDepartment: Department,
    ) {
        const code = await this.customizeUCase.autoGenInstanceCode(
            form,
            ownerDepartment,
            params.values,
        );
        const formInstance = this.repo.createNewFormInstance(
            form,
            params,
            camundaForm,
            ownerUser,
            ownerDepartment,
            code,
        );
        await this.validate(formInstance);
        return this.repo.saveFormInstance(formInstance);
    }

    private async getInfoByCreateParams(params: ConvertCreateParams) {
        const form = await this.formRepo.findOne(params.formId);
        if (!form) {
            throw new BaseError('表單定義不存在', httpStatus.NOT_FOUND);
        }
        const camundaForm = await this.formUCase.findCamundaFormByForm(form);

        const ownerDepartment = await this.departmentService.findOneOrError(
            params.ownerDepartmentId,
        );
        const ownerUser = await this.userService.findOneOrError(
            params.ownerUserId,
        );
        const formData = params.values ?? ({} as JSON);
        const attachmentS3Keys = params.attachmentS3Keys ?? [];
        return {
            form,
            camundaForm,
            ownerDepartment,
            ownerUser,
            formData,
            attachmentS3Keys,
        };
    }

    protected async validate(instance: FormInstance): Promise<void> {
        if (instance.referenceFormInstanceId) {
            const referenceFormInstance = await this.repo.findOne(
                instance.referenceFormInstanceId,
            );
            if (!referenceFormInstance) {
                throw new BaseError(
                    '參考的表單來源實例不存在不存在',
                    httpStatus.NOT_FOUND,
                );
            }
            if (referenceFormInstance.status != EnumInstanceStatus.Completed) {
                throw new BaseError(
                    '參考的表單來源實例尚未完成簽核',
                    httpStatus.NOT_FOUND,
                );
            }
        }
        // TODO
    }

    async getFormInstanceResultById(
        id: number,
        userPayload: UserPayload,
    ): Promise<FormInstanceResult | null> {
        const instance = await this.repo.findOne(id, {
            formInstanceMessages: {
                createUser: true,
                formInstanceTask: true,
            },
        });
        if (!instance) return null;

        const { isSuperUser, isOwnUser } =
            await this.permissionUCase.getInstancePermission(
                userPayload,
                instance,
            );

        const formInstanceTasks = instance.formInstanceTasks.filter(
            (i) => !i.deleted,
        );
        const isFinishProcess = [
            EnumInstanceStatus.Completed,
            EnumInstanceStatus.Canceled,
        ].includes(instance.status);
        let currentTask: Partial<CurrentTask> | null = null;
        const userInfo = await this.userUCase.getFilterUserInfoById(
            userPayload.id,
        );

        const filterData = {
            userInfo,
            instance,
            currentTaskStatus: EnumCurrentTaskStatus.Processing,
        };
        const formFieldStatusesFn =
            await this.formConfFieldStatusUCase.getFormFieldStatusesFnMap(
                instance.formId,
            );
        if (isFinishProcess) {
            filterData.currentTaskStatus = EnumCurrentTaskStatus.Finish;
            currentTask = this.currentTaskUCase.genFinishCurrentTask(
                instance,
                formFieldStatusesFn,
                filterData,
            );
        } else {
            filterData.currentTaskStatus = EnumCurrentTaskStatus.Processing;
            currentTask = this.currentTaskUCase.getCurrentTaskByTasks(
                formInstanceTasks,
                formFieldStatusesFn,
                filterData,
                userPayload.id,
                isOwnUser,
                isSuperUser,
            );
        }

        const allCurrentTasks = this.currentTaskUCase.getAllCurrentTasks(
            formInstanceTasks,
            formFieldStatusesFn,
            filterData,
            isSuperUser,
            userPayload.id,
        );

        let result: FormInstanceResult = {
            ...instance,
            formInstanceTasks,
            currentTask,
            allCurrentTasks,
        };

        // 閱讀權限
        result = await this.allowRead(result, userPayload.id);

        // 上傳檔案詳細資訊
        result = await this.genValueUploadFileInfo(result);
        return result;
    }

    private async genValueUploadFileInfo(instance: FormInstanceResult) {
        if (!instance.values) {
            return instance;
        }
        const uploadFiles: ValuesUploadFile[] = instance.values['uploadFiles'];
        if (!uploadFiles) return instance;
        const s3Keys = uploadFiles.map((i) => i.s3Key);
        const attachments =
            await this.attachmentSvc.getAttachmentsByS3Keys(s3Keys);

        const attachmentMap = attachments.reduce((pre, cur) => {
            pre.set(cur.s3Key, cur);
            return pre;
        }, new Map<string, Attachment>());

        const userResult = await this.userPositionService.search({
            userIds: attachments.map((i) => i.uploadUserId),
        });
        const userMap = userResult.rows.reduce((pre, cur) => {
            pre.set(Number(cur.userId), cur);
            return pre;
        }, new Map<number, UserPermission>());
        const uploadFileInfoList: ValueUploadFileInfo[] = uploadFiles.map(
            (i) => {
                const attachment = attachmentMap.get(i.s3Key);
                const user = userMap.get(Number(attachment?.uploadUserId) ?? 0);
                const result: ValueUploadFileInfo = {
                    name: i.name,
                    s3Key: i.s3Key,
                    groupBy: i.groupBy,
                    formInstanceId: attachment?.formInstanceId ?? null,
                    formInstanceTaskId: attachment?.formInstanceTaskId ?? null,
                    formInstanceMessageId:
                        attachment?.formInstanceMessageId ?? null,
                    uploadUserId: attachment?.uploadUserId,
                    uploadUserCode: user?.user?.code,
                    uploadUserName: user?.user?.name,
                    source:
                        (attachment?.source as EnumValueUploadFileSource) ??
                        EnumValueUploadFileSource.INSTANCE,
                };
                return result;
            },
        );

        instance.values['uploadFiles'] = uploadFileInfoList;

        return instance;
    }

    private async allowRead(
        instance: FormInstanceResult,
        userId: number,
    ): Promise<FormInstanceResult> {
        instance.allowRead = false;

        /* in tasks userIds */
        const userIds = _.uniqBy(instance.formInstanceTasks, 'userId').map(
            (data) => data.userId,
        );
        if (userIds.includes(userId)) {
            instance.allowRead = true;
        }

        /* user permissions userId */
        const userPermission = await this.permissionRepo.findByUserId(userId);
        const isSuperUser =
            userPermission?.permission &&
            userPermission.permission['isSuperUser'];
        if (isSuperUser) {
            instance.allowRead = true;
        }

        const allowReadByFormTaskMailTo =
            await this.formUCase.allowReadByFormTaskMailTo(
                instance.formId,
                userId,
            );

        if (allowReadByFormTaskMailTo) {
            instance.allowRead = allowReadByFormTaskMailTo;
        }

        const formInstanceTasks = (instance.formInstanceTasks =
            instance.formInstanceTasks.filter((data) => {
                if (!data.values) {
                    return true;
                }
                const json = data.values;
                if (!json.isSkip) {
                    return true;
                }
                return false;
            }));

        instance.formInstanceTasks = formInstanceTasks;

        if (!instance.allowRead) {
            //移除內容
            instance.values = {} as JSON;
            instance.formInstanceMessages = [];
        }

        return instance;
    }
}
