import { GRPCFileUpload } from '@/common/types/common.grpc.type';
import { Timestamp } from '@grpc/grpc-js/build/src/generated/google/protobuf/Timestamp';
import { EnumInstanceStatus } from './formInstance.type';
import { ValuesAppendFormData } from './formValues.type';

export type GRPCSearchFormInstanceParams = {
    id: number;
    offset?: number;
    limit?: number;
};

export type GRPCFormInstance = {
    id: number;
    formId: number;
    processInstanceId?: string;
    values?: string;
    startDate: Timestamp;
    finishDate?: Timestamp;
    ownerCompanyCode: string;
    ownerDepartmentCode: string;
    ownerUserCode: string;
    status: EnumInstanceStatus;
    createdUserCode: string;
    deleted: boolean;
    createdAt: Timestamp;
    updatedAt?: Timestamp;
};

export type GRPCCreateFormInstanceParams = {
    formCode: string;
    values?: ValuesAppendFormData;
    companyCode?: string;
    departmentCode?: string;
    userCode: string;
    attachmentS3Keys?: string[];
    createdUserCode: string;
    files?: GRPCFileUpload[];
};
