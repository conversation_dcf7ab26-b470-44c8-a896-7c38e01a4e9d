export type ValuesUploadFile = {
    s3Key: string;
    name: string;
    groupBy?: string | null;
};

/**
 * 區分來源
 */
// export type ValueUploadFileSource = 'instance' | 'task' | 'additionalDoc' | 'message';
export enum EnumValueUploadFileSource {
    /**
     * formInstance
     */
    INSTANCE = 'instance',
    /**
     * formInstanceTask
     */
    TASK = 'task',
    /**
     * 留言上傳
     */
    MESSAGE = 'message',
}

export type ValueUploadFileInfo = ValuesUploadFile & {
    source: EnumValueUploadFileSource;
    formInstanceId?: number | null;
    formInstanceTaskId?: number | null;
    formInstanceMessageId?: number | null;
    /**
     * 上傳人員
     */
    uploadUserId?: number | null;
    uploadUserName?: string | null;
    uploadUserCode?: string | null;
};

export type ValuesAppendFormData = {
    [key: string]: any;
};
