import { FormInstance } from '@/common/models/clinico/bpm/formInstance.model';
import { FormInstanceTask } from '@/common/models/clinico/bpm/formInstanceTask.model';
import {
    CommonSearchPageParams,
    CommonSearchParams,
} from '@/common/types/common.controller.type';
import { CommonPageInfo } from '@/common/types/common.type';
import {
    IsBoolean,
    IsDateString,
    IsEnum,
    IsInt,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
} from 'class-validator';
import { EnumInstanceStatus, InstancesValuesData } from './formInstance.type';
import { FormFieldStatusItem } from '../../form/types/form.settings.type';
import { FilterQueryMetadata } from '@/common/helpers/jsonbQuery/type';
import { AssigneeUser } from '@/modules/camunda/providers/taskProperty/taskProperty.type';
import { JSONSchema } from 'class-validator-jsonschema';

export class FormInstanceQuery {
    @IsInt()
    @IsOptional()
    formId?: number;

    @IsEnum(EnumInstanceStatus)
    @IsOptional()
    status?: number;

    @IsString()
    @IsOptional()
    code?: string;

    @IsString()
    @IsOptional()
    formCode?: string;

    @IsInt()
    @IsOptional()
    taskUserid?: number;

    @IsInt()
    @IsOptional()
    ownerCompanyId?: number;

    @IsInt()
    @IsOptional()
    ownerUserId?: number;

    @IsString()
    @IsOptional()
    ownerUserCode?: string;

    @IsInt()
    @IsOptional()
    ownerDepartmentId?: number;

    @IsString()
    @IsOptional()
    ownerDepartmentCode?: string;

    @IsDateString()
    @IsOptional()
    @JSONSchema({
        description: '表單結案時間 - 查詢起始時間',
    })
    finishDateRange1?: Date;

    @IsDateString()
    @IsOptional()
    @JSONSchema({
        description: '表單結案時間 - 查詢結束時間',
    })
    finishDateRange2?: Date;

    @IsDateString()
    @IsOptional()
    @JSONSchema({
        description: '起單時間 - 查詢起始時間',
    })
    startDateRange1?: Date;

    @IsDateString()
    @IsOptional()
    @JSONSchema({
        description: '起單時間 - 查詢結束時間',
    })
    startDateRange2?: Date;

    @IsInt()
    @IsOptional()
    @JSONSchema({
        description: '被引用 formInstance 的 id',
    })
    beReferencedId?: number;

    @IsEnum(EnumInstanceStatus)
    @IsOptional()
    @JSONSchema({
        description: '被引用 formInstance 的狀態',
    })
    beReferencedStatus?: EnumInstanceStatus;

    @IsInt()
    @IsOptional()
    currentAssignedUserid?: number;
}

export class SearchBody extends CommonSearchPageParams {
    @IsObject()
    @IsOptional()
    @JSONSchema({
        description: '查詢表單實例的條件-表單基礎資訊',
    })
    formInstanceParams?: FormInstanceQuery;

    @IsObject()
    @IsOptional()
    @JSONSchema({
        description: '查詢表單實例的條件-表單內容資訊',
    })
    formValueFilter?: FilterQueryMetadata;
}

export class SearchParams extends CommonSearchParams {
    @IsInt()
    @IsOptional()
    formId?: number;

    @IsEnum(EnumInstanceStatus)
    @IsOptional()
    status?: number;

    @IsString()
    @IsOptional()
    code?: string;

    @IsString()
    @IsOptional()
    formCode?: string;

    @IsInt()
    @IsOptional()
    @JSONSchema({
        description: '被參考 formInstance 的 id',
    })
    beReferencedId?: number;

    @IsEnum(EnumInstanceStatus)
    @IsOptional()
    @JSONSchema({
        description: '被參考 formInstance 的狀態',
        nullable: true,
    })
    beReferencedStatus?: EnumInstanceStatus;

    @IsEnum(EnumInstanceStatus)
    @IsOptional()
    @JSONSchema({
        description:
            '排除掉被參考 formInstance 的狀態，回傳會包含 beReferencedStatus 為 null 的資料',
        nullable: true,
    })
    notBeReferencedStatus?: EnumInstanceStatus;
}

export class SearchTaskParams extends CommonSearchParams {
    @IsBoolean()
    @IsOptional()
    previous?: boolean;
}

export class SearchReturnTaskParams {
    @JSONSchema({
        description: '前端是否為 superUser 狀態(後端需驗證)',
    })
    @IsBoolean()
    @IsOptional()
    isSuperUser?: boolean;
}

export class CreateParams {
    @IsInt()
    @IsNotEmpty()
    formId: number;

    // @IsObject()
    @IsOptional()
    values?: JSON | InstancesValuesData;

    @IsInt()
    @IsNotEmpty()
    ownerCompanyId: number;

    @IsInt()
    @IsNotEmpty()
    ownerDepartmentId: number;

    @IsInt()
    @IsOptional()
    referenceFormInstanceId?: number;

    @IsString({ each: true })
    @IsOptional()
    attachmentS3Keys?: string[];
}

export class ApproveTaskParams {
    // @IsObject()
    @IsOptional()
    values?: JSON | InstancesValuesData;

    @IsString()
    @IsOptional()
    memo?: string;

    @IsString({ each: true })
    @IsOptional()
    attachmentS3Keys?: string[];
}

export class EditTaskParams extends ApproveTaskParams {
    @IsObject()
    @IsOptional()
    values?: JSON | InstancesValuesData;

    @IsString()
    @JSONSchema({
        description: `
        編輯表單的留言訊息:
        \${欄位名稱}變更，變更前「\${Before}」，變更後「\${After}」
        `,
    })
    message: string;
}

export class AdditionalDocsTaskParams extends ApproveTaskParams {}

export class RejectTaskParams extends ApproveTaskParams {}

export class CancelTaskParams extends ApproveTaskParams {}

export class ReconsiderParams extends ApproveTaskParams {}

export class ReturnTaskParams extends ApproveTaskParams {
    @IsOptional()
    @IsString()
    processTaskId: string;
}

export class MessageParams extends ApproveTaskParams {
    @IsString()
    @IsNotEmpty()
    message: string;

    @IsInt()
    @IsOptional()
    taskId?: number;
}

export class UpdateMessageParams {
    @IsString()
    @IsNotEmpty()
    message: string;
}

export class CurrentTask extends FormInstanceTask {
    onlyApproved?: boolean;
    allowAddAssignee?: boolean;
    allowAdditionalDocs?: boolean;
    formFieldStatus?: FormFieldStatusItem[];
    additionalDocsUsers?: AssigneeUser[];
    appendFormData?: {
        keys: string[];
    };
}

export class FormInstanceResult extends FormInstance {
    currentTask?: Partial<CurrentTask> | null;
    allCurrentTasks?: CurrentTask[];
    allowRead?: boolean;
}

export class SearchFormInstanceResult {
    rows: FormInstanceResult[];
    count: number;
    pageInfo?: CommonPageInfo;
}
