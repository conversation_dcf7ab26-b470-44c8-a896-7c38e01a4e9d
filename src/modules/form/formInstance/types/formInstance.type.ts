import { CommonSearchParams } from '@/common/types/common.type';
import { Variables } from '@/modules/camunda/types/processInstance.type';

export type InstancesValuesData = {
    [key: string]: any;
};

export const enum EnumPrefixVariableName {
    PrefixFormData = 'form_data_', // 表單資料
}

export const enum EnumUserVariableName {
    CompanyId = 'company_id', // 公司編號
    DepartmentId = 'department_id', // 部門編號
}

export enum EnumInstanceStatus {
    Waiting = 0,
    Completed = 1,
    Canceled = 2,
    Rejected = 3,
    Reconsidered = 4, //再議
}

export type SearchParams = CommonSearchParams & {
    ids?: number[];
    formId?: number;
    formCode?: string;
    processInstanceId?: string;
    startDate?: Date;
    startDateRange1?: Date;
    startDateRange2?: Date;
    finishDate?: Date;
    finishDateRange1?: Date;
    finishDateRange2?: Date;
    ownerCompanyId?: number;
    ownerDepartmentId?: number;
    ownerDepartmentCode?: string;
    ownerUserId?: number;
    ownerUserCode?: string;
    status?: number;
    createdUserId?: number;
    currentAssignedUserid?: number;
    taskUserid?: number;
    code?: string;
    beReferencedId?: number;
    beReferencedStatus?: number;
    notBeReferencedStatus?: number;
};

export type ConvertCreateParams = {
    formId: number;
    values?: JSON;
    ownerCompanyId: number;
    ownerDepartmentId: number;
    ownerUserId: number;
    createdUserId: number;
    // for camunda
    // variables?: Variables; // 目前看似沒使用，先註解
    referenceFormInstanceId?: number;
    attachmentS3Keys?: string[];
};

export type CreateParams = {
    formId: number;
    values?: JSON | InstancesValuesData;
    ownerCompanyId: number;
    ownerDepartmentId: number;
    ownerUserId: number;
    createdUserId: number;
    // for camunda
    // variables?: Variables; // 目前看似沒使用，先註解
    referenceFormInstanceId?: number;
    attachmentS3Keys?: string[];
};

export type UpdateParams = {
    id: number;
    values?: any;
    finishDate?: Date;
    status?: EnumInstanceStatus;
    deleted?: boolean;
    updateUserId: number;
};

export type DeleteParams = {
    id: number;
    values?: JSON | InstancesValuesData;
    memo?: string;
    updateUserId: number;
};

export type CancelParams = DeleteParams;

export type ReconsiderParams = DeleteParams & {
    taskId: number;
};

export type MessageParams = DeleteParams & {
    message: string;
    taskId?: number;
    attachmentS3Keys?: string[];
};

export type UpdateMessageParams = {
    id: number;
    messageId: number;
    message: string;
    updateUserId: number;
};

export type AppendAttachmentParams = {
    values: string;
    formInstanceId: number;
    formInstanceTaskId: number; //紀錄是由哪個Task附加檔案
    attachmentS3Keys: string[];
};

export type AppendFormDataParams = {
    values: string;
    taskVariables: Variables;
    formInstanceId: number;
};
