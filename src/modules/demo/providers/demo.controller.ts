import { Helpers } from '@clinico/clinico-node-framework';
import { Body, Controller, Get, Put } from 'routing-controllers';

@Controller('/demo')
export class DemoController {
    @Get('/')
    async hello() {
        return Helpers.Json.success('Hi, Demo');
    }

    @Put('/')
    async demo(@Body() body: any) {
        console.log({ body });
        return Helpers.Json.success('Hi, Demo');
    }
}
