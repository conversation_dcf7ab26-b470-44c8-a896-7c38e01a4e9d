import {
    AliasInfoType,
    AliasMapType,
    AliasValueRowInfoType,
} from '@/common/helpers/alias.helper.type';
import { SearchBody } from '@/modules/form/formInstance/types/forminstance.controller.type';
import { Type } from 'class-transformer';
import { ValidateNested, IsOptional, IsString } from 'class-validator';
import { JSONSchema } from 'class-validator-jsonschema';

export class ReqFormInstanceSearchBodyDTO extends SearchBody {
    @JSONSchema({
        description: '匯出的表單內容的額外資訊 (新版)',
    })
    @ValidateNested({
        each: true,
    })
    @IsOptional()
    @Type(() => AliasValueRowInfoDTO)
    exAliasValueRows?: AliasValueRowInfoType[];
}

export type ExFormRowTitleName = {
    name: string;
};

export class AliasValueRowInfoDTO
    implements ExFormRowTitleName, AliasValueRowInfoType
{
    @ValidateNested({
        each: true,
    })
    @Type(() => AliasInfoDTO)
    aliasInfo: AliasInfoType[];

    @ValidateNested({
        each: true,
    })
    @IsOptional()
    @Type(() => AliasMapDTO)
    aliasMap?: AliasMapType[] | undefined;

    @JSONSchema({
        description:
            '欄位值的組成方式，可以用 ${_0} 表示第一個 aliasInfo 的值、${contract[0]} 表示 contract[0] 的值、${alias1} 表示 alias1 的值',
        example:
            '${_0}_${_1} | ${contract[0]}_${contract[1]} | ${alias1}_${alias2}',
    })
    @IsString()
    @IsOptional()
    template?: string | undefined;

    @JSONSchema({
        description: '欄位顯示名稱',
        example: '合約類別',
    })
    @IsString()
    name: string;
}

export class AliasInfoDTO implements AliasInfoType {
    @JSONSchema({
        description: 'formValue 路徑, array 可以用 [i] 表示',
        example: 'contract[0]',
    })
    @IsString()
    path: string;

    @JSONSchema({
        description: 'formValue alias，給 template 方便使用(選填)',
        example: 'alias1',
    })
    @IsString()
    @IsOptional()
    aliasKey?: string | undefined;
}

export class AliasMapDTO implements AliasMapType {
    @JSONSchema({
        description: 'formValue 路徑取得的值做轉換 key',
        example: 'ProductSales',
    })
    @IsOptional()
    key: any;

    @JSONSchema({
        description: 'formValue 路徑取得的值做轉換 value',
        example: '產品銷售契約',
    })
    @IsOptional()
    value: any;
}

export type ExFormValuesInfoType = {
    valuePath: string;
    name: string;
};

export class ExFormValuesInfoDTO implements ExFormValuesInfoType {
    @IsString()
    @JSONSchema({
        description: '匯出的表單內容的欄位路徑',
    })
    valuePath: string;

    @IsString()
    @JSONSchema({
        description: '匯出的表單的欄位名稱',
    })
    name: string;
}
