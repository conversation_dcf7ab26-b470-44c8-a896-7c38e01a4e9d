import { Inject, Service } from 'typedi';
import { BaseExcelService, ExcelSheet } from './base.excel';
import { FormInstanceUseCase } from '@/modules/form/formInstance/providers/formInstance.usecase';
import { UserPayload } from '../../organization/types/user.type';
import { ReqFormInstanceSearchBodyDTO } from '../types/excel.type';
import { ExcelFormInstanceService } from './excel.formInstance.service';

@Service()
export class ExcelFormInstanceUseCase extends BaseExcelService {
    @Inject()
    private formInstanceUCase: FormInstanceUseCase;
    @Inject()
    private excelFormInstanceSvc: ExcelFormInstanceService;

    async exportFormInstancesToExcel(
        params: ReqFormInstanceSearchBodyDTO,
        userPayload: UserPayload,
    ) {
        if (params.offset) {
            params.offset = 0;
        }
        if (params.limit) {
            params.limit = 1000;
        }
        const { rows } = await this.formInstanceUCase.searchByUserPayload(
            params,
            userPayload,
        );
        const data = this.excelFormInstanceSvc.genExportFormInstanceRowData(
            rows,
            params.exAliasValueRows ?? [],
        );
        const rowTitle = this.excelFormInstanceSvc.getFormInstanceRowTitle(
            params.exAliasValueRows ?? [],
        );
        const sheet: ExcelSheet<any> = {
            name: '簽核紀錄',
            rowTitle,
            data,
        };
        return this.genBySheets([sheet]);
    }
}
