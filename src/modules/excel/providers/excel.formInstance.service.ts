import { Service } from 'typedi';
import { ExFormRowTitleName } from '../types/excel.type';
import {
    CurrentTask,
    FormInstanceResult,
} from '@/modules/form/formInstance/types/forminstance.controller.type';
import { InstancesValuesData } from '@/modules/form/formInstance/types/formInstance.type';
import { AliasValueRowInfoType } from '@/common/helpers/alias.helper.type';
import { getAliasValueRow } from '@/common/helpers/alias.helper';

@Service()
export class ExcelFormInstanceService {
    /**
     * 匯出 Excel 欄位包含以下；
     *  - 編號
     *  - 表單名稱
     *  - 起單日期
     *  - 結案日期
     *  - 起單人員部門
     *  - 起單人員
     *  - 當前階段
     *  - 狀態
     *  - <<對應表單的詳細欄位 (上傳檔案不允呈現)>>
     * @returns
     */
    private getBaseFormInstanceRowTitle() {
        const baseTitles: string[] = [
            '編號',
            '表單名稱',
            '起單日期',
            '結案日期',
            '起單人員部門',
            '起單人員',
            '當前階段',
            '狀態',
        ];
        return baseTitles;
    }

    getExFormInstanceRowTitle(params: ExFormRowTitleName[]) {
        const exTitles: string[] = params.map((param) => {
            return param.name;
        });
        return exTitles;
    }

    getFormInstanceRowTitle(exAliasValueRows: AliasValueRowInfoType[]) {
        return this.getBaseFormInstanceRowTitle().concat(
            this.getExFormInstanceRowTitle(exAliasValueRows),
        );
    }

    genExportFormInstanceRowData(
        data: FormInstanceResult[],
        exAliasValueRows: AliasValueRowInfoType[],
    ) {
        return data.map((formInstance) => {
            const baseRow = {
                編號: formInstance.id,
                表單名稱: formInstance.form?.name,
                起單日期: formInstance.startDate ?? '',
                結案日期: formInstance.finishDate ?? '',
                起單人員部門: formInstance.ownerDepartment?.name,
                起單人員: formInstance.ownerUser?.name,
                當前階段: this.getFormInstanceCurrentStage(
                    formInstance.allCurrentTasks ?? [],
                ),
                狀態: this.getFormInstanceStatus(formInstance.status),
            };
            const exRow = this.getExportFormValue(
                formInstance.values ?? {},
                exAliasValueRows,
            );
            return Object.assign(baseRow, exRow);
        });
    }

    private getFormInstanceCurrentStage(allCurrentTasks: CurrentTask[]) {
        return allCurrentTasks[0]?.name ?? '';
    }

    private getFormInstanceStatus(status: number) {
        switch (status) {
            case 0:
                return '執行中';
            case 1:
                return '已完成';
            case 2:
                return '已取消';
            case 3:
                return '已拒絕';
            default:
                return '';
        }
    }

    getExportFormValue(
        formValue: InstancesValuesData,
        exAliasValueRows: AliasValueRowInfoType[],
    ) {
        return exAliasValueRows.reduce((acc, cur) => {
            const data = getAliasValueRow(cur, formValue);
            acc[cur.name] = data;
            return acc;
        }, {});
    }
}
