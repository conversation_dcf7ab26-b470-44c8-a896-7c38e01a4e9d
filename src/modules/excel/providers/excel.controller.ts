import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UseBefore } from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { Context } from 'koa';
import { Body, Post, Ctx } from 'routing-controllers';
import { ReqFormInstanceSearchBodyDTO } from '../types/excel.type';
import { UserPayload } from '@/modules/organization/types/user.type';
import { ExcelFormInstanceUseCase } from './excel.formInstance.usecase';
import * as mime from 'mime-types';
import { Helpers } from '@clinico/clinico-node-framework';
import { UserAuthKoaInterceptor } from '@/common/interceptors/userAuth.interceptor';

@Service()
@JsonController('/excel')
@UseBefore(UserAuthKoaInterceptor)
export class ExcelController {
    @Inject()
    private excelFormInstanceUCase: ExcelFormInstanceUseCase;

    @Post('/form/instances/search')
    async searchMany(
        @HeaderParam('authorization') authorization: string,
        @Body() body: ReqFormInstanceSearchBodyDTO,
        @Ctx() ctx: Context,
    ) {
        const userPayload: UserPayload = ctx.req['user'];
        const content =
            await this.excelFormInstanceUCase.exportFormInstancesToExcel(
                body,
                userPayload,
            );
        const fileName = `formInstance-${Helpers.Date.now().format(
            'YYYYMMDDHHmmss',
        )}.xlsx`;
        const mimetype = mime.lookup(fileName);

        ctx.set('Content-filename', fileName);
        ctx.set('Content-disposition', 'attachment; filename=' + fileName);
        ctx.set('Content-type', <string>mimetype);
        return content;
    }
}
