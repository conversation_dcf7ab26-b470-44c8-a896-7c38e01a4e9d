import { ExcelFormInstanceService } from './excel.formInstance.service';

describe('ExcelFormInstanceService', () => {
  const service = new ExcelFormInstanceService();
  const formValue = {
    bpmOptions: {
      formName: 'formName',
      formId: '01',
    },
    testStr: 'a',
    testInt: 1000,
  }
  const exFormTitle = [
    { name: 'row10' },
    { name: 'row100' },
  ];
  describe('getExFormInstanceRowTitle', () => {
    it('should return an array of string', () => {
      const result = service.getExFormInstanceRowTitle(exFormTitle);
      expect(result).toEqual(['row10', 'row100']);
    });
  });
});