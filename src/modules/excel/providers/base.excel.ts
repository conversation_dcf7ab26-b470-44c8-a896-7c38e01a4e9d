import { Utils } from '@clinico/clinico-node-framework';
import configs from '@/configs';

export interface ExcelSheet<T> {
    name: string;
    rowTitle: string[];
    data: T[];
}

// @Service()
export abstract class BaseExcelService {
    /**
     * 建立新表單名，並讓excelTemplate 選擇新表單
     * @param name 新表單名
     * @param excelTemplate Utils.Excel2Templater
     */
    createAndSelectSheet(name: string, excelTemplate: Utils.Excel2Templater) {
        excelTemplate.addSheet(name);
        excelTemplate.sheet(name);
    }

    /**
     * initial Utils.Excel2Templater, 使用 .../excelEmptyTemplate.xlsx
     * @returns Utils.Excel2Templater
     */
    async initExcelEmptyTemplate() {
        const excelTemplate = new Utils.Excel2Templater();
        await excelTemplate.load(
            configs.templateFolder + '/excel/excelEmptyTemplate.xlsx',
        );
        excelTemplate.removeSheet(1);
        return excelTemplate;
    }

    /**
     * 產生表頭(粗體)
     * @param excelTemplate Utils.Excel2Templater
     * @param data string[] 要產生的表頭 string[]
     * @returns
     */
    genRowTitle(excelTemplate: Utils.Excel2Templater, data: string[]) {
        excelTemplate.fillRow({
            index: 1,
            data,
            cellOptions: {
                font: {
                    bold: true,
                },
                alignment: {
                    horizontal: 'fill',
                    vertical: 'middle',
                },
            },
        });
        return excelTemplate;
    }

    /**
     * 根據T[]，產生表單內容
     * @param excelTemplate Utils.Excel2Templater
     * @param data T[]
     * @param startRowIndex default:2, 預設第二行開始長內容
     * @returns
     */
    genRowData<T>(
        excelTemplate: Utils.Excel2Templater,
        data: T[],
        startRowIndex = 2,
    ) {
        excelTemplate.fillRows({
            startRowIndex,
            data,
        });
        return excelTemplate;
    }

    /**
     * 根據ExcelSheet[] 產生excel 工作表
     * ExcelSheet[] 動態產生多工作表內容
     * @param sheets ExcelSheet[] 自定義工作表內容
     * @returns
     */
    async genBySheets(sheets: ExcelSheet<any>[]) {
        const excelTemplate = await this.initExcelEmptyTemplate();
        for (const { name, rowTitle, data } of sheets) {
            this.createAndSelectSheet(name, excelTemplate);
            this.genRowTitle(excelTemplate, rowTitle);
            this.genRowData(excelTemplate, data);
        }
        return excelTemplate.saveToBuffer();
    }
}
