import { ClinicoDataSource } from '@/common/databases/clinico.database';
import { UserPermission } from '@/common/models/clinico/bpm/userPermission.model';
import { CommonSearchResult } from '@/common/types/common.type';
import { Service } from 'typedi';
import { FindOptionsWhere } from 'typeorm';
import { SearchPermissionParams } from '../types/permission.type';
import _ from 'lodash';

@Service()
export class PermissionRepository {
    protected repository = ClinicoDataSource.getRepository(UserPermission);

    private convertJsonb(data: UserPermission) {
        data.permission =
            data.permission && _.isString(data.permission)
                ? JSON.parse(data.permission)
                : data.permission;
        return data;
    }

    async findByUserId(userId: number): Promise<UserPermission | null> {
        const data = await this.search({ userId });
        return data.count == 0 ? null : data.rows[0];
    }

    private async search(
        params: SearchPermissionParams,
    ): Promise<CommonSearchResult<UserPermission>> {
        const filters: FindOptionsWhere<UserPermission> = {};
        if (params.userId) {
            filters.userId = params.userId;
        }

        const [rows, count] = await this.repository.findAndCount({
            relations: {
                user: true,
            },
            where: filters,
        });

        const result: CommonSearchResult<UserPermission> = {
            rows: rows.map((i) => this.convertJsonb(i)),
            count,
        };
        return result;
    }
}
