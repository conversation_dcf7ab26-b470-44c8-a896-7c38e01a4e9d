import { Inject, Service } from 'typedi';
import { PermissionRepository } from './permission.repository';
import { FormInstanceResult } from '@/modules/form/formInstance/types/forminstance.controller.type';
import { UserPayload } from '@/modules/organization/types/user.type';

@Service()
export class PermissionUseCase {
    @Inject()
    private repo: PermissionRepository;

    async getInstancePermission(
        userPayload: UserPayload,
        instance: FormInstanceResult,
    ) {
        const userPermission = await this.repo.findByUserId(userPayload.id);
        const isSuperUser =
            userPermission?.permission &&
            userPermission.permission['isSuperUser'];
        const isOwnUser = userPayload.id == instance.ownerUserId;

        return {
            isSuperUser,
            isOwnUser,
        };
    }
}
