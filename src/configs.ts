import { Helpers } from '@clinico/clinico-node-framework';

const DEFAULT_AWS_BUCKET = Helpers.Env.isProduction()
    ? 'clinico-bpm-prod'
    : 'clinico-bpm-dev';

export default {
    tempFolder: __dirname + '../temp',
    templateFolder: __dirname + '/../templates',
    app: {
        port: parseInt(process.env['APP_PORT']!) || 3000,
        bodySize: '100mb',
    },
    sentry: {
        dsn: process.env['SENTRY_DSN']!,
        ignoreErrors: [/^Relogin required/],
    },
    auth: {
        key: process.env['AUTH_KEY'],
        secret: process.env['AUTH_SECRET'],
        erp: {
            key: process.env['ERP_AUTH_KEY'],
            secret: process.env['ERP_AUTH_SECRET'],
        },
    },
    graphql: {
        debug: Helpers.Str.isTrue(process.env['GRAPHQL_DEBUG']),
        playground: Helpers.Str.isTrue(process.env['GRAPHQL_PLAYGROUND']),
    },
    grpc: {
        port: parseInt(process.env['GRPC_PORT']!) || 50051,
        proto: {
            path: __dirname + '/../protobuf/main.proto',
        },
    },
    database: {
        clinico: {
            host: process.env['DB_HOST'],
            port: 5432,
            name: process.env['DB_NAME'],
            user: process.env['DB_USER'],
            password: process.env['DB_PASSWORD'],
            entities: [__dirname + '/common/models/clinico/**/*.model.{ts,js}'],
        },
        redis: {
            host: process.env['REDIS_HOST']!,
            port: parseInt(process.env['REDIS_PORT']!) || 6379,
        },
        logging: Helpers.Str.isTrue(process.env['DB_LOGGING']),
    },
    jwt: {
        secret: process.env.JWT_SECRET!,
        expiresIn: 60 * 60 * 24 * 7,
    },
    sso: {
        url: process.env['SSO_URL'] || 'https://sso.clinico.cloud/api/verify',
        headers: {},
    },
    camunda: {
        url: process.env['CAMUNDA_URL'] || 'http://localhost:8080/engine-rest',
    },
    aws: {
        key: process.env['AWS_KEY']!,
        secret: process.env['AWS_SECRET']!,
        endpoint: process.env['AWS_ENDPOINT'],
        region: process.env['AWS_REGION'],
        s3: {
            bucket: process.env['AWS_BUCKET'] || DEFAULT_AWS_BUCKET,
        },
    },
    smtp: {
        url:
            process.env['SMTP_URL'] ||
            'http://micro-smtp-api.cloud.clinico.com.tw/api/send/notify',
        headers: {
            'license-source':
                process.env['SMTP_LICENSE_SOURCE'] || 'clinico-bpm-api',
            'license-token':
                process.env['SMTP_LICENSE_TOKEN'] ||
                'VUF6P4DIUJHMHXAJPOD4QWBXM7C3ZYHRQ4D16B7CLLLDT5KJ8GAWC3YEEALX52I4',
        },
    },
    webDomain:
        process.env.APP_WEB_DOMAIN || 'http://clinico-bpm-web.clinico.cloud',
};
