# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [3.3.1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.3.0...v3.3.1) (2025-07-29)


### Features

* **assignee:** generalManagerProcess 增加新版 HR 系統副董級以上條件排除，searchUsersByDeptCodes 使用 generalManagerProcess ([2b2ea5a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2b2ea5a3f6ef5642c1fe0f7058af44802bf1be10))

## [3.3.0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.2.2...v3.3.0) (2025-07-16)


### Features

* **diagram:** 調整科林 申請合約用印 流程 [#121421](https://asking.clinico.com.tw/issues/121421) ([b4344d7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b4344d74fd9fb0ca056bb63dd4f98c165f3bc282))
* **diagram:** 調整科林 簽呈單(新) 流程 [#119850](https://asking.clinico.com.tw/issues/119850) ([91298d2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/91298d23de7d0bc2823ddce4cee640c20a90350f))
* **taskAssignee:** FormTaskAssigneeUseCase 移除 _getSpecifyUserByVariableUserStr 功能 ([216e110](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/216e1104964dc95f90b7f6494e1c9965a566d08a))

### [3.2.2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.2.1...v3.2.2) (2025-07-16)


### Features

* **formConf:** FormConfTaskAssigneesUseCase respoitory 增加 formVersion 條件 [#113984](https://asking.clinico.com.tw/issues/113984) ([b374aaa](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b374aaaf999667978619b6306ec76918932325a7))
* **formConf:** FormConfTaskAssigneesUseCase 增加排序 taskAssignees ([65f4604](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/65f46045d25fffbf1814cf0500afc8d95495c0c8))
* **formConf:** 增加 formConfTaskAssignees 相關模組、欄位增加 configFilter 的 FilterMatchQuery ([1a085bb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1a085bbeff8a2aafa8bca2e4cad858bf386ef6ae))
* **formConf:** 增加 TaskAssigneesFilterDataDTO 給 genGetFormTaskAssigneesFn 使用 ([f305db7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f305db7b4da7b02d8b2e1ceb92bd36793b226b5e))
* **formConf:** 調整 genGetFormTaskAssigneesFn configFilter isMatch 為 false 時，關卡 FormTaskAssigneeInfo 預設空的 ([6ec5ff3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6ec5ff3706097f4aec8b8b59c9ee2d28541e34d8))
* **formInstance:** 增加 GET formInstances/:id/preview/tasks-assignees 入口 ([7303a6f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7303a6ff20a80df7338781ce6ff30d0fe886edc6))
* **model:** FormInstance 增加 formVersion && FormConfTaskAssignee 增加 stepName、formVersion [#113984](https://asking.clinico.com.tw/issues/113984) ([09a4fe6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/09a4fe603f5ffd8f618006461b412cc90050258e))
* **stepTaskAssignee:** 新增 StepTaskAssigneeService 處理各關卡的不同簽核類型的資料集 ([99f8298](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/99f82980874fb7dafd0cbecd5d22bcf68b87bbe0))
* **taskAssignee:** 新增 getPreviewFormInstanceTaskAssignees 預覽 instance 簽核人員清單 (step base 版) ([50c966b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/50c966b6dc6f1fa7127b56ff82cc8ba27072ef8e))
* **task:** taskAssigneeService genFormSpecifyUserMap, genFormValueSpecifyUsersMap params 改使用 formTaskAssignees ([b188722](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b188722f57246457f7517ed7e7a1f670ad260695))


### Bug Fixes

* **taskAssignee:** genVariablesByFormTaskAssignee, genFormSpecifyUserMap 在關卡沒有 formSpecifyUsers 還是產生一個空陣列供多人簽核使用 ([52a0c1f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/52a0c1ffdd6ef227fdc4bd4e0c3326cf77d5b3a0))

### [3.2.1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.2.0...v3.2.1) (2025-07-15)


### Features

* **assignee:** 人資系統異動，調整 /supervisors, /users/region 組織樹狀，產生虛擬部門公司根節點 ([5fd06aa](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5fd06aa3c504dc022b83c9b9bf37d3c506557c86))
* **diagram:** 調整科明簽呈流程把營運長、執行長的關卡都拿掉 ([e1b5424](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e1b54247b680b1d78a3590475d5a3e4fcd7d6352))
* **smtp:** 新增 SMTP 配置環境變數 ([4c84879](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4c848795751b40c58499f5c6f9de9e2bc707f852))

## [3.2.0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.1.3...v3.2.0) (2025-07-04)


### Features

* **formConf:** 配置各個狀態下的 currentTask filterData 給 formFieldStatusesFn 使用 [#120989](https://asking.clinico.com.tw/issues/120989) ([82363a7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/82363a7d79e386805a939c11bbd8b2d26d5b1053))
* **formConf:** FormConfFieldStatusUseCase 使用 formFieldStatusesFn 個別控制 filterMetadata 過濾條件 [#120989](https://asking.clinico.com.tw/issues/120989) ([e1d6ecf](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e1d6ecf69ad2347c1be5bd152233c6aa5f80872b))
* **formInstanceTask:** 增加表單內容更新 API (與留言共用同邏輯)  [#121026](https://asking.clinico.com.tw/issues/121026) ([113509d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/113509d2b5a4345f7e167900a1001d85febf0f54))
* **formInstanceTask:** 增加表單內容更新 API 路由 [#120989](https://asking.clinico.com.tw/issues/120989) ([79b709c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/79b709c7b6e9b781da7758677d9fb4f4c1f48f0f))
* **taskProperty:** 配置 allowFormEditorUsers, allowFormEditor 供 currentTask 上配置，供前端判斷是否允許使用者編輯表單 [#120989](https://asking.clinico.com.tw/issues/120989) ([26a66f2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/26a66f25160c0fa4fb47d4edc8bed94565ea1777))


### Bug Fixes

* **formValues:** 回傳的 appendFormData 與 instanceValue 改用 _.merge 合併 ([33a218d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/33a218da97152a55a900499331d88fffc6fbac3e))
* **smtp:** 修正 email notify template 讓 context 可以直接渲染 html 格式  [#121026](https://asking.clinico.com.tw/issues/121026) ([291ad6a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/291ad6ab798d4c463ae7c398910079f8340d1ee5))

### [3.1.3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.1.2...v3.1.3) (2025-06-30)


### Features

* **diagram:** 新增科林簽呈（新）流程 [#119850](https://asking.clinico.com.tw/issues/119850) ([fe21031](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/fe21031793f0a86a837201c0aadf7fca492206eb))
* **diagram:** 調整科林簽呈（新）流程 [#119850](https://asking.clinico.com.tw/issues/119850) ([066fdf1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/066fdf1b40b5b4029be7dc5b141a5dc43cf19b24))
* **form.conf:** form formFieldStatus 設定檔增加 finish 配置 ([74bc5b9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/74bc5b983db501b430823962fa22cac4bdad78c6))
* **form.conf:** form formFieldStatus 設定檔增加 override 配置，強制覆蓋全部配置 ([8990fdf](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8990fdf6d39fa0bd747cf8b36e91888d0c747b3a))
* **form:** form taskSetting FormTaskSpecifyUser add departmentCode property [#119850](https://asking.clinico.com.tw/issues/119850) ([728cadb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/728cadb2a84849e3dbce5d7ad6f79e5020c9edfb))
* **formInstance:** 已完成、取消的簽核單，產生虛擬的 currentTask 存放結束的 formFieldStatus ([c3a70d2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c3a70d2a7b8b9d256a733cb1ee80729ccaa579ae))

### [3.1.2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.1.1...v3.1.2) (2025-06-20)


### Features

* **taskProperty#120509:** 增加關卡上傳檔案副檔名限制的配置 ([17fc000](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/17fc00052e36b5626e4e0abadf31fc0c844d17ad)), closes [taskProperty#120509](https://asking.clinico.com.tw/issues/120509)

### [3.1.1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.1.0...v3.1.1) (2025-06-17)


### Features

* **diagram#118162:** 2025-06-06 合約用印系統 流程調整 ([4ac1185](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4ac1185c77cc89951c4b02291f707c9e829be28d)), closes [diagram#118162](https://asking.clinico.com.tw/issues/118162)
* **diagram:** 2025-06-09 科林簽呈單 v2 ([d0fda4e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d0fda4e3bd86eca0e7e6be6b5da5909569f7419b))
* **diagram:** 2025-06-11 新增科明流程 ([e7ebd53](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e7ebd532cf0e354be51881b76201bd496f722d0c))
* **form#120417:** form TaskConfig 增加 taskAssigneeFilter 類型判斷 ([8dc8e04](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8dc8e04ca1a244076ba696539a4d0d5e58230fad)), closes [form#120417](https://asking.clinico.com.tw/issues/120417)
* **form#120417:** form TaskConfig 增加 taskAssigneeFilter 類型條件，寫入 formInstanceTask.property ([f7ba74b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f7ba74b08222280e8211db3723cd0ae63d3d2fb8)), closes [form#120417](https://asking.clinico.com.tw/issues/120417)
* **taskProperty#120417:** 增加關卡代簽的配置 ([762eba9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/762eba9cae5b2e4d702675b8f8ec34d801894afc)), closes [taskProperty#120417](https://asking.clinico.com.tw/issues/120417)

## [3.1.0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.36...v3.1.0) (2025-05-22)


### Features

* **diagram:** 【固资租借】类型【服务单】申请目的为【维修顶用】时，需要在签核流程中，加签【固资管理】页面配置的【技术经理】 ([8321583](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/832158323656bd1fd56fa519c392a369554c35c0)), closes [#119144](https://asking.clinico.com.tw/issues/119144)
* **task_webhooks:** 增加關卡任務異動的 webhook Sentry ([6f5a32b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6f5a32b17b1a9272f981638402083e66a8afed33))
* **task_webhooks:** 排除關卡任務異動刪除的設定 ([3e5ab35](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3e5ab35ad65654a6a67346de72ea8b93a6d78dc5))
* **task_webhooks:** 新增關卡任務異動的 webhook 模組 usecase ([88fb686](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/88fb6862d998880021ef95d0581284e322f2c5b7))
* **task_webhooks:** 新增關卡任務異動的 webhook 模組設定檔 ([8a16f28](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8a16f2863fab8422723991d64b08181ddb0ef888))
* **task_webhooks:** 新增關卡任務異動的 webhook 範例 ([8346d54](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8346d5478e95f6635179b5223d9987c3d02d47c7))
* **task_webhooks:** 調整 sample api ([e31c73f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e31c73f71915dfea72466c269530d64a442d9a39))
* **task_webhooks:** 調整關卡任務異動 webhook 錯誤訊息 ([88cd509](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/88cd5095678ff96206402b531371544369e1053f))
* **task_webhooks:** 調整關卡任務異動的 webhook 模組、增加 webhook 異動紀錄 ([eae44df](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/eae44df2d68e3d8fdc4f18d7051ad83be1beb32b))
* **task_webhooks:** 調整關卡任務異動的 webhook 模組調整 webhook 異動紀錄 ([0253e61](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0253e6169e1efb4d4e9a5da97af0b246b05f216c))
* **task_webhooks:** 調整關卡任務異動的 webhook 模組調整 webhook 異動紀錄 rollback ([25a13ce](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/25a13ce3bdd2282f447b76dc6f8a82c2db898df8))
* **task_webhooks:** 調整關卡任務異動的 webhook 模組錯誤處理 ([d1c0d36](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d1c0d36ff9296c0d6536169a91b0c90989ade01c))
* **task_webhooks:** 調整關卡任務異動的 webhook 模組錯誤處理、紀錄前端 appendFormData ([e5774d3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e5774d391ae271c54071f7a0cd22a158ecb8b15a))

### [3.0.36](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.34...v3.0.36) (2025-04-28)


### Features

* **#118162:** 合約用印 - 功能優化 - 當合約類別為「無收付款」+「對方簽署文件(例如：授權書、保證函、公司函等)」 流程調整 ([6ff9ae8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6ff9ae8c698144f8c1063c165d5869f89175f009)), closes [#118162](https://asking.clinico.com.tw/issues/118162)
* **#118162:** 流程調整 改為 Activity_Step2 判斷 ([81ded7e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/81ded7e64e34295e92938aff03bed8c8a081e3c0)), closes [#118162](https://asking.clinico.com.tw/issues/118162)
* **#118162:** Activity_Step2 判斷 initiatorUpload 是否塞起單人上傳雙方合約 ([752e7a8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/752e7a8fa37d5a64a5cc3218c73e68276009806a)), closes [#118162](https://asking.clinico.com.tw/issues/118162)
* 待簽核查詢API ([6bd9ffd](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6bd9ffd2e66898e585ad4a11438099460e41fec9))
* **diagram#118162:** 2025-04-28 合約用印系統 流程調整 ([af0b702](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/af0b70272822c0ed2340979bfdf0f789e3bf75dc)), closes [diagram#118162](https://gitlab.aws.clinico.com.tw/clinico/workflow/diagram/issues/118162)
* **diagram:** 2025-04-01 合約用印系統 流程調整 ([3296014](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/329601458249be0a2906a9b70e5c7a62a257bd64))


### Bug Fixes

* 修正 sortRelation 拿到 id 為字串時的排序異常 ([54adea2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/54adea2b4bd3b34da607d80ce9be1cd67b2ccafa))
* **excel:** 調整 excel AliasMapDTO key 支援 string 以外的資料類型 ([44bac93](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/44bac933301f01048464d6edc93437a10af4d319))

### [3.0.34](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.33...v3.0.34) (2025-04-01)


### Bug Fixes

* 修正 sortRelation 拿到 id 為字串時的排序異常 ([18ca57e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/18ca57eea93403776eeea68731a9807f26daad3a))

### [3.0.33](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.31...v3.0.33) (2025-03-31)


### Features

* **#116357:** 增加「匯出」Excel 功能 ([11d51ea](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/11d51ea3854ead1908ab35e2e60f6dc780ed14cc)), closes [#116357](https://asking.clinico.com.tw/issues/116357)
* **alias.helper:** 增加 alias.helper ([75335fd](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/75335fd9a1edc649ae2f79a98523a786192bc9e6))
* **diagram:** 2025-03-25 中國ERP/BPM 報價單調整 ([a71dc09](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a71dc097c8548098ccfbbf7b879fd4153975b6ee))
* **diagram:** 科明&科林「文管發行」關卡調整多人簽核 [#115865](https://asking.clinico.com.tw/issues/115865) ([7bf5a88](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7bf5a88e19a18a5a58b357f7c9267d2b9e446b56))
* **excel:** 更新匯出的表單內容的資訊功能，改用 exAliasValueRows ([506b04a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/506b04a3102768b3efb6a4cbb7c51db1d2103e93))


### Bug Fixes

* 信件通知內文改HTML ([98853b0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/98853b049d3a08c71846011c136657af77b25890))
* 信件通知內文改HTML ([9751f9f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9751f9fb6c320e0e0b1f8f9379cda3715ac5859c))
* 調整 helpers getNestedFormValue 功能 ([f04d5a6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f04d5a6769d53c385950d95a1cee193bba35c436))
* **excel:** AliasMapType.value 改為 any type ([4e97c14](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4e97c141f9707280f537ec25b231a7103a6b391a))
* **form:** 調整 getFormValueTaskSpecifyUsers 拿 getNestedFormValue 判斷 ([f38b4c7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f38b4c79711b4dc9f0f261a699f29b4be88fdd5f))

### [3.0.31](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.30...v3.0.31) (2025-02-19)


### Features

* 2025-02-12 中國ERP/BPM 衝刺 - 增加 gateway ([1d05cd3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1d05cd3b86c14c05cde6346b66a052448cfecc22))
* **diagram:** 2025-02-12 中國ERP/BPM 衝刺 - 移除未配置的 gateway ([8e77e30](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8e77e301464401a50fc99d3671bdf0afa42eb6fe))
* **diagram:** 2025-02-12 中國ERP/BPM 衝刺 - 增加關卡 isCOO / isCEO 的 bool 判斷 ([51f78dd](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/51f78ddc5c4b68dc5b64bb77a1026b60af379e75))
* **diagram:** 2025-02-12 中國ERP/BPM 衝刺 - 調整服務單 ([796bf99](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/796bf99c0abd184973cd723da0201aad3283d26e))
* **diagram:** 2025-02-12 中國ERP/BPM 衝刺 - 調整服務單v2 ([40bd9c1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/40bd9c1425afed7eab014a615ab4108b965d251f))

### [3.0.30](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.29...v3.0.30) (2025-02-12)


### Features

* 科林、科明的合約、文件用印的 autoFormValues, autoInstanceCode ([ee55ef0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ee55ef084c93aa7042061f3500eb1e8d2b777f4d))
* 調整簽核完成 - 信件通知簽核完成狀態 ([7078950](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/707895024f4b3bd225defb34a84b6b2d45dafec2))
* 調整簽核完成 - 信件通知簽核完成狀態，使用 EnumInstanceStatus ([b168e4a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b168e4a61a2f1975cf0d8585ef36c89c326d0987))
* **diagram:** 2025-02-12 中國ERP/BPM 衝刺 ([3b9b0f4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3b9b0f46c1cc8cb051c6dafc5fd0cf182b67818b))
* **diagram:** 中國报价单-采购、请购、报废、展耗流程調整 ([0318338](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/031833894531e4ab398527e15988ac509e3e2c31))
* **diagram:** 科明、科林合約、文件用印申請流程 ([c1c7bd9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c1c7bd9ae54982ba862c90d03a3bd236cb17e46b))

### [3.0.29](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.28...v3.0.29) (2025-02-10)


### Features

* 中國報價單流程增加四個報價流程 ([8c6b578](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8c6b578164f1f21502a5b8ead3ac7376385635af))
* 中國報價單流程增加四個報價流程 ([93bc81b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/93bc81b07fdf9c7c03de02b4a9c1976539b42def))
* 顯示上傳時間，並依時間大到小排序 ([b5ed53d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b5ed53d90e71a55dec7b861d8c4acfd57ed5d695))
* **diagram:** 中國報價單流程調整-財務主管關卡能走總監關卡 ([0c9a35a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0c9a35a62d205dff31b82a42b33de0726389968a))
* **diagram:** 科明HR簽核 ([73a478f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/73a478ff4406ce4589d78cf93f881b0e6f67ba37))
* **diagram:** BPM / 關卡名稱及簽核人調整 [#113983](https://asking.clinico.com.tw/issues/113983) ([8326447](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/83264473b810bd0517b438e7f1606efc1bc5217f))


### Bug Fixes

* **diagram:** 中國報價單流程調整 ([c24b30d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c24b30d2ec46bb851a0ce8dc69aac5a4e5c074f1))

### [3.0.28](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.27...v3.0.28) (2025-01-15)


### Features

* add bpm hook return formInstance.values ([d4993de](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d4993de078ec9d153556c6a9d48e494f2f76779a))


### Bug Fixes

* **forms:** 修正 formFieldStatus key 切分 step, formKey 方式 ([74fd020](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/74fd020428f41eba9138dbcff59566d8d1fa9483))

### [3.0.27](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.26...v3.0.27) (2025-01-07)


### Features

* 起單人關卡支援多人簽核 ([33652e7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/33652e748ac5fc0aa9ad844fa48cf5be901d9631))
* 起單人關卡支援多人簽核-起單關卡移除使用 Promise.all 來同時執行 ([89bd1ef](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/89bd1ef8f5cffb798d6b90c24a6dae41aa4a1a5f))
* 起單人關卡支援多人簽核-產生多人簽核關卡 ([5934736](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/593473620822accda52487deb40a0faa5bf57645))
* 調整 form taskSettings assignees 支援關卡複數設定 ([566dbfc](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/566dbfcd29ce9a47730ba05fa5be4908b114b81e))
* camunda tartTask event 增加 groupCode ([c07c7c8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c07c7c8f26843ff247406568bdee82db34a90518))
* **diagram:** 中國報價單流程調整 ([ee6c540](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ee6c54048ee55f3ff6d99a9023f7277cbd6b54bb))
* **diagram:** 中國報價單流程調整-業務關卡多人簽核 ([eccafc3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/eccafc3d105e3a19d208c4d71f63c1a2b567da7f))
* form taskSettings assignees 支援關卡複數設定 ([0843d11](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0843d113eb68feb5e737801734c2caced21b9fe4))
* task.property 增加 filterAllowSkipUsers 過濾掉不允許跳過的人員 ([82b7d40](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/82b7d40a4cc115ae5825048153f9b1f374d50c40))

### [3.0.26](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.25...v3.0.26) (2024-11-22)


### Features

* **diagram:** cn 展示机 ([2d4b1ea](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2d4b1ea4bdd58d0c13fac3d9f4e9a7c9b65e42ec))
* **diagram:** HR簽核調整 ([9e083a6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9e083a66b6b714be1bfff0b6f72c06ab9ae816b3))
* formInstance value 增加紀錄關卡簽核人員 ([9180174](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/918017478eb20d92a6928851689cd73e8cd25559))

### [3.0.25](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.24...v3.0.25) (2024-11-19)


### Bug Fixes

* 移除過濾篩選簽核人員向上組樹狀結構 ([3b7b37a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3b7b37acf7ba590a07a9b8870d268dc8a93ec3ff))

### [3.0.24](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.23...v3.0.24) (2024-11-14)


### Features

* form 關卡欄位狀態控制支援多狀態配置 ([ab7c5fb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ab7c5fb6575d714c4c9b97f00d34f12da9159469))

### [3.0.23](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.22...v3.0.23) (2024-11-13)


### Features

* 退回功能 - 修改預設為關閉 ([#110837](https://asking.clinico.com.tw/issues/110837)) ([5bab6ec](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5bab6ec64a47ae784c1a20efdef0584812185333))
* 調整額外簽核清單 exAssignee assigneeId 規則 ([4ef2abf](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4ef2abf169608e54e788303ba1dc72653876e9d7))
* 調整額外簽核清單 exAssignee assigneeId 規則 (非 grouping) ([dc4ef6e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/dc4ef6e0a98264d49c43feab0c52fc1df69fc4a1))
* 額外簽核清單 exAssignee 排除空的節點名稱 ([27fc312](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/27fc3120662f496532e6719963eba84813a0de51))
* 額外簽核清單 exAssignee 增加動態 key/value ([e480147](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e480147c2c8465983c4c83440868b048a2c32cc0))
* **diagram:** 門市會員意見回饋 ([3242a72](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3242a72b46d1b4c2ee203a088e7697cb6a6c2f0c))
* **diagram:** 科明 - 申請文件用印單(API) ([#109508](https://asking.clinico.com.tw/issues/109508)) ([5730ebc](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5730ebcd08da2c187ed7a08caa9dbac3bc283887))


### Bug Fixes

* 修正簽核人員清單排除離職人員 ([ff50984](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ff509848c6a4d6667186ea813c7b225e3f3df5d2))

### [3.0.22](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.21...v3.0.22) (2024-10-30)


### Features

* **diagram:** BPM / 簽呈 - 關卡調整 [#110394](https://asking.clinico.com.tw/issues/110394) ([9577ccf](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9577ccf480d73715c19bc95cc5dccac0426736bf))
* **diagram:** 新進人員考核 - HR簽核路線增加 ([28b0fa3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/28b0fa3ed5025f1d0454d470c336ac533689aff9))
* **diagram:** 新進門市會員反應 ([fcc6cae](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/fcc6cae67428a8dffbf4bf107b8635a0aa23b057))
* formTaskAssigneeType 增加 step type 指定某關關卡簽核方式 ([b5aa418](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b5aa418b56d8d74c89c1da466d457288f7d146b7))


### Bug Fixes

* 修正 jsonb boolean typeTransfer & 修正 boolean sql 產生 ([6e8ae1c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6e8ae1cae0d9880d8dd2dbb84d4273f507666d9c))

### [3.0.21](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.20...v3.0.21) (2024-10-21)


### Features

* **diagram:** [#108909](https://asking.clinico.com.tw/issues/108909) 中國簽核系統 / 新增「市場活動」簽核 ([e3de991](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e3de991afde2ddc3a619d1668951426dc490cdb5))
* **diagram:** 中國簽核系統 / 新增「授权商品」簽核 ([f125fb4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f125fb46a7b1a05ddcefc760ae13b710e91d4238))
* **diagram:** 科明 - 申請文件用印單 ([e568366](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e5683668142e01c6cba8c32d36735acbd9d7f0ae))
* mailTo 增加 skip assignee 配置 ([13e627f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/13e627f1080fcf7ad6128adab85aca89d0843280))
* 表單查詢增加篩選條件: 表單結案時間, 起單時間 ([911d37d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/911d37d9660f20ad1a891d6e760b280281afa672))


### Bug Fixes

* FormInstance 改用 update 只更新內容 ([63110b5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/63110b5830e433dea86125b6682c2135d1ad7592))
* 修正 FormInstance 更新內容無 updateUserId ([d483f49](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d483f49a4ed31b8a8fb0cad7336c7d09ba97c9a5))

### [3.0.20](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.19...v3.0.20) (2024-09-23)


### Bug Fixes

* 修正 filterMetadatas 為空時，回傳全部 assignees ([095af90](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/095af909b00f41e37de2dcd9aa10f0f84d2cd203))

### [3.0.19](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.18...v3.0.19) (2024-09-19)

### [3.0.18](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.17...v3.0.18) (2024-09-13)


### Features

* **diagram:** [#105514](https://asking.clinico.com.tw/issues/105514) 展延的签核流程优化 ([6b2d4a4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6b2d4a4ea7fa36bf6cb120f2b2577779cbbebd1a))
* 更新展延-影響人員關卡流程 ([63cf9bf](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/63cf9bf7ab8256670660e3276c755371882d8616))


### Bug Fixes

* 限制給 Camunda Veriable 的字串長度 ([7a9e23e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7a9e23e5e9fb62b7a88eb369f01cc1a08570455c))

### [3.0.17](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.16...v3.0.17) (2024-07-23)


### Features

* 中國簽呈單會簽人員排除David ([52481e1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/52481e19f96ec797420b78d857bafe8a576c2d76))
* 新增外部取消流程API ([89beede](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/89beede47c79ca8bcda77e24eecae2905b63bf17))

### [3.0.16](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.15...v3.0.16) (2024-07-17)


### Bug Fixes

* 修正為只對留言信件內容增加斷行轉換 ([1e32938](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1e329386a37ee6e7c3e073c6985a7f237f95ba78))

### [3.0.15](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.14...v3.0.15) (2024-07-17)


### Features

* 信件內容增加斷行轉換 ([02747a0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/02747a07c0b170f9952f92a57547da333d86a7c0))
* 增加 留言信件通知歷史簽核人員 的關卡 property 參數配置 ([2a17b41](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2a17b415d8563fc328b58e3a06bb51bbb1fac394))
* 增加留言信件通知歷史簽核人員 ([67fc261](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/67fc261651e454f868a5113f7ac610b7e658e49b))

### [3.0.14](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.13...v3.0.14) (2024-07-12)


### Bug Fixes

* 修正 FormInstance save 時帶入關聯的存取錯誤 ([9ab4f12](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9ab4f12536da787cd7a817fd4c2cfb9651f34651))

### [3.0.13](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.12...v3.0.13) (2024-07-09)


### Features

* bpm Institute, Proposal 使用新版簽核方式 ([68c458e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/68c458e6c2872e73be024b25ff2e6a0777858dcb))
* form, instance, instanceTask repository 增加 form conf 關聯 ([1535833](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/15358330929a8c409ed9f3e129b291fbda269af7))
* formUseCase 新增 取得符合個人的表單篩選設定 ([432cd80](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/432cd80151cd77f0b8747408afefeb8358efc41f))
* 取得使用中表單篩選設定 ([5e505b4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5e505b42731564844d146d0c6c9dd67fdc561203))
* 增加 jest package ([de74acd](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/de74acd35158061438a8da868e7d542993e8c16b))
* 定義 form conf 相關的 jsonb 資料結構 ([642d432](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/642d43246d53ac68eded0549d1fe5be3cb45d525))
* 拆分 form 底下的相關設定為個別 model ([3129643](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/31296434faa627b5c926d8b8f7475cbb79cc25e1))
* 新增 form conf 資料處理 service ([f54ef3a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f54ef3a11bc6613086472dfc06fdcc3bfb9cdf77))
* 新增 me/params/forms 取得有簽核過的關聯表單 ([787da2d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/787da2de0cb341295c623909ef80e18861825d5d))
* 新增 userUseCase，新增功能 取得篩選用人員資訊 ([deca03e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/deca03ea372c06ed92b304848d413f1e1706b854))
* 新增表單設定的過濾條件，篩選簽核樹狀結構 ([715eba2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/715eba2298215022b3146d6a1b4ba48f71338c97))
* 簽核單使用 filterMappingMatchQueryData 篩選觀看人員 ([c12feb2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c12feb2f0cb4b1520d02e8b842d1b0025b521ee0))
* 調整 form 相關的 conf models ([6ac9cc8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6ac9cc8a4e390c1d6bdb633cad6bb91a5fc62647))
* 調整 form 相關的 conf models many to one ([5ed30a2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5ed30a2189cb355a6de030cf7890ea99c4b9ef21))


### Bug Fixes

* 預設 instance, instanceTask 關聯 form 使用 {} ([1390308](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/13903087b245d04c172b11cf068b3dbacd7a3115))

### [3.0.12](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.11...v3.0.12) (2024-07-08)


### Bug Fixes

* 調整指定簽核人員方式，防止多個 company 有相同的部門代碼 ([1961f3b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1961f3bb41e77548b787cad20af86ff46c4473e5))

### [3.0.11](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.10...v3.0.11) (2024-07-01)


### Features

* 表單新增 deleted 欄位 ([3ed3b75](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3ed3b7539025b1b942b1fc96796960dc1660b67e))

### [3.0.10](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.9...v3.0.10) (2024-06-28)


### Features

* formInstances 搜尋增加 owner userId, userCode, departmentId, departmentCode, companyId ([f06fd6a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f06fd6a1a8c7e1d714746c7c7adaa09ad4239f90))

### [3.0.9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.8...v3.0.9) (2024-06-20)


### Bug Fixes

* 修正再議信件的連結遺失 formCode ([226b7e4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/226b7e4b55a1e4ffe827de50d2945cbf7da3dc3c))
* 修正當自動簽核為多人會簽時，無下一關 undefined.property 錯誤 ([3865d5b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3865d5bb3ea150ae9cbbfa09dc04f65b0675e323))
* 更新 @clinico/clinico-node-framework - 修正 S3 Bucket copy source uri ([d2dac79](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d2dac790ccd74c96e64c20403daa9e83f8699316))

### [3.0.8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.7...v3.0.8) (2024-05-14)


### Features

* 多人員關卡根據 instance.values filterKey 動態更新關卡人員簽核 ([c3f4d43](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c3f4d437037a69af00435206c6cd8088c8937419))

### [3.0.7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.6...v3.0.7) (2024-05-09)


### Features

* 增加 userCode, deptCode 人員簽核的類型 ([46f6831](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/46f6831fba73d15eb649fb32506cd610bcd957d6))

### [3.0.6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.5...v3.0.6) (2024-04-29)


### Features

* formInstance model 增加被參照表單的 id 和狀態 ([1b8ad04](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1b8ad046d4eb778298c647840b81390f5a6b224a))
* 參照表單的 id 和狀態存到 formInstance 表單欄位裡 ([81cd90e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/81cd90e0683af2954e23cba7dba072d1f8a3edf6))
* 增加被參照表單的 id 和狀態到 /form/instances SearchParams ([4c35f81](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4c35f81d127084b1e2ef0e4bd3bfe074ca707084))
* 新增、異動表單資訊時，使用 referenceFormInstanceId 動態更新被參考表單資訊 ([a4c064e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a4c064e30ed991ffd958542f6ddba4f9ce20883d))
* 新增、異動表單資訊時，動態更新被參考表單資訊 ([612ca13](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/612ca13c93c7d26ebcd10bc24169d20593b3fe01))

### [3.0.5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.4...v3.0.5) (2024-04-03)


### Features

* Camunda 'common:task:start' handler 增加自動簽核功能 ([605610c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/605610c4ffbc9219dfe28d55ec9375702f655e1a))
* form instance task usecase 開放 runAutoApprove 給外部使用 ([2e70b1b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2e70b1b81b9940399b9cd48aed7526a070952d59))
* 外部起單 api 增加判定自動核准功能 ([deac6c7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/deac6c7fcb7717005bf389dcd4d731f895926a26))
* 建立流程自動簽核、發信通知使用 Promise.all ([5d012a0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5d012a0f7d8c15bcf7e1f4011de05864fc02968a))
* 拆分自動簽核及跳過信件通知的邏輯 ([f6971a4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f6971a43944cdf966f140316d0e5de86e3ca4323))


### Bug Fixes

* filter allowSkipNotifyMailUsers use Task.userId ([65a2710](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/65a27100f4860b2356d59a72ad4dc237f02f5cca))

### [3.0.4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.3...v3.0.4) (2024-04-03)


### Features

* 使用 property.allowSkipUsers 配置跳關，property.isSkipTaskSkipMail 決定跳關是否發信通知 ([676d8c4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/676d8c4e7d1edcc0a7200359c36636e42cae779f))

### [3.0.3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.2...v3.0.3) (2024-02-20)


### Bug Fixes

* replaceJsonS3Key 移除 RegExp，使用 string replace ([a799edb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a799edbb3c309beb0be0a14e10a7abf35b6e1d50))

### [3.0.2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.1...v3.0.2) (2024-02-07)


### Features

* company search add params regionId ([dc63caa](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/dc63caad51e3369459db15dca1ef241edfa169b4))
* 客製化-簽呈依公司判斷是否有負責人關卡 ([37386d2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/37386d29fe9d1c9f29bd55dd87d40efcd829428f))

### [3.0.1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v3.0.0...v3.0.1) (2024-02-02)


### Bug Fixes

* genFormInstanceS3Files formInstance.values update attachmentS3Keys CurrentFolder ([99ea0b3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/99ea0b32947de3c6ac0cb058ca342fa6f68abb29))

## [3.0.0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v2.1.2...v3.0.0) (2024-01-29)


### Features

* 🎸 報價單簽核流程調整-移除從 bpm gateway 設定 ([17844b6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/17844b60cfb5522a17ce7bdc898ff399e5af1e1b)), closes [#97086](https://asking.clinico.com.tw/issues/97086)
* 🎸 選項為變更的合約類別，自動產生原合約編號的延伸編號 ([5c86132](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5c861329f011323eb3aa26efa6e2dcf0e5331d10)), closes [#95401](https://asking.clinico.com.tw/issues/95401) [#95401](https://asking.clinico.com.tw/issues/95401)
* add customizes autoInstanceCode ([5a55946](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5a55946ec712d6c1fda13e9a75298b8311a8c5a7))
* add CustomizeUseCase ([47976fc](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/47976fcf4634c34ffb0e64c39b06ce21ddc03887))
* add ValueUploadFileInfo type ([cd8aaba](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cd8aabafda6e5e522a6b91be2b991d82c77244f6))
* attachments model add formInstanceMessageId, source, uploadUserId ([116dd60](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/116dd6078b475c8b80de2c68599e46a82e900f01))
* attachments model add groupBy ([62755c0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/62755c00c1a469c99daa47a6aac4b7db1687b777))
* AttachmentService create deprecated, use updateOrCreate ([eb0ba11](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/eb0ba1101d3e18eafeb919e2496620bae43bc10d))
* currentTask service add genCurrentTaskAllowAdditionalDocs ([6d65ab9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6d65ab97355525a4621d958d7cc4b4fa45f277d4))
* customize cn_QuotationOfficialSeal gateway ([604587f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/604587f188339966c792febdf64377cb41ef29a7))
* customize cn_QuotationOfficialSeal set gateway value 1 ([bba45c4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/bba45c444f6cb181f7344b202e4af34a425a672c))
* form instance task svc getCurrentTaskByTasks move to form instance ucase ([01e52b4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/01e52b44493b8abc6b3bcb31a36fe39153c19a7b))
* genValueUploadFileInfo add uploadUserCode, uploadUserName ([aeecbb5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/aeecbb52404578fafd836590f421940651d8a970))
* 取得單一 instance 時， 產生詳細的上傳檔案資訊 ([31435ca](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/31435ca82ffcc70f878bc63fc7d60e06f3ebb2a0))
* 新增留言可以附加上傳檔案功能 ([cdb88f2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cdb88f240e8121187fb4397380de911832312826))
* 現有的檔案上傳功能存取詳細上傳資訊 ([ad3ea44](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ad3ea447a66ddc35ad121255c10738a83a6ed555))


### Bug Fixes

* message upload file move out transaction ([7d528b3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7d528b3c65029a8defe05374f24dd83b10604a97))
* updateVariables assign formInstance.values and appendFormData ([4196dd5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4196dd56675effa1ce349a36934fd6fcb69286d9))
* upload same s3key files error ([d31af1d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d31af1d736b245e5d266bd5d58e71250c48cc3ce))
* user position find gt one rows error ([d2a0bc8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d2a0bc8e6436be89eb71d07aa0c6484834aef024))
* 濰視 companyCode 使用 WT ([38bc87d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/38bc87dfa037f45376a4eff9a2bd8a73ccb585cc))
* 調整取得待簽核清單方式，以及總待簽核的數量顯示 ([ef74f02](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ef74f02fa452a8c66afbaacd88f6a890409bd703))

### [2.1.2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v2.1.1...v2.1.2) (2024-01-10)


### Bug Fixes

* form findOneByCodeOrErrpr 取得完全比對的 formCode ([65de030](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/65de0309c7aeef7a5e0a30efc3ef30ca064ca608))

### [2.1.1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v2.1.0...v2.1.1) (2024-01-05)


### Features

* add allowSkipUsers config assignees property to runAutoApprove ([4c992be](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4c992be6b2374e4828b9ce805a377040e53aade4))
* mail body instance url rm form code toLowerCase ([3128ef1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3128ef1dbf3f887286467d6dea8ba220ff049a56))


### Bug Fixes

* CI_COMMIT_BRANCH is null on created tag ([581def1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/581def1d96bc5d786b98ba1a855ed24bd3712137))
* EEV_UPSTREAM_BRANCH is null for prod ([ab229c3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ab229c33d6ec1b2b7dc339ff1bb8a37e6323a784))
* form taskAssignee run specifyUser to assignee use findOneSpecifyUser ([4b64353](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4b64353c786bb72a579230f9ca2deffaee833845))
* process taskAssignee run getBySpecifyUser use findOneSpecifyUser ([0643ae5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0643ae550feaa1469eb785efad7e9d78199c8b36))

## [2.1.0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v2.0.2...v2.1.0) (2024-01-03)


### Features

* formInstance.value or appendFormData gen multi UserTasks user_str list ([ef37972](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ef37972d8e3927a72faafe54106f7f0dffbd1bc0))
* formSpecifyUsers.length == 0 set variable empty array ([f6a147a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f6a147af9450a85ddc6eb51390e5ec7388513382))
* get specifyUser by instanceValue to task assignee user ([9f77af8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9f77af88f3a46cad0967fb51171c6d8c2775543d))
* set getFormValueTaskSpecifyUsers string array ([e289945](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e289945f33e891608813ae24db492af3a24ef083))
* task Variables add CN_Quotation gateway usecase ([2b90c99](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2b90c99510ece7d3435b8f5ba7518d64627dbf42))
* update genPropertyByPrefixFormSpecifyUser assignee by key first ([0918206](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/091820679522ae7662becd9ab15cb5491bab3e67))
* 中國 ERP / 報價單 - 簽核流程關卡 gateway 條件 ([a5d52bf](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a5d52bfd2bf20630473e0df493e492ab18bb2cd6))


### Bug Fixes

* add assignee CountersignType.Part set variable ([b4716a0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b4716a062351aa8dcd638b44eb9f50996d8dab3e))
* task addAssignee not notify ([5f71d69](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5f71d690e5d05cf33bec6064484f2940d77e2b1c))

### [2.0.2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v2.0.1...v2.0.2) (2023-12-28)


### Features

* remove deploy to k8s config for prod ([a0c4a44](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a0c4a4452f5d377cfcc7e10162565638190b7cb7))

### [2.0.1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/compare/v2.0.0...v2.0.1) (2023-12-27)


### Features

* add send reminder notify ([ea5aad4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ea5aad49999a9fafae0ac91cb9c2d8ca285faaea))

## 2.0.0 (2023-12-22)


### Features

* [#75267](https://asking.clinico.com.tw/issues/75267) 申請文件/合約用印_印信類別新增 電子簽章 選項 ([1f915f0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1f915f02be744149e9aeeed78debb83fcd6d85d1))
* [#75268](https://asking.clinico.com.tw/issues/75268) 組織節點選到一般員工(非主管職，系統目前只有主管職) api ([81f84fa](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/81f84fa4b8241f82b9016dfdb02f36b8f1a54456))
* `直屬主管`與`部門主管`關卡為執行長時，略過關卡 [#60146](https://asking.clinico.com.tw/issues/60146) ([29015a0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/29015a05aefea633ca7e2703e1442012faedb80a))
* add audo deploy ([db2c1b6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/db2c1b6f42bd52158170e5a7c892c914da588a4c))
* add ci rule for manual run pipeline ([42ebc44](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/42ebc4482381ee364227f2db999826b05d4b9a4b))
* add current assigned ([083658a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/083658a5887ec00f68f5380a26fbca0e21af8829))
* add deploy config for cn dev ([a4e3709](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a4e3709496fc52e4fd0fc373563421d3f2757028))
* add enum of applyDocumentSeal ([3ed539a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3ed539a72bfc4e774cdee0f10fb7cfe0b36d5e59))
* add form instances search by jsonbQuery FilterQueryMetadata ([81ec96f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/81ec96f8dfbc2a2d4bb2f79db4281eaf1ce6c679))
* add form jsonb task_settings usecase ([6e577ec](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6e577ec3ac9f2dd835f0b1ed099a134f3805bdab))
* add formInstanceTaskId of formInstanceMessage ([4639e35](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4639e351afcc72cde731a0ebd54e5c917d864137))
* add jsonb column form_value, workaround by parse copy values to form_values ([2439ca3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2439ca3ddbfdcf391bc03d8427743a1398c531ef))
* add jsonbQuery helper ([f8127ca](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f8127cafa23fd2a5968bde8e4e62e52d4b420ac1))
* add processTaskId == Activity_End auto approved process ([c4f63b7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c4f63b7b5c64ac2df545ae6c8b7ef73758413868))
* add proposal flow ([d24c3d2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d24c3d22fe4fed05e93cdaead758c84fd8adbe37))
* add reference for create form instance ([2921511](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/292151199bf0649237094931d22e20c006ec341e))
* add user permission ([302fdee](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/302fdee62f0de5b1631e0241b08f9b7d44796ca8))
* allow skip task ([f3e87cb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f3e87cbde8df29830ed0e115f2d21052c04b8550))
* allowAddAssignee of property ([7c573c1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7c573c1c87018b6331a0dbe01fe338f7f2bd09f1))
* append form data of current task ([d1cb002](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d1cb002e77fc8ac42f8b44dac251219743e4b0fd))
* assignee ExAssignee add assignee name, prefix, update departmentName ([9db569a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9db569acd07700ac387fd366a36a899ed224521f))
* assignee type prefix_form_department_head, prefix_form_specify_user ([a6a8e7d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a6a8e7d1ef1df6927b5de168637cd607f07d489d))
* assignee type task_category_users ([419071c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/419071ccf31dd05b3d6e460d10b7010f365ae92a))
* assigneeType add specifyTaskUser, flatten variables array value ([16cfafe](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/16cfafe4377897378cb09e597c2e43523af51317))
* auto form values usecase, add process ApplyOfficialSeal usecase ([4d797d2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4d797d288ff469ab8e825ba35d21f4293bd1c5bd))
* autoApprovedActivityEnd add notifiTaskPublish after saveFormInstanceTask, rm isSkipTask ([520ee3c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/520ee3c408fe851639723c0e1f591698544249f3))
* autoApprovedActivityEnd set isSkipTask ([8522965](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8522965efa9e69444e96bf9093424f3ca07ae385))
* autoApprovedActivityEnd task rm deleted, task update null user, department ([f87db98](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f87db98fc9e742eb088eaaaba767394dbf91c717))
* camunda handlerEndTask notifyEndTask send ws message ([31511e1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/31511e1c15458be063b521dfbd8916b11acb24da))
* CamundaClientService.handlerEndTask add sendCompletedNotifyToSpecifyUsers ([5c913c8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5c913c83bcef786247dffd2ef18c68929ba6148a))
* change EnumSealCategory: simple, special & add EnumSealType: pagingSeal, stampSeal ([84d05f8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/84d05f88c72f0c29c593d4b6f0251af1846ee091))
* counting additional assignee ([61b8abe](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/61b8abee7e9334224ba45aeef17e7b58ece4f0e4))
* counting approved and reject for countersigners ([6446548](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6446548e7737a76151cabe3eb23d41555f5e01fa))
* definition tasks ([5cfacff](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5cfacffe0b6ca8465207b011fe9f73eb50345101))
* department specify users ([7b3c1fe](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7b3c1febc76fb48b196e048799c99191bea6f5ad))
* description of internal api ([33a0972](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/33a09720167275d3e4de143cb3a9144f020ea619))
* email notification subject add form instance id ([499656e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/499656e4e299a0e3aa495e7be91f713aac6f807c))
* email notify ([39ae9b9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/39ae9b99f2db494d8bd078ed233e8f9f3c228479))
* email 通知關卡補件的簽核人員 ([def2a0e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/def2a0ed4c47efafebfc5a638e291eaaea4e5b32))
* end userTask use Activity_Finished, autoApprovedActivityEnd set isSkipTask ([2df0f90](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2df0f90c00014268689e5bc73203011bd77d8a8c))
* external assignees add grouping ([20a55c9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/20a55c93ee5c9da336b378f30a6ec554e68f582c))
* external assignees from form_assignees ([50e0ad9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/50e0ad96e4f7ec185785cbf99e272ab79c966adc))
* form instance task add additional documents api ([4b8f9fa](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4b8f9faea6af154eb08e724563792b5eff4ea777))
* form instance task allowRead add MailToSpecifyUsers ([a3481ff](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a3481ff98afb342701d87dd0edf02cc294eb914e))
* form instance task usecase function approve, reject use autoApprovedActivityFinished ([cd139db](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cd139db26e96fb17f06cc2d9c421ff019d9e4ba1))
* form task assignee usecase add getUserInfoByUserCode ([0aab831](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0aab83171bf69834ef12763b6c763d8cf5d430c7))
* form_settings jsonb add formFields ([798e64e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/798e64eac36298b4e072c1f6c145b8c00b8bfeee))
* form_settings jsonb update formFields type ([59b4ee6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/59b4ee6d156176fcdd1deb964569c4fd06bd6b66))
* form_settings jsonb update mailToSpecifyUsers type ([bdc33a6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/bdc33a6fb872dd0e3e154ced237681e9e99917a0))
* formInstanceTask params.values update form instance values ([6fc609b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6fc609b74a419aa7e3fda1ed053b361c7b5094cf))
* forms add form_settings field, currentTask use formFieldStatus ([d6c9d39](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d6c9d3954846c41786a8b8a7c945c7be2af851b5))
* formValueFilter get empty ids return empty results ([f38440c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f38440c98d635096a7a83c796a4fe2fda642198b))
* generate instance code ([a1e4ee6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a1e4ee652e954bd270df63da35fcc4b1ad31256a))
* genFormInstanceCode add prefix by owner company ([8bd6e7e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8bd6e7ef7246ddfe05e16e3ff29c9962938536f9))
* get attachment url by keys ([e908552](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e90855295b6546702e7566cbe5319e7d7222cff0))
* get nested variables, form values path keys value ([8c137bb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8c137bb4e20f97c777e5db25a6cebb727ca6f45c))
* handlerEndTask add delay 3000 ms ([d4b58aa](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d4b58aa22608db26868e91f9795764866f7c0c6b))
* handlerEndTask rm delay, instanceTaskUpdateFormData before save formInstance set current instance's status, finishDate ([2c82ae2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2c82ae2bafdd5d9d46337122771484da08b2e0e8))
* initial variables add create no category taskCategoryUsers ([4add737](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4add7379db3c580771dad3f25a118f8b242f5349))
* internal api for create instance ([64d4ff4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/64d4ff4bcc993b77868d85d58aaabc431b698191))
* menu badges ([8e41d74](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8e41d7489f08b5424249149174dfdabd5d9382b9))
* menu badges controller ([b78d7b5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b78d7b5381abca5a2f8bba6ed32d49b34fe05bf3))
* message of instance ([080da1b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/080da1bf71b144434162985a28b07a7c12f1b687))
* modify ci config for devops script ([30f8463](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/30f8463a5546b8d0a746fbd35572c38b60ea36f3))
* process instance task use formTaskConfigUCase.setProperties ([ddf4724](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ddf472493bb9224a03d6af6b515a7f5d6228e45b))
* reconsider task ([f685f17](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f685f173a20af1f523c3a1892dd9bbf097f88d09))
* reminder ([281b9e9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/281b9e93cd30aa32b2dde8ea30cdaf876b36953a))
* return task ([a327b60](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a327b600fcb8adadd55056a9e4e4c512c7689924))
* runNextFormInstanceTasks add autoFormValuesUCase.autoGenFormInstanceValues after create instance task ([5155206](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5155206e65cd655c7033fc58d988cd4dbfe84ff4))
* save attachment s3keys to db ([303ed45](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/303ed45b9f5ddff8c14fe275063e19fda34ec072))
* search all presidents and add depth filter ([e43d51f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e43d51fb4c00ef91bd5a3f7760355de9238c3422))
* search all specify users ([59bd4cb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/59bd4cba4d60ba9c7a2eec26dcfcd5a6328ae7a4))
* searchSupervisorsByDeptCodes 取消略過組級部門 ([e44d05b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e44d05be6e5f06b2393279f47e8d4c2124ae9ba8))
* sendNotifyToSpecifyUsers use formTaskAssigneeInfo feature ([b52a440](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b52a4407e4e25984416fce2079f95fd118a54b26))
* service auth ([5bbdf25](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5bbdf2535b9cf07fcc46be2a0886e7cb9e13924f))
* set form task mailTo's specifyUser allowRead ([1d81a86](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1d81a863fac585cc1e2bc10ef8d76e5a9ee542a3))
* simulate user ([50f2a7b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/50f2a7b5e623319b2f503fb1c7e622657ba583ac))
* smtp service add sendNotifyToSpecifyUsers ([2cd489e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2cd489e9655c7c65e38728515883f86ed23983f4))
* specify custodian ([89c87d3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/89c87d35ade84cbfd18db9a30e79f2410d53094d))
* SpecifyCategoryUser ([cb70d0d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cb70d0d0336fd28338acb0be37c3f2fa0bca6419))
* specifyTaskUserKey appendFormData 根據 specifyTaskUserKey 將 form data 塞到 SpecifyTaskUser ([3bcadd4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3bcadd428663fe80f1e823ff7bfef313926305f5))
* task add assignee ([7733f39](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7733f3900159d58e2975cd4ce2932ce730c55a5f))
* task only approved ([8a3cd41](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8a3cd41b165ee2af9232b1b37096dc3ab34fdfcb))
* task update params.appendFormData to camunda variables form_data_ ([68a4157](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/68a415738becea4c121265545d42c802f68e7d53))
* task variables add form task settings assignees ([a31ea7f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a31ea7f077a7be848d11a0ff92e1b52ceb0f4209))
* TaskVariableUseCase.getRunCompleteVariables add TaskGatewayVariableUseCase ([e9de516](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e9de5168e7b0dca024ec74d27a2e00a836280d95))
* update camunda variable ([4671d4a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4671d4a98358535da4ad509c7e4e1017991d8675))
* update genExAssigneesByFormAssignees exAssigneeInfo, FormExternalAssigneeUser add suffix ([e349c0f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e349c0f3b94c8524c0fd4d8e133177c437b4b308))
* update message of instance ([1c251c4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1c251c45404cd2dfb4ea2e1acc5cd5a9fac28855))
* 增加 EnumAssigneeType 以外的人員簽核 ([6fc3022](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6fc3022079fb820d968a154c020a7e6de008a3d6))
* 新增關卡簽核類型 specify_task_category_users 功能 ([ebb5a55](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ebb5a559aea38b7175c65d6a7d7bd0622d9f5356))
* 會簽選項排除執行長  [#60839](https://asking.clinico.com.tw/issues/60839) ([e1c5cba](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e1c5cbab1807be2e396036e075d48ccb39b90d09))
* 流程增加 additional_docs_type 參數配置補件人員 ([12e523e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/12e523eb4b45ea72fc98c827c852ae2318f0c03d))
* 簽呈核准通知 ([82520a4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/82520a44b6a4fd1cc2c8e8649989e97c2d5a824f))
* 簽呈核准通知, add logger info ([59aaa97](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/59aaa9735927be9ded6026337a2969dccc7433ac))
* 簽核紀錄隱藏執行長略過的關卡 ([cdcf89e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cdcf89eff7f5790d2e31cbb27201937d9a7242b9))


### Bug Fixes

* .proto ([4b9f249](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4b9f249b741bf1a6e1de88d4984a959e4e803758))
* add allCurrentTasks of instance ([37a583f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/37a583f410b0e06e4ca3f194f499291da6cb956b))
* add allCurrentTasks of searching instance result ([985d5a5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/985d5a5b0319e99dff84098f2393dd2f43cf62f7))
* add attachments of creating instance ([197dc21](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/197dc21dea2c913f0002c21119e77bdb78f3748d))
* add auth for erp api ([5343c77](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5343c772a50bb2bdf160d76f1fa656494093fcb8))
* add class validator ([691b9b6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/691b9b6e21fd763f64d0c367e3d63d6b13170e35))
* add code of form ([840cd86](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/840cd864c9b0b19fa6ec45eed7419e7c94306aca))
* add code of instance ([3c4a972](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3c4a97285ad9914cb7a063e12ae9b0a69d78b15c))
* add CommonSearchPageParams of QueryParams ([4b1f26a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4b1f26aa67e485431c0849c23f6a347a354eed32))
* add deleted of core.user ([8c08d68](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8c08d6839c61cece6265b5a0453b4e0207719daf))
* add departmentId for addAssignee ([e19a683](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e19a683728b9ca2971cc7e3b110ff380be72564a))
* add enableCreating of form ([8cdeb64](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8cdeb64f33bc68462a690da27c6d997208c0fa99))
* add form code filter ([a882e2c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a882e2cf0cd88da77f521eff3bd705b895cba8f0))
* add form of reminder ([e5527b9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e5527b9bd10e70eaa50379fbb5e849271125d9de))
* add i18n code of form ([330b5fd](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/330b5fd3622acad2d94dfc71af3d252f6115a8d1))
* add includeGM filter for supervisors ([4c2348d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4c2348d2000c43bbbcf7490eac9211c7f6cb8c5d))
* add limit and offset of QueryParams ([452b0b8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/452b0b81f980a4ee4e8d5f23296d2b7c266cfc25))
* add onlyGeneralManager filter for supervisors ([a203a59](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a203a59ebe7f97c88f7fa51d9bc8efdd6c265b4c))
* add open api req description ([aafd9fc](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/aafd9fc388e26aaeddd1da6b22dcacad8bbe87f1))
* add owner get form instance currentTask ([0359d64](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0359d64455c1867822fd3728fef668ea0230f3b7))
* add PrefixFormSpecifyUser ([c55a509](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c55a509ee6febd59f3343cc691b71e603e14dd85))
* add processTaskId of task ([81d75b0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/81d75b0991f4c1dfa6c616dfbcf2e592615192b1))
* add read permission of instance ([fd9c879](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/fd9c879ab2962e113c9cc3b5d6217ef31424c405))
* add relations of reminder ([3a04763](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3a04763282497804f1f8e7e274424d56dd6db644))
* add reminders of current task ([da943b5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/da943b5c7b4802ab371b12e41381848e45b9cebd))
* add socket io event ([a08a7bb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a08a7bb3caaf86bba68cd93c11bfbf567dd33282))
* add task group code and complated status of task ([b9f3709](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b9f3709fd042db449adcbdc9135ad76375d6930f))
* add task start lisenter ([e182894](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e182894211f2d19d105959e5bb69b3d939ee1a43))
* addAssignee get instance by task's formInstanceId error ([b91521d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b91521dd9817de6379c34807be081d284c3a2ad5))
* allow select email of user ([3d84c51](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3d84c512aa9840565c7689bedc85c43203df67c3))
* append attachment of formInstanceTaskId ([50f7124](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/50f71243ac22b4d6409c802a23ed6581189ae9db))
* append attachments for approve ([c78ac8b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c78ac8b32bbc198b831b9da14da88bbf38a9521b))
* apply_official_seal.bpmn ([e970882](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e9708829ff41c0506ab6054d68f1196d654738fb))
* approve process use isSkipTask property to notify ([20371eb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/20371eb035d2b6101b62fc013a610cb2978af5b5))
* assignee findAllSupervisorsFromRoot empty dept codes ([b22282f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b22282f02211968aa3833ed3e89d55e6c705eb9d))
* assignee of department supervisor ([724aa35](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/724aa35cbac1ac123b1396da4969b73fcdfa0447))
* assignee of specify user ([0aae77b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0aae77bc45cfb0f9b1ba7129d1a6d82e1b9899c9))
* assignee user type of form data ([f40f304](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f40f304dc70ed6034386c5b7495450bfc714e20c))
* axios handling errors ([31303d2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/31303d2206b63b1f716f9afdad0c0c8a2e845e64))
* bpm url of forminstance ([55ebcb4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/55ebcb43e8fc3164eef0ae3f9507b0fd578a000b))
* bug of UserPosition filter ([5c4c6e9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5c4c6e9d7c0e3817f763b9262aea50d4229627fc))
* bulkMoveDraftToCurrentFolder filter draft/ path ([7609610](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/760961022bfb4ae15109b12983be3d52807c1fc5))
* camunda subscribe task end update formInstance ([d336f5f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d336f5f48842b7e3a377ebeb736dfbb0f2308165))
* camunda xml by process id ([4fcb2c4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4fcb2c4e6a77dc6ec8c86b2dae775a193d91bb04))
* cancel instance and task ([ed5f215](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ed5f2153d7917066812adcdd8877fce9722da9ee))
* change to micro-smtp-api ([88d6302](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/88d6302c90ff8622a7fd094b932ceb543683f171))
* check countersign ([091aa84](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/091aa8449542cabed5a4b56b10c97994523529e3))
* check task property ([d4ce4f1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d4ce4f1ef1ece09afecbc068ea27cb94670c4332))
* clean currentTaskId when reject, cancel and delete ([6e10244](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6e1024444f883ad547d6fb7846e09f2e0d4d549a))
* close instance by emitter from common subscribe ([7eed14f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7eed14f15c07387ce2e707fd5db120b275bd6300))
* controller add middleware ([aec7b74](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/aec7b744805635ac40d0303bdfebedca3a135a4d))
* countersigners of variables ([c24721c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c24721cef74e8ccea975d2fe08bc64305f303a0f))
* countersigns finish status ([c461d05](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c461d05da7a7ddb451406c1386e2f06ce9ef7c59))
* create instance open api examples ([f0024b9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f0024b9d319660a4d4319d22681fb8684c921228))
* create instance params for grpc ([ca6506d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ca6506d605605f2568271025fdab931a2a93b59b))
* create reminder by instance id ([9589700](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/95897002aed160e09b256045ad788c9548e73b2f))
* createUser of formInstanceMessages ([203f53f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/203f53f4bb85f0729cfda2c411d8697da08df555))
* currentAssignedUserid taskFilter status add Reconsidered ([3dfcae5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3dfcae53770825e5ca2926032a587e6152dd5b50))
* currentAssignedUserid taskFilter status add Reconsidered ([26fe245](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/26fe2455b65361cfc75b1d6a482ba9f3cecf19a0))
* currentTask with Waiting and Reconsidered ([d9346ca](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d9346ca40607fc290dd0430716b3935d18772e28))
* currentTaskId set null ([0006884](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0006884e2eaae169c00f044311075c4cb20f2b8a))
* custodian and user simultaneously exist ([cb6ae8b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cb6ae8bf4eb50093ac3b365d5ad46d978bb5abb6))
* default code of instance ([eac358a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/eac358ac187857b8613ea93ba244fe60bb025b06))
* disabled filter ([765ff21](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/765ff211927db9724c2369298cffa1aa5aeae03f))
* display name of camunda user ([1aed6c9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1aed6c9ccd22a8495aeebc5612a0898b65c605b7))
* double publish websocket ([8e3485c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8e3485c3d429a421909e0b172a23ab10f1d3e20c))
* email to create user in dev ([c3f9d97](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c3f9d970d2523e5145e8e70433a617fe069ac579))
* email to create user in dev ([a8c1ba3](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a8c1ba3ef1e66a4007e9d740968b07d74bd25fe1))
* entities ([9146277](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9146277d14693715c400b0d3eb9956e0813c00fc))
* env ([d498a09](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d498a09ded99c95247a49a156806f00f12a5eca7))
* ERESOLVE unable to resolve dependency tree error on npm 8 without --force or --legacy-peer-deps options ([6c23b07](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6c23b0741320e9282c2eddb912c7f9456f63bfa3))
* event of menu badges ([f786410](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f7864106c4f65296e416ce4cb99428dddcd6aca3))
* example of approve open api ([044aefe](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/044aefeea5cb67c00dba6f62d5f9d72fed1565de))
* exclude select email and mobile of user ([3c14a67](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3c14a671b1ec7b26285836f9b09ba6bfcfc0ec4b))
* find enabled user ([dc7567d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/dc7567d6581e1d2ffc5637d5d86cbdf19bbb22ca))
* findOneOrErrorByCode with disabled filter ([0d78a27](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0d78a271e908abceb366c216d25b4a2ff463946f))
* finishDate of instance ([bdad6e6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/bdad6e60d5b8056a6f7ffca051ce39f6f2418996))
* fix formTaskAssignee formSpecifyUser type to veriables ([a7803c0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a7803c0458a8ba9b99520b9a691b0a68075359f5))
* form code format ([64125c5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/64125c54587633baa4663e106c6876d70374f103))
* form instance task ucase runCompleteCamundaProcess add attachmentS3Keys ([3b43c5d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3b43c5d1d7f80d81c676f3ef80d8e35eb8442c00))
* form relation formInstances ([152545f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/152545fba0b0b0de3b4697398bd2ef5afe871d35))
* format of form instance code ([cc4acae](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cc4acae5ec07cceaeea21e90a2638ef525a476ed))
* formInstanceTasks of formInstance ([9cfa904](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9cfa904fc76c7c736b06a2ace1d94c6fb0e0aec7))
* fuzzy search instance by code ([12bd624](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/12bd62414e21e270cf506802d397db599dadd446))
* gen user by form_data_generalManager ([885058e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/885058e876615e4000e17254c0e24e1bfa5f0bbe))
* genAppendUploadFilesToFormInstance initial instanceValues.uploadFiles array ([0f8f875](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0f8f875efa1e28134b9788d002ec475dbda9729c))
* general manager result ([fa483e4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/fa483e46cc60d3c7522051f4d53e2a218a1f0a24))
* genPropertyByPrefixFormSpecifyUser legacy hotcode form_data_supervisor user object type ([28619bd](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/28619bde550a42446a104a41981c2d3da3f1cbd0))
* genVariables return by null object ([ee6840e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ee6840e7467ee75b5a6ba6e1160600992e254119))
* GM000 dept_code ([47238d1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/47238d1abc04f9ea78978c4a6c66a497cdb13e5e))
* grpc add order code for create instance ([9570aaf](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9570aaf574e5ab64e67ab41d977217519dee01c0))
* grpc of createQuotationCN ([b352d7b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b352d7bb35de1af918c5b7616ac5efc6b6e1eb88))
* grpc response ([203b4d0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/203b4d06777eabb14744a311cfb37914343391fb))
* hook by event ([336cfef](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/336cfef70715ab6e632bbd8cb5ecd584aa199feb))
* hook event ([0a80251](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/0a802511a7089c144020122de86379817d65d3ca))
* initial variables of form data type ([afb6978](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/afb6978b7eb3ad36e3724897d78f81a2af545170))
* instance proto ([a581f99](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a581f9915e13e81519c7f6cabe07b27ef40a8256))
* link url for mail ([defbb7b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/defbb7b6a426cf1cc7274cfffdbfbe68f743e608))
* link url of mail ([4021683](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4021683c9fb05316f31fede48787bbcfa928930f))
* mail addressee for dev ([ea7bcab](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ea7bcabd0a3d8f16332db07d89b0170093b2b595))
* mail for message ([1a93f10](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1a93f1034040158108097cb746d5f7cc81a9a124))
* mail subject add [test] for dev ([b3b9cea](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b3b9cea96a8c100a0235694610f104eb9d387fa2))
* mail url form code ([1cf2c05](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1cf2c05836ecc1b975127e507e19ef6d9928adcd))
* mail user ([adbef4a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/adbef4adc16050426a7c2772f340606d69dae8bf))
* me result ([b98cc91](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/b98cc912e8ab5cb315a53d6e157564d50fedf56e))
* message include formInstanceTask ([291d87f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/291d87f4d03f047b68122a85b61ac87724769cae))
* next task email notify ([653af9e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/653af9e9be70e59f2a1dc98dcd280084d118a025))
* next task email notify ([84e762f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/84e762f72231d9a729a668c7eb43c62b7a798833))
* onlyApproved of allCurrentTasks ([eda92cc](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/eda92ccfd622b5635a6c33fd7ddeac3e99571501))
* overwrite current task ([5a71ffb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5a71ffb8f2ffaeae973139a565c92c80dd8e396e))
* ownerUserId of internal form instance ([22f2142](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/22f2142ee962aa8bb291cb4d54f612c0375c1465))
* PrefixFormSpecifyUser ([930617f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/930617fbe8c75ea2ec7d3d6c42acc2f71be979db))
* process task assigneee type default use initiator ([96f96bf](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/96f96bf940023c9672f4b0a242df4591df2cec1d))
* processTaskAssigneeUCase getTaskAssignee params property ([708368b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/708368bbb1394b4b28f47651a9e5bb916cee967a))
* property users ([3c419b6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3c419b632fb2ca3dc31c770e05cbee3b95e7ab41))
* property.user conflict currentTask.user ([a711292](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a711292297f9e57334036da5796c370e2700d0e0))
* publish ws when task closed ([3ec0f35](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3ec0f3576f50cd517c0e35326f5afe610f1dc960))
* query UserPosition by userCode ([3d53aa4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3d53aa4dda450edcaa277fedfc4883899681d9ff))
* Quotation_CN.bpmn ([cb19760](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cb19760cc087d6d04392c51986a4094fea7a68e2))
* raw query for department specify users ([2b80dde](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2b80dde4aa922593fa1f7a3f0e367258bb1c0c2d))
* reconsider of instance ([19dfaf5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/19dfaf5ce5c02ba9bb77f4aead66bc9bd4eaea31))
* relations company, department and user of tasks ([f4dbd7c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f4dbd7ccf1c5c9d4e56eac8cea84d01176fd958f))
* relations users ([4774960](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/477496072e2de0f69a94dedb735db2cf7eb63460))
* reminder open api description ([16ebf67](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/16ebf6760557e998ac05cbb326f0ca50309cff9d))
* reminder order by id ([159aca8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/159aca88ea53b5e762d029ea29e862e7bb422af7))
* remove assignees of root for query supervisor ([abd40e2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/abd40e277c26ba9bc4eff71345b882d2c34639e8))
* rename notification ([fc21911](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/fc2191188f47c8e19f6734f6a06a9ee4239f16fc))
* replaceS3Key of instance values ([5f3db74](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5f3db74790315ccc8f47b6dce164443703bed684))
* reset counting when countersigners reject ([2ca93c4](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2ca93c4154bf8aef4929cf333a483482f1399338))
* response of grpc ([3626b30](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3626b3076b5a36089cfc3b08c0c003a964a2237c))
* response of internal instance ([fb10a40](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/fb10a40bd2922edcdfe1bc0b5260dc188eac8509))
* return task by processTaskId ([18f65d8](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/18f65d8ca801d2f67b269e45fe55b0e50f274d1f))
* rm @clinico/typeorm-persistence ([d28043e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d28043e2b7256e718e8349bf34c5061e6a8da82d))
* rm currentTaskReminders ([503f764](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/503f76435743d84c85d58148cdf2c5fa97c5a71f))
* rm search instance of grpc ([374efee](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/374efee9f497a113858657c57a2e1701aeb05a17))
* rollback transaction ([8efdcce](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8efdcce7150b4a20f512fe21f500046e02239c1f))
* run finally process runNextFormInstanceTasks empty tasks ([8da7060](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8da706059c3c518d2b92a1373eb5c2bdcac43405))
* runNextFormInstanceTasks empty tasks nextIsMultiInstance set false ([1432c49](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1432c490182cf82a20365caf4e5bc61d0c29b9d5))
* search by self instances ([268e48c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/268e48c23729231470009debf205e14ce1821681))
* search form instance router ([9d81018](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9d8101897293d83bd086dcbd090b3ca7bbda8732))
* search instaces by task user ([686ffa9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/686ffa9aec3088e0de1093484d3d3e6e5cdff7b4))
* search order by id ([26fc40d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/26fc40d1f68f607fe15a0628e302275b9c5fe666))
* search specify user with regionId ([64825d5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/64825d5c707786468a14f9b1af2d7511d4b27ac3))
* searchUsersByDeptCodes 移除排除人員、節點 & group by code 排除重複 ([3744ce6](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3744ce673e6f05e700cd6a5a75896054bda5fa46))
* send and receive message length of grpc ([4458373](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4458373dfe7b0d0a06078a009cf60b7e23f28884))
* send notify when user disabled ([bb2e02c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/bb2e02c37145ebdc3f7276f48b2e93f16406beb1))
* sendAdditionalDocsNotify filter by current task processTaskId ([f9bcf1d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f9bcf1d23625c71973ca2727b1a3d5cebc4cf0b1))
* SENTRY_DSN ([a150991](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a150991c7593818398c020cca8ea316be6b82376))
* set axios error to sentry ([dd08c00](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/dd08c00cfd7c4c0620f49f9d04d08bcfa36bacfa))
* skip BOARD and GM000 depts of direct supervisors ([a9b2f67](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a9b2f67e35798ea6301d863314c3fdad0784f012))
* skip BOARD and GM000 depts of supervisors ([6ff6f0a](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6ff6f0a07e1aba064e6cd1a19d3508dc664f066b))
* socket auth ([9285e5d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9285e5d9d9a825717428d76b554fa39d758d5659))
* socket disconnect console ([3980d73](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3980d731febccfa82414d9d183bd091967f19654))
* sort formInstanceTasks ([97d7dc9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/97d7dc97d5da4894da0c5609779b72faecfbf6b8))
* sort messages ([6e05031](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6e0503194100658b613a2d7248ae521530390dc5))
* sorting with foreign key in wrong result ([ac38d94](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ac38d943af0615fcdfe4d5328cad47e4aff51c45))
* specify_activity_user assignee type empty assignee ([78569bc](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/78569bc0bff1916f47e65bcb4c1bcfcac3a31e4a))
* suffix `_TEMP` for `Institute` form code ([692bd12](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/692bd12b3d27a465c624bbd2fa6bf726ae9a7719))
* super user 可拿到全部 instance tasks ([6e7ffc5](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/6e7ffc5eb92328113acc53029d84a82307d5719f))
* superuser findOne form instance's currentTask by userPayload.id, superuser allowRead ([5c68e7e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5c68e7edca46859efceb8ad0332e9abedec40340))
* superuser get form instance empty formInstance.values ([f93adeb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f93adebe85df96bf6648183a26107ac971b37841))
* supervisor of variables ([fe54929](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/fe5492937bca1cb0563845f3230de862e2003a89))
* task completed email notify ([a60b0eb](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/a60b0eb879fe5b1cdda0e4ba7d0831b094fa5d0a))
* task property usecase getTaskProperty setProperty order ([93d09ae](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/93d09aec7c29e1b94da2f50f3c7368f23b898ab9))
* taskAssigneeUseCase DirectSupervisor use findOneOwnerDirectSupervisor ([01b17fc](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/01b17fce613b6775e853f91db62705f9385956f4))
* transaction of queryRunner ([30408be](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/30408bef78289b6ac62feb2dd4d491976115f17d))
* try catch updateTaskStatus by emitter ([3683b54](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/3683b54d15a55ab3cdc496b2ee459031afd75230))
* typo ([c29ae60](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c29ae60f663d41460b83423f8c477447095a7c42))
* typos ([1394fe1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1394fe170c21e9d54019db8b37660406e2cb4eb3))
* typos ([4659721](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/46597210120559474817f6ba0df834cb786f9831))
* typos ([493854e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/493854e80fb0fc9491f2cc8cc48d7b3188ac707f))
* typos ([151bd44](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/151bd44e1e7158eead9e1dc5a67a64058ca3bd76))
* update append data to instance values ([1a75e3c](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1a75e3ca47a52697eb0c541f81d3f4545f8b57e5))
* update bpmInstanceId of erp quotation order ([bea58ef](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/bea58efd3ad1fb23ed65f78f298b9452c019f013))
* update currentTaskId ([383cab9](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/383cab9236413ced8710a9953b6a1941c82f3ef9))
* update formInstance ([5db0a6b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5db0a6b83dbb7385b4505fb3de0c0b6ca1778354))
* update formInstance for camunda client ([4ca8e29](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/4ca8e295c842465aa2cc5bd2067d10c110d6c862))
* update instance values by task update params.appendFormData ([ffd75d7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/ffd75d70c07f551215e931017638645049f9fde8))
* update status of quotation order ([1b401da](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/1b401daff18393cd90223d8a5df6fa209639c580))
* update status to erp ([5fe59c0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5fe59c0beb84ad79dde63f8d8b39cc263d95bc36))
* update task updateUserId ([8566b42](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8566b4226910cf467bf36591f89f9ccf2423c691))
* use socket.io ([655daca](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/655daca0534de2fb44fe78ea21434f245042e576))
* user of currentTask ([e7aaf4f](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e7aaf4f52f550b7c5e980419f5d84e01337c7226))
* userPosition disabled ([d2721e1](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d2721e105b9d774b089908a9cabdf7a2e5a018ab))
* v_department_users ([890f6e0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/890f6e0eb4dbe00d77f494fc984e269c2de9335f))
* validate reference instance ([f3c3b6b](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/f3c3b6b60876f9283d814d5a866fd942ed334285))
* web socket for menu badge ([5ac75a7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/5ac75a777a2143c0f353be453fd2f2bfccc38e39))
* 人員附件上傳 S3 暫存路徑移到 process_instance_id 底下 ([db79103](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/db7910342a6568560ad92c4607739e69164ffc6f))
* 修改流程 [#55349](https://asking.clinico.com.tw/issues/55349) ([d7d8b55](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/d7d8b55de415d26076795738fe38c0cdcad18353))
* 修改流程 [#55350](https://asking.clinico.com.tw/issues/55350) ([db404f7](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/db404f713cab8693127e59ddf68db47f301d70be))
* 加簽不是主要部門的主管 ([2282a7d](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/2282a7d12d47bcf79968c24d88e7011545a01c2e))
* 報價流程 ([91f4537](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/91f45379da3e06e84e207e722a9d51a323aa06e2))
* 已完成的簽核單內，有任務有再議狀態時，會出現在待處理 ([8c095c0](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/8c095c063665550f73de9c18fccc3a0d46ce94e7))
* 新增 `事業群主管` ([356703e](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/356703e7082255d38f51b0111eb68b507ec19e6a))
* 新增 `管理代表` ([9e8f315](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/9e8f3156db707d3d1c85c87e73fb67077f82c67e))
* 會簽選項排除執行長 ([7d98b18](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7d98b18c6b1bccd8e52257e6f348d75b583586c8))
* 會簽選項排除清單 ([e388b32](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/e388b32c094725c5a6a8e8d29a909f53d5d46fad))
* 會簽駁回立即結束關卡 ([887c8aa](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/887c8aa5af94a64a8b62adadec8c16f21c4ec1e3))
* 耳科事業群`N000`部門主管例外處理 ([c158e64](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/c158e6430a77098433eccd805de79eba8dd6c1aa))
* 部門主管清單正常顯示 ([7e0ec06](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/7e0ec069f7170fe5210b173ebf5876c35f4019d0))
* 部門主管顯示總經理級 ([cdb6b77](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/cdb6b7743f69d16090d8880024d5109e07cc66e5))
* 駁回後，流程回到起單人確認 ([33582b2](https://gitlab.aws.clinico.com.tw/clinico/workflow/clinico-bpm-api/commit/33582b2a81ce8d88f6ba861c0f7490ec511fde15))
