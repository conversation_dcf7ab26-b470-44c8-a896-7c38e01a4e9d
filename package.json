{"name": "clinico-bpm-api", "version": "3.3.1", "description": "", "main": "index.js", "scripts": {"clean": "npx rimraf ./dist", "build": "npm run clean && npx tsc && cp .env* ./dist", "start:dev": "NODE_ENV=local npx tsc-watch -p tsconfig.json --onSuccess \"node dist/app.js\"", "format": "npx prettier --write \"{src,test}/**/*.ts\"", "lint": "npx eslint \"{src,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "prepare": "npx husky install", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "_moduleAliases": {"@": "./dist"}, "lint-staged": {"*.ts": ["npx eslint --fix"]}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@swc/core": "^1.3.99", "@swc/helpers": "^0.5.3", "@types/camunda-external-task-client-js": "^1.3.6", "@types/dotenv-flow": "^3.3.3", "@types/jest": "^29.5.12", "@types/kcors": "^2.2.8", "@types/koa": "^2.13.12", "@types/koa-bodyparser": "^4.3.12", "@types/koa-logger": "^3.1.5", "@types/koa-router": "^7.4.8", "@types/lodash": "^4.14.202", "@types/redis": "^4.0.11", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.1.0", "prettier": "^3.1.0", "regenerator-runtime": "^0.14.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.4", "ts-node": "^10.9.1", "ts-protoc-gen": "^0.15.0", "tsc-watch": "^6.0.4", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2", "vitepress": "^1.6.3"}, "dependencies": {"@clinico/base-error": "^0.0.5", "@clinico/clinico-node-framework": "^1.12.39", "@grpc/grpc-js": "^1.9.11", "@grpc/proto-loader": "^0.7.10", "@sentry/node": "^7.81.1", "apollo-server-core": "^3.13.0", "apollo-server-koa": "^3.13.0", "axios": "^1.6.2", "camunda-external-task-client-js": "^2.2.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "class-validator-jsonschema": "^5.0.0", "dotenv-flow": "^4.0.1", "fs-capacitor": "^8.0.0", "graphql": "^15.8.0", "graphql-type-json": "^0.3.2", "http-errors": "^2.0.0", "http-status": "^1.7.3", "kcors": "^2.2.2", "knex": "^3.0.1", "koa": "^2.14.2", "koa-bodyparser": "^4.4.1", "koa-logger": "^3.2.1", "koa-multer": "^1.0.2", "koa-router": "^12.0.1", "koa2-swagger-ui": "^5.10.0", "lodash": "^4.17.21", "mali": "^0.47.1", "module-alias": "^2.2.3", "moment": "^2.29.4", "pg": "^8.11.3", "redis": "^4.6.11", "reflect-metadata": "^0.1.13", "routing-controllers": "^0.10.4", "routing-controllers-openapi": "^4.0.0", "socket.io": "^4.7.2", "type-graphql": "^1.1.1", "typedi": "^0.10.0", "typeorm": "^0.3.17", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2"}}